import request from '@/config/axios'

// 合同变更 VO
export interface ContractChangeVO {
  id: number // 主键
  contractId: number // 合同ID
  originalContractCode: string // 原合同编码
  originalContractName: string // 原合同名称
  originalContractAmount: string // 原合同金额
  modifiedContractCode: string // 变更后合同编码
  modifiedContractName: string // 变更后合同名称
  modifiedContractAmount: string // 变更后合同金额
  remark: string // 合同说明
  result: number // 审核状态
  userId: number // 申请人的用户编号
  processInstanceId: string // 流程实例的编号
}

// 合同变更 API
export const ContractChangeApi = {
  // 查询合同变更分页
  getContractChangePage: async (params: any) => {
    return await request.get({ url: `/bpm/contract-change/page`, params })
  },

  // 查询合同变更详情
  getContractChange: async (id: number) => {
    return await request.get({ url: `/bpm/contract-change/get?id=` + id })
  },

  // 新增合同变更
  createContractChange: async (data: ContractChangeVO) => {
    return await request.post({ url: `/bpm/contract-change/create`, data })
  },

  // 修改合同变更
  updateContractChange: async (data: ContractChangeVO) => {
    return await request.put({ url: `/bpm/contract-change/update`, data })
  },

  // 删除合同变更
  deleteContractChange: async (id: number) => {
    return await request.delete({ url: `/bpm/contract-change/delete?id=` + id })
  },

  // 导出合同变更 Excel
  exportContractChange: async (params) => {
    return await request.download({ url: `/bpm/contract-change/export-excel`, params })
  },
}
