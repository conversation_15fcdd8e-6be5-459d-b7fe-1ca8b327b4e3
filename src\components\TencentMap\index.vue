<template>
  <el-dialog
    title="查看打卡轨迹回放"
    v-model="dialogVisible"
    width="1200px"
    append-to-body
    center
    @close="handleClose"
  >
    <div class="map-container">
      <div id="tencent-map-container" class="map-box"></div>
      <div class="route-info">
        <div class="info-header">
          <h4>打卡轨迹信息</h4>
          <span class="total-count">共 {{ punchLocations.length }} 个位置</span>
        </div>
        <div class="route-summary" v-if="routeInfo">
          <div class="summary-item">
            <span class="label">总距离：</span>
            <span class="value">{{ formatDistance(routeInfo.totalDistance) }}</span>
          </div>
          <div class="summary-item">
            <span class="label">总时长：</span>
            <span class="value">{{ formatDuration(routeInfo.totalDuration) }}</span>
          </div>
          <div class="summary-item">
            <span class="label">轨迹点：</span>
            <span class="value">{{ routeInfo.pointCount }} 个</span>
          </div>
        </div>
        <div class="location-list">
          <div
            v-for="(location, index) in punchLocations"
            :key="index"
            class="location-item"
            @click="focusLocation(location)"
          >
            <div class="location-index">{{ index + 1 }}</div>
            <div class="location-details">
              <div class="location-name">{{ location.customerName || '未知客户' }}</div>
              <div class="location-address">{{ location.punchLocation || '未知地址' }}</div>
              <div class="location-time">{{ location.punchTime || '未知时间' }}</div>
              <div class="location-coords">{{ location.latitude }}, {{ location.longitude }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const MAP_KEY = 'R6WBZ-FZQ34-NRDUU-KROLW-3ST6Q-RKFC5'

const dialogVisible = ref(false)
const punchLocations = ref([])
const routeInfo = ref(null)
const map = ref(null)
const markers = ref([])
const polyline = ref(null)

// 初始化地图
const initMap = (locations) => {
  if (window.TMap) {
    createMap(locations)
  } else {
    const script = document.createElement('script')
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${MAP_KEY}`

    script.onload = () => {
      setTimeout(() => {
        if (window.TMap) {
          createMap(locations)
        } else {
          fallbackToStaticMap(locations)
        }
      }, 2000)
    }

    script.onerror = () => {
      fallbackToStaticMap(locations)
    }

    document.head.appendChild(script)
  }
}

// 创建地图和标记
const createMap = (locations) => {
  try {
    if (!locations || locations.length === 0) return

    const validLocations = locations.filter((loc) => loc.latitude && loc.longitude)
    if (validLocations.length === 0) return

    const sortedLocations = [...validLocations].sort((a, b) => {
      const timeA = new Date(a.punchTime).getTime()
      const timeB = new Date(b.punchTime).getTime()
      return timeA - timeB
    })

    const firstLocation = sortedLocations[0]

    map.value = new window.TMap.Map('tencent-map-container', {
      center: [firstLocation.longitude, firstLocation.latitude],
      zoom: 15,
      viewMode: '2D'
    })

    createMarkers(sortedLocations)
    focusLocation(firstLocation)
  } catch (error) {
    fallbackToStaticMap(locations)
  }
}

// 创建标记和轨迹回放
const createMarkers = (locations) => {
  try {
    markers.value = []

    const path = locations.map(
      (location) => new window.TMap.LatLng(location.latitude, location.longitude)
    )

    const startMarker = new window.TMap.MultiMarker({
      map: map.value,
      styles: {
        start: new window.TMap.MarkerStyle({
          width: 30,
          height: 30,
          anchor: { x: 15, y: 15 },
          src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png'
        }),
        car: new window.TMap.MarkerStyle({
          width: 40,
          height: 40,
          anchor: { x: 20, y: 20 },
          faceTo: 'map',
          src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png'
        })
      },
      geometries: [
        {
          id: 'start',
          styleId: 'start',
          position: path[0]
        },
        {
          id: 'car',
          styleId: 'car',
          position: path[0]
        }
      ]
    })

    if (path.length > 1) {
      const endMarker = new window.TMap.MultiMarker({
        map: map.value,
        styles: {
          end: new window.TMap.MarkerStyle({
            width: 30,
            height: 30,
            anchor: { x: 15, y: 15 },
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png'
          })
        },
        geometries: [
          {
            id: 'end',
            styleId: 'end',
            position: path[path.length - 1]
          }
        ]
      })

      polyline.value = new window.TMap.MultiPolyline({
        map: map.value,
        styles: {
          route: new window.TMap.PolylineStyle({
            color: '#409EFF',
            width: 4,
            borderColor: '#fff',
            borderWidth: 1
          })
        },
        geometries: [
          {
            id: 'route',
            styleId: 'route',
            paths: path
          }
        ]
      })

      markers.value.push(startMarker, endMarker)
    } else {
      markers.value.push(startMarker)
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 聚焦到指定位置
const focusLocation = (location) => {
  if (!map.value || !location.latitude || !location.longitude) return

  map.value.setCenter({ lat: location.latitude, lng: location.longitude })
  map.value.setZoom(15)
}

// 备用方案：显示位置信息
const fallbackToStaticMap = (locations) => {
  if (!locations || locations.length === 0) return

  const validLocations = locations.filter((loc) => loc.latitude && loc.longitude)
  if (validLocations.length === 0) return

  const mapContainer = document.getElementById('tencent-map-container')
  if (mapContainer) {
    const locationList = validLocations
      .map(
        (loc, index) => `
      <div style="padding: 10px; margin: 5px; background: #f5f5f5; border-radius: 5px;">
        <div style="font-weight: bold;">位置 ${index + 1}</div>
        <div>客户：${loc.customerName || '未知'}</div>
        <div>地址：${loc.punchLocation || '未知'}</div>
        <div>时间：${loc.punchTime || '未知'}</div>
        <div>坐标：${loc.latitude}, ${loc.longitude}</div>
      </div>
    `
      )
      .join('')

    mapContainer.innerHTML = `
      <div style="padding: 20px; height: 100%; overflow-y: auto;">
        <h3 style="margin-bottom: 15px;">打卡位置信息（共 ${validLocations.length} 个位置）</h3>
        ${locationList}
      </div>
    `
  }
}

// 打开弹窗
const openDialog = (locations) => {
  punchLocations.value = locations
  dialogVisible.value = true
  nextTick(() => {
    initMap(locations)
  })
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  if (map.value) {
    map.value.destroy()
    map.value = null
  }
  markers.value.forEach((marker) => {
    if (marker) {
      marker.setMap(null)
    }
  })
  markers.value = []
  if (polyline.value) {
    polyline.value.setMap(null)
    polyline.value = null
  }
  punchLocations.value = []
  routeInfo.value = null
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  height: 600px;

  .map-box {
    width: 100%;
    height: 100%;
  }

  .route-info {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    width: 320px;
    max-height: 500px;
    overflow-y: auto;

    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;

      h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
      }

      .total-count {
        color: #666;
        font-size: 12px;
      }
    }

    .route-summary {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;

      .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 12px;
        }

        .value {
          color: #409eff;
          font-weight: bold;
          font-size: 12px;
        }
      }
    }

    .location-list {
      .location-item {
        display: flex;
        align-items: flex-start;
        padding: 10px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: #e9ecef;
        }

        .location-index {
          width: 24px;
          height: 24px;
          background: #409eff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          margin-right: 10px;
          flex-shrink: 0;
        }

        .location-details {
          flex: 1;
          min-width: 0;

          .location-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
            font-size: 14px;
          }

          .location-address {
            color: #666;
            font-size: 12px;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .location-time {
            color: #999;
            font-size: 11px;
          }

          .location-coords {
            color: #666;
            font-size: 10px;
            font-family: monospace;
            margin-top: 2px;
          }
        }
      }
    }
  }
}
</style>
