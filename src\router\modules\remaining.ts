import { Layout } from '@/utils/routerHelper'

const { t } = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/index',
    name: 'Home',
    meta: {},
    children: [
      {
        path: 'index',
        component: () => import('@/views/Home/Index.vue'),
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    name: 'UserInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'profile',
        component: () => import('@/views/Profile/Index.vue'),
        name: 'Profile',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: t('common.profile')
        }
      },
      {
        path: 'notify-message',
        component: () => import('@/views/system/notify/my/index.vue'),
        name: 'MyNotifyMessage',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '我的站内信'
        }
      },
      // 任务详情
      {
        path: 'taskDetail',
        component: () => import('@/views/Home/taskDetail.vue'),
        name: 'TaskDetail',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          title: '我的任务'
        }
      }
    ]
  },

  {
    path: '/dict',
    component: Layout,
    name: 'dict',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'type/data/:dictType',
        component: () => import('@/views/system/dict/data/index.vue'),
        name: 'SystemDictData',
        meta: {
          title: '字典数据',
          noCache: true,
          hidden: true,
          canTo: true,
          icon: '',
          activeMenu: '/system/dict'
        }
      }
    ]
  },

  {
    path: '/codegen',
    component: Layout,
    name: 'CodegenEdit',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit',
        component: () => import('@/views/infra/codegen/EditTable.vue'),
        name: 'InfraCodegenEditTable',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '修改生成配置',
          activeMenu: 'infra/codegen/index'
        }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    name: 'JobL',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'job-log',
        component: () => import('@/views/infra/job/logger/index.vue'),
        name: 'InfraJobLog',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '调度日志',
          activeMenu: 'infra/job/index'
        }
      }
    ]
  },
  {
    path: '/onSiteCheck',
    component: () => import('@/views/onSiteCheck/checkindex.vue'),
    name: 'onSiteCheckAdd',
    meta: {
      hidden: true,
      title: '现场检查',
      noTagsView: true
    }
  },

  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },

  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/social-login',
    component: () => import('@/views/Login/SocialLogin.vue'),
    name: 'SocialLogin',
    meta: {
      hidden: true,
      title: t('router.socialLogin'),
      noTagsView: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  },
  {
    path: '/bpm',
    component: Layout,
    name: 'bpm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/manager/form/edit',
        component: () => import('@/views/bpm/form/editor/index.vue'),
        name: 'BpmFormEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程表单',
          activeMenu: '/bpm/manager/form'
        }
      },
      {
        path: '/manager/model/edit',
        component: () => import('@/views/bpm/model/editor/index.vue'),
        name: 'BpmModelEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: '/manager/definition',
        component: () => import('@/views/bpm/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程定义',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: '/manager/task-assign-rule',
        component: () => import('@/views/bpm/taskAssignRule/index.vue'),
        name: 'BpmTaskAssignRuleList',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '任务分配规则'
        }
      },
      {
        path: '/process-instance/create',
        component: () => import('@/views/bpm/processInstance/create/index.vue'),
        name: 'BpmProcessInstanceCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起流程',
          activeMenu: 'bpm/processInstance/create'
        }
      },
      {
        path: '/process-instance/detail',
        component: () => import('@/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程详情',
          activeMenu: 'bpm/processInstance/detail'
        }
      },
      {
        path: '/bpm/oa/leave/create',
        component: () => import('@/views/bpm/oa/leave/create.vue'),
        name: 'OALeaveCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: '/bpm/oa/leave/detail',
        component: () => import('@/views/bpm/oa/leave/detail.vue'),
        name: 'OALeaveDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: '/bpm/oa/resign/create',
        component: () => import('@/views/bpm/oa/resign/create.vue'),
        name: 'OAResignCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起离职申请',
          activeMenu: '/bpm/oa/resign'
        }
      },
      {
        path: '/bpm/oa/resign/detail',
        component: () => import('@/views/bpm/oa/resign/detail.vue'),
        name: 'OAResignDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看离职申请',
          activeMenu: '/bpm/oa/resign'
        }
      },
      {
        path: '/bpm/oa/overtimeApplication/create',
        component: () => import('@/views/bpm/oa/overtimeApplication/create.vue'),
        name: 'OAOvertimeApplicationCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起加班申请',
          activeMenu: '/bpm/oa/overtimeApplication'
        }
      },
      {
        path: '/bpm/oa/overtimeApplication/detail',
        component: () => import('@/views/bpm/oa/overtimeApplication/detail.vue'),
        name: 'OAOvertimeApplicationDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看加班申请',
          activeMenu: '/bpm/oa/overtimeApplication'
        }
      },
      {
        path: '/bpm/oa/belate/create',
        component: () => import('@/views/bpm/oa/belate/create.vue'),
        name: 'OABelateCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起迟到/早退/漏打卡申请',
          activeMenu: '/bpm/oa/belate'
        }
      },
      {
        path: '/bpm/oa/belate/detail',
        component: () => import('@/views/bpm/oa/belate/detail.vue'),
        name: 'OABelateDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看迟到/早退/漏打卡申请',
          activeMenu: '/bpm/oa/belate'
        }
      },
      //客户资料
      {
        path: '/sales/customerInfo/detail',
        component: () => import('@/views/sales/customerInfo/detail.vue'),
        name: 'CustomerInfoDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看客户资料',
          activeMenu: '/sales/customerInfo'
        }
      },
      // 销售合同
      {
        path: '/sales/contract/create',
        component: () => import('@/views/sales/contract/create.vue'),
        name: 'ContentCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '销售合同创建',
          activeMenu: '/sales/contract'
        }
      },
      {
        path: '/sales/contract/detail',
        component: () => import('@/views/sales/contract/detail.vue'),
        name: 'ContentDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看销售合同内容',
          activeMenu: '/sales/contract'
        }
      },
      {
        path: '/sales/contract/payment',
        component: () => import('@/views/sales/contract/payment.vue'),
        name: 'ContentPayment',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '收款约定',
          activeMenu: '/sales/contract'
        }
      },

      {
        path: '/projectTemplate',
        name: 'projectTemplate',
        meta: {
          hidden: true
        },
        children: [
          {
            path: 'data/:type',
            component: () => import('@/views/project/projectTemplateType/data/index.vue'),
            name: 'ProjectTemplateData',
            meta: {
              title: '模板数据',
              noCache: true,
              hidden: true,
              canTo: true,
              icon: '',
              activeMenu: '/project/project-template-type'
            }
          }
        ]
      },

      {
        path: '/project/planApply/create',
        component: () => import('@/views/project/planApply/create.vue'),
        name: 'PlanApplyCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '计划审批创建',
          activeMenu: '/project/planApply'
        }
      },
      {
        path: '/project/planApply/detail',
        component: () => import('@/views/project/planApply/detail.vue'),
        name: 'PlanApplyDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '计划审批详情',
          activeMenu: '/project/planApply'
        }
      },
      {
        path: '/project/projectSpecial/create',
        component: () => import('@/views/project/projectSpecial/create.vue'),
        name: 'ProjectSpecialCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '立项特批创建',
          activeMenu: '/project/projectSpecial'
        }
      },
      {
        path: '/project/projectSpecial/detail',
        component: () => import('@/views/project/projectSpecial/detail.vue'),
        name: 'ProjectSpecialDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '立项特批详情',
          activeMenu: '/project/projectSpecial'
        }
      },

      // 立项申请
      {
        path: '/project/applyProject/create',
        component: () => import('@/views/project/applyProject/create.vue'),
        name: 'ProjectCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '项目创建',
          activeMenu: '/project/applyProject'
        }
      },
      {
        path: '/project/applyProject/detail',
        component: () => import('@/views/project/applyProject/detail.vue'),
        name: 'ProjectDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看项目内容',
          activeMenu: '/project/applyProject'
        }
      },
      // 任务信息
      {
        path: '/project/projectTask/create',
        component: () => import('@/views/project/projectTask/create.vue'),
        name: 'ProjectTaskCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '任务创建',
          activeMenu: '/project/projectTask'
        }
      },
      {
        path: '/project/projectTask/detail',
        component: () => import('@/views/project/projectTask/detail.vue'),
        name: 'ProjectTaskDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看任务详情',
          activeMenu: '/project/projectTask'
        }
      },
      // 开票申请
      {
        path: '/sales/invoice/create',
        component: () => import('@/views/sales/invoice/create.vue'),
        name: 'InvoiceCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '开票申请创建',
          activeMenu: '/sales/invoice'
        }
      },
      {
        path: '/sales/invoice/detail',
        component: () => import('@/views/sales/invoice/detail.vue'),
        name: 'InvoiceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看开票申请',
          activeMenu: '/sales/invoice'
        }
      },
      {
        path: '/sales/receiptRecord/detail',
        component: () => import('@/views/sales/receiptRecord/detail.vue'),
        name: 'ReceiptRecordDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看收款记录',
          activeMenu: '/sales/receiptRecord'
        }
      },
      // 咨询任务书
      {
        path: '/consultTask/create',
        component: () => import('@/views/consultTask/create.vue'),
        name: 'ConsultTaskCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '咨询任务书创建',
          activeMenu: '/consultTask'
        }
      },
      {
        path: '/consultTask/detail',
        component: () => import('@/views/consultTask/detail.vue'),
        name: 'ConsultTaskDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看咨询任务书',
          activeMenu: '/consultTask'
        }
      },
      // 项目成果
      {
        path: '/project/projectTaskResult/detail',
        component: () => import('@/views/project/projectTaskResult/detail.vue'),
        name: 'ProjectTaskResultDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看项目成果',
          activeMenu: '/project/projectTaskResult'
        }
      },
      // 工时延长
      {
        path: '/project/workHourDelay/create',
        component: () => import('@/views/project/workHourDelay/create.vue'),
        name: 'WorkHourDelayCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '工时延长创建',
          activeMenu: '/project/workHourDelay'
        }
      },
      {
        path: '/project/workHourDelay/detail',
        component: () => import('@/views/project/workHourDelay/detail.vue'),
        name: 'WorkHourDelayDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看工时延长',
          activeMenu: '/project/workHourDelay'
        }
      },
      // 申请单
      {
        path: '/finance/businessTripApply/create',
        component: () => import('@/views/finance/businessTripApply/create.vue'),
        name: 'BusinessTripApplyCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '申请单创建',
          activeMenu: '/finance/businessTripApply'
        }
      },
      {
        path: '/finance/businessTripApply/detail',
        component: () => import('@/views/finance/businessTripApply/detail.vue'),
        name: 'BusinessTripApplyDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看申请单',
          activeMenu: '/finance/businessTripApply'
        }
      },
      // 费用报销
      {
        path: '/finance/reimbursement/create',
        component: () => import('@/views/finance/reimbursement/create.vue'),
        name: 'ReimbursementCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '费用报销创建',
          activeMenu: '/finance/reimbursement'
        }
      },
      {
        path: '/finance/reimbursement/detail',
        component: () => import('@/views/finance/reimbursement/detail.vue'),
        name: 'ReimbursementDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看费用报销',
          activeMenu: '/finance/reimbursement'
        }
      },
      // 变更记录
      {
        path: '/project/changeRecord/create',
        component: () => import('@/views/project/changeRecord/create.vue'),
        name: 'ChangeRecordCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '变更记录创建',
          activeMenu: '/project/changeRecord'
        }
      },
      {
        path: '/project/changeRecord/detail',
        component: () => import('@/views/project/changeRecord/detail.vue'),
        name: 'ChangeRecordDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看变更记录',
          activeMenu: '/project/changeRecord'
        }
      },
      // 外出申请
      {
        path: '/OA/goingOut/create',
        component: () => import('@/views/OA/goingOut/create.vue'),
        name: 'GoingOutCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '外出申请创建',
          activeMenu: '/OA/goingOut'
        }
      },
      {
        path: '/OA/goingOut/detail',
        component: () => import('@/views/OA/goingOut/detail.vue'),
        name: 'GoingOutDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看外出申请',
          activeMenu: '/OA/goingOut'
        }
      },
      {
        path: '/OA/talent/detail',
        component: () => import('@/views/OA/talent/detail.vue'),
        name: 'TalentDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看招聘管理详情',
          activeMenu: '/OA/talent'
        }
      },
      {
        path: '/OA/performanceProcess/create',
        component: () => import('@/views/OA/performanceProcess/create.vue'),
        name: 'PerformanceProcessCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '绩效考核流程创建',
          activeMenu: '/OA/performanceProcess'
        }
      },
      {
        path: '/statistics/workCalendar/planDetail',
        component: () => import('@/views/statistics/workCalendar/planDetail.vue'),
        name: 'planDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看变更计划详情',
          activeMenu: '/statistics/workCalendar'
        }
      }
    ]
  },
  {
    path: '/member',
    component: Layout,
    name: 'MemberCenter',
    meta: { hidden: true },
    children: [
      {
        path: 'user/detail/:id',
        name: 'MemberUserDetail',
        meta: {
          title: '会员详情',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/member/user/detail/index.vue')
      }
    ]
  },
  {
    path: '/target',
    component: Layout,
    name: 'targetCenter',
    meta: { hidden: true },
    children: [
      {
        path: 'target/setting/detail',
        name: 'TargetSettingDetail',
        meta: {
          title: '目标详情',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/target/setting/detail.vue')
      }
    ]
  },
  {
    path: '/crm',
    component: Layout,
    name: 'CrmCenter',
    meta: { hidden: true },
    children: [
      {
        path: '/crm/workReport/create',
        component: () => import('@/views/crm/workReport/create.vue'),
        name: 'CrmWorkReportCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '工作报告流程创建',
          activeMenu: '/crm/workReport'
        }
      },
      {
        path: '/crm/workReport/detail',
        component: () => import('@/views/crm/workReport/detail.vue'),
        name: 'CrmWorkReportDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '报告详情',
          activeMenu: '/crm/workReport'
        }
      },
      {
        path: 'clue/detail/:id',
        name: 'CrmClueDetail',
        meta: {
          title: '线索详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/clue'
        },
        component: () => import('@/views/crm/clue/detail/index.vue')
      },
      {
        path: 'customer/detail/:id',
        name: 'CrmCustomerDetail',
        meta: {
          title: '客户详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/customer'
        },
        component: () => import('@/views/crm/customer/detail/index.vue')
      },
      {
        path: 'business/detail/:id',
        name: 'CrmBusinessDetail',
        meta: {
          title: '商机详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/business'
        },
        component: () => import('@/views/crm/business/detail/index.vue')
      },
      {
        path: 'contract/detail/:id',
        name: 'CrmContractDetail',
        meta: {
          title: '合同详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/contract'
        },
        component: () => import('@/views/crm/contract/detail/index.vue')
      },
      {
        path: 'receivable-plan/detail/:id',
        name: 'CrmReceivablePlanDetail',
        meta: {
          title: '回款计划详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/receivable-plan'
        },
        component: () => import('@/views/crm/receivable/plan/detail/index.vue')
      },
      {
        path: 'receivable/detail/:id',
        name: 'CrmReceivableDetail',
        meta: {
          title: '回款详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/receivable'
        },
        component: () => import('@/views/crm/receivable/detail/index.vue')
      },
      {
        path: 'contact/detail/:id',
        name: 'CrmContactDetail',
        meta: {
          title: '联系人详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/contact'
        },
        component: () => import('@/views/crm/contact/detail/index.vue')
      },
      {
        path: 'product/detail/:id',
        name: 'CrmProductDetail',
        meta: {
          title: '产品详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/product'
        },
        component: () => import('@/views/crm/product/detail/index.vue')
      },
      {
        path: 'bidapplication/create',
        component: () => import('@/views/crm/bidapplication/create.vue'),
        name: 'BidApplicationCreate',
        meta: {
          title: '投标申请创建',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/box/bid-application'
        }
      },
      {
        path: 'bidapplication/detail',
        component: () => import('@/views/crm/bidapplication/detail.vue'),
        name: 'BidApplicationDetail',
        meta: {
          title: '投标申请详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/box/bid-application'
        }
      }
    ]
  },
  {
    path: '/hrm',
    component: Layout,
    name: 'hrmCenter',
    meta: { hidden: true },
    children: [
      {
        path: '/hrm/carApply/create',
        component: () => import('@/views/hrm/carApply/create.vue'),
        name: 'HrmCarApplyCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '用车申请流程创建',
          activeMenu: '/hrm/carApply'
        }
      },
      {
        path: '/hrm/carApply/detail',
        name: 'HrmCarApplyDetail',
        meta: {
          title: '用车申请详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/clue'
        },
        component: () => import('@/views/hrm/carApply/detail.vue')
      }
    ]
  },
  {
    path: '/erp',
    component: Layout,
    name: 'erpCenter',
    meta: { hidden: true },
    children: [
      {
        path: '/erp/purchase/purchase-request/create',
        component: () => import('@/views/erp/purchase/purchaserequest/create.vue'),
        name: 'ErpPurchaseRequestCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '项目采购申请管理流程创建',
          activeMenu: '/erp/purchase/purchaserequest'
        }
      },
      {
        path: '/erp/purchase/purchase-request/detail',
        name: 'ErpPurchaseRequestDetail',
        meta: {
          title: '项目采购申请详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/purchaserequest'
        },
        component: () => import('@/views/erp/purchase/purchaserequest/detail.vue')
      },
      {
        path: '/erp/purchase/operational-procurement/create',
        component: () => import('@/views/erp/purchase/operationalProcurement/create.vue'),
        name: 'ErpOperationalProcurementCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '业务采购申请管理流程创建',
          activeMenu: '/erp/purchase/operationalProcurement'
        }
      },
      {
        path: '/erp/purchase/operational-procurement/detail',
        name: 'ErpOperationalProcurementDetail',
        meta: {
          title: '业务采购申请详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/operationalProcurement'
        },
        component: () => import('@/views/erp/purchase/operationalProcurement/detail.vue')
      },
      {
        path: '/erp/purchase/administrative-procurement/create',
        component: () => import('@/views/erp/purchase/administrativeProcurement/create.vue'),
        name: 'ErpAdministrativeProcurementCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '行政采购申请管理流程创建',
          activeMenu: '/erp/purchase/administrativeProcurement'
        }
      },
      {
        path: '/erp/purchase/administrative-procurement/detail',
        name: 'ErpAdministrativeProcurementDetail',
        meta: {
          title: '行政采购申请详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/administrativeProcurement'
        },
        component: () => import('@/views/erp/purchase/administrativeProcurement/detail.vue')
      },
      {
        path: '/erp/purchase/supplier/create',
        component: () => import('@/views/erp/purchase/supplier/create.vue'),
        name: 'SupplierCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供应商流程创建',
          activeMenu: '/erp/purchase/supplier'
        }
      },
      {
        path: '/erp/purchase/supplier/detail',
        name: 'SupplierDetail',
        meta: {
          title: '供应商详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/supplier'
        },
        component: () => import('@/views/erp/purchase/supplier/detail.vue')
      },
      {
        path: '/erp/purchase/enquiry/create',
        name: 'ErpPurchaseEnquiryCreate',
        meta: {
          title: '采购询价创建',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/enquiry'
        },
        component: () => import('@/views/erp/purchase/enquiry/create.vue')
      },
      {
        path: '/erp/purchase/enquiry/detail',
        name: 'ErpPurchaseEnquiryDetail',
        meta: {
          title: '采购询价详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/enquiry'
        },
        component: () => import('@/views/erp/purchase/enquiry/detail.vue')
      },
      {
        path: '/erp/purchase/supplierquote/detail',
        name: 'ErpPurchaseSupplierquoteDetail',
        meta: {
          title: '供应商报价详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/supplierquote'
        },
        component: () => import('@/views/erp/purchase/supplierquote/detail.vue')
      },
      {
        path: '/erp/product/product/detail',
        name: 'ErpProductDetail',
        meta: {
          title: '产品详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/product/product'
        },
        component: () => import('@/views/erp/product/product/detail.vue')
      },
      {
        path: '/erp/purchase/order/create',
        name: 'PurchaseOrderCreate',
        meta: {
          title: '采购订单创建',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/order'
        },
        component: () => import('@/views/erp/purchase/order/create.vue')
      },
      {
        path: '/erp/purchase/order/detail',
        name: 'PurchaseOrderDetail',
        meta: {
          title: '采购订单详情',
          noCache: true,
          hidden: true,
          activeMenu: '/erp/purchase/order'
        },
        component: () => import('@/views/erp/purchase/order/detail.vue')
      }
    ]
  },
  {
    path: '/inspect',
    component: Layout,
    name: 'inspectCenter',
    meta: { hidden: true },
    children: [
      {
        path: 'addEdit',
        name: 'inspectAddEdit',
        meta: {
          title: '现场检查',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/onSiteCheck/addEdit.vue')
      },
      {
        path: 'detail',
        name: 'inspectDetail',
        meta: {
          title: '现场检查详情',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/onSiteCheck/detail.vue')
      }
    ]
  }
]

export default remainingRouter
