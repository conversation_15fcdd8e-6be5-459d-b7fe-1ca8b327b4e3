/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: any
  colorType: ElementPlusInfoType | ''
  cssClass: string
  remark?: string
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

export const getIntDictOptions = (dictType: string): DictDataType[] => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  SYSTEM_TENANT_PACKAGE_ID = 'system_tenant_package_id',
  TERMINAL = 'terminal', // 终端

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex',
  SYSTEM_MENU_TYPE = 'system_menu_type',
  SYSTEM_ROLE_TYPE = 'system_role_type',
  SYSTEM_DATA_SCOPE = 'system_data_scope',
  SYSTEM_NOTICE_TYPE = 'system_notice_type',
  SYSTEM_OPERATE_TYPE = 'system_operate_type',
  SYSTEM_LOGIN_TYPE = 'system_login_type',
  SYSTEM_LOGIN_RESULT = 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE = 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS = 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS = 'system_sms_receive_status',
  SYSTEM_ERROR_CODE_TYPE = 'system_error_code_type',
  SYSTEM_OAUTH2_GRANT_TYPE = 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS = 'system_mail_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type',
  SYSTEM_SOCIAL_TYPE = 'system_social_type',
  BUSINESS_LEVEL = 'business_level', // 商机等级

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',
  INFRA_JOB_STATUS = 'infra_job_status',
  INFRA_JOB_LOG_STATUS = 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS = 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE = 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE = 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE = 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE = 'infra_codegen_scene',
  INFRA_FILE_STORAGE = 'infra_file_storage',

  // ========== BPM 模块 ==========
  BPM_MODEL_CATEGORY = 'bpm_model_category',
  BPM_MODEL_FORM_TYPE = 'bpm_model_form_type',
  BPM_TASK_ASSIGN_RULE_TYPE = 'bpm_task_assign_rule_type',
  BPM_PROCESS_INSTANCE_STATUS = 'bpm_process_instance_status',
  BPM_PROCESS_INSTANCE_RESULT = 'bpm_process_instance_result',
  BPM_TASK_ASSIGN_SCRIPT = 'bpm_task_assign_script',
  BPM_OA_LEAVE_TYPE = 'bpm_oa_leave_type',

  // ========== ERP - 企业资源计划模块  ==========
  ERP_AUDIT_STATUS = 'erp_audit_status', // ERP 审批状态
  ERP_STOCK_RECORD_BIZ_TYPE = 'erp_stock_record_biz_type', // 库存明细的业务类型
  PROCUREMENT_CATEGORY = 'procurement_category', //采购类别

  // ========== PAY 模块 ==========
  PAY_CHANNEL_CODE = 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS = 'pay_order_status', // 商户支付订单状态
  PAY_REFUND_STATUS = 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS = 'pay_notify_status', // 商户支付回调状态
  PAY_NOTIFY_TYPE = 'pay_notify_type', // 商户支付回调状态
  PAY_TRANSFER_STATUS = 'pay_transfer_status', // 转账订单状态
  PAY_TRANSFER_TYPE = 'pay_transfer_type', // 转账订单状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH = 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE = 'mp_message_type', // 消息类型

  // ========== MALL - 会员模块 ==========
  MEMBER_POINT_BIZ_TYPE = 'member_point_biz_type', // 积分的业务类型
  MEMBER_EXPERIENCE_BIZ_TYPE = 'member_experience_biz_type', // 会员经验业务类型

  // ========== MALL - 商品模块 ==========
  PRODUCT_UNIT = 'product_unit', // 商品单位
  PRODUCT_SPU_STATUS = 'product_spu_status', //商品状态
  PROMOTION_TYPE_ENUM = 'promotion_type_enum', // 营销类型枚举

  // ========== MALL - 交易模块 ==========
  EXPRESS_CHARGE_MODE = 'trade_delivery_express_charge_mode', //快递的计费方式
  TRADE_AFTER_SALE_STATUS = 'trade_after_sale_status', // 售后 - 状态
  TRADE_AFTER_SALE_WAY = 'trade_after_sale_way', // 售后 - 方式
  TRADE_AFTER_SALE_TYPE = 'trade_after_sale_type', // 售后 - 类型
  TRADE_ORDER_TYPE = 'trade_order_type', // 订单 - 类型
  TRADE_ORDER_STATUS = 'trade_order_status', // 订单 - 状态
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS = 'trade_order_item_after_sale_status', // 订单项 - 售后状态
  TRADE_DELIVERY_TYPE = 'trade_delivery_type', // 配送方式
  BROKERAGE_ENABLED_CONDITION = 'brokerage_enabled_condition', // 分佣模式
  BROKERAGE_BIND_MODE = 'brokerage_bind_mode', // 分销关系绑定模式
  BROKERAGE_BANK_NAME = 'brokerage_bank_name', // 佣金提现银行
  BROKERAGE_WITHDRAW_TYPE = 'brokerage_withdraw_type', // 佣金提现类型
  BROKERAGE_RECORD_BIZ_TYPE = 'brokerage_record_biz_type', // 佣金业务类型
  BROKERAGE_RECORD_STATUS = 'brokerage_record_status', // 佣金状态
  BROKERAGE_WITHDRAW_STATUS = 'brokerage_withdraw_status', // 佣金提现状态

  // ========== MALL - 营销模块 ==========
  PROMOTION_DISCOUNT_TYPE = 'promotion_discount_type', // 优惠类型
  PROMOTION_PRODUCT_SCOPE = 'promotion_product_scope', // 营销的商品范围
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE = 'promotion_coupon_template_validity_type', // 优惠劵模板的有限期类型
  PROMOTION_COUPON_STATUS = 'promotion_coupon_status', // 优惠劵的状态
  PROMOTION_COUPON_TAKE_TYPE = 'promotion_coupon_take_type', // 优惠劵的领取方式
  PROMOTION_ACTIVITY_STATUS = 'promotion_activity_status', // 优惠活动的状态
  PROMOTION_CONDITION_TYPE = 'promotion_condition_type', // 营销的条件类型枚举
  PROMOTION_BARGAIN_RECORD_STATUS = 'promotion_bargain_record_status', // 砍价记录的状态
  PROMOTION_COMBINATION_RECORD_STATUS = 'promotion_combination_record_status', // 拼团记录的状态
  PROMOTION_BANNER_POSITION = 'promotion_banner_position', // banner 定位

  // ========== CRM - 客户管理模块 ==========
  CRM_AUDIT_STATUS = 'crm_audit_status', // CRM 审批状态
  CRM_BIZ_TYPE = 'crm_biz_type', // CRM 业务类型
  CRM_BUSINESS_END_STATUS_TYPE = 'crm_business_end_status_type', // CRM 商机结束状态类型
  CRM_RECEIVABLE_RETURN_TYPE = 'crm_receivable_return_type', // CRM 回款的还款方式
  CRM_CUSTOMER_INDUSTRY = 'crm_customer_industry', // CRM 客户所属行业
  CRM_CUSTOMER_LEVEL = 'crm_customer_level', // CRM 客户级别
  CRM_CUSTOMER_SOURCE = 'crm_customer_source', // CRM 客户来源
  CRM_PRODUCT_STATUS = 'crm_product_status', // CRM 商品状态
  CRM_PERMISSION_LEVEL = 'crm_permission_level', // CRM 数据权限的级别
  CRM_PRODUCT_UNIT = 'crm_product_unit', // CRM 产品单位
  CRM_FOLLOW_UP_TYPE = 'crm_follow_up_type', // CRM 跟进方式
  DEAL_STATUS = 'deal_status', // CRM 成交状态

  // ========== 项目管理业务 ===========
  INDUSTRY = 'industry', // 行业
  INSTITUTIONAL_NATURE = 'institutional_nature', // 机构性质
  NUMBER_EMPLOYEES = 'number_employees', // 员工数
  ANNUAL_INCOME = 'annual_income', // 年收入
  CLASSIFICATION = 'classification', // 分级
  SOURCE_ID = 'source_id', // 来源
  CUSTOMER_GROUP = 'customer_group', // 客户类别
  CONTRACT_STATUS = 'contract_status', // 销售合同状态
  CONTRACT_CATEGORY = 'contract_category', // 合同类别
  BUSINESS_CATEGORY = 'business_category', // 业务种类
  CUSTOMER_NATURE = 'customer_nature', // 客户性质
  PRIORITY = 'priority', // 合同优先级
  CURRENCY = 'currency', // 币种
  PROJECT_STATUS = 'project_status', // 项目状态
  PROJECT_TYPE = 'project_type', // 项目类型
  PROJECT_BUSINESS_TYPE = 'project_bisiness_type', // 项目业务类型
  INVOICE_TYPE = 'invoice_type', // 发票类型
  TAX_RATE = 'tax_rate', // 税率
  ACCPETANCE_TYPE = 'acceptance_type', // 完工验收方式
  IS_ORDER_ISSUED = 'is_order_issued', // 完工单是否发出
  SUBMIT_FLAG = 'submit_flag', // 合同原件是否提交项目管理人
  BUSINESS_TYPE = 'business_type', // 合同原件是否提交项目管理人
  TEMPLATE_STATUS = 'template_status', //模板管理状态
  PROJECT_PROPOSAL = 'project_proposal', // 咨询建议书
  TASK_STATUS = 'task_status', //项目计划任务状态
  TASK_TYPE = 'task_type', //项目计划任务状态
  BPM_TASK_STATUS = 'bpm_task_status', //流程实例结果
  CUSTOMER_DEPT = 'customer_dept', // 客户部门
  OPERATING_AREA = 'operating_area', // 经营面积
  OUT_BUSINESS_APPLY_TYPE = 'out_business_apply_type', // 出差申请类型
  OUT_BUSINESS_VEHICLE = 'out_business_vehicle', // 出差申请事项
  EXPENSES_CLAIM_TYPE = 'expenses_claim_type', // 报销类型
  TRAVEL_MODE = 'travel_mode', // 出行方式
  LOCAL_TRAVEL_MODE = 'local_travel_mode', // 市内出差交通方式
  INTER_CITY_TRAVEL_MODE = 'inter_city_travel_mode', // 跨市出差交通方式
  // ========== 现场检查 ===========
  HIDE_DANGER_LEVEL = 'hide_danger_level',
  INSPECT_TYPE = 'inspect_type',
  HIDE_DANGER_TYPE = 'hide_danger_type',
  INSPECT_STATUS = 'inspect_status',
  // ========== 系统试用管理 ===========
  PRODUCT_STATUS = 'product_status',
  MEMBER_STATUS = 'member_status',
  APPLY_STATUS = 'apply_status',
  TASK_PRIORITY = 'task_priority', // 任务优先级

  // ========== hrm 人事管理 ===========
  ACADEMIC_DEGREE = 'academic_degree', //学位
  QUALIFICATION = 'qualification', //学历
  RANK_LEVEL = 'rank_level', //职称级别
  TECHNICAL_TITLE = 'technical_title', //职称
  ATTENDANCE_SCHEDULING_TYPE = 'attendance_scheduling_type', //考勤排班类型
  ON_DUTY_STATUS = 'on_duty_status', //在职状态
  JOB = 'job', //职务
  EMPLOYEE_TYPE = 'employee_type', //员工类型
  WAGE_SCALE = 'wage_scale', //工资级别
  PROFESSION = 'profession', //工种
  HUKOU = 'hukou', //户口类别
  POLITICAL_STATUS = 'political_status', //政治面貌
  HEALTH_STATUS = 'health_status', //健康状况
  MARRIAGE = 'marriage', //婚姻状况
  BLOOD_TYPE = 'blood_type', //血型
  ZODIAC = 'zodiac', //生肖
  CONTRACT_TYPE = 'contract_type', //合同类型
  SIGN_TYPE = 'sign_type', //签约方式
  SPECIALIZATION = 'specialization', //签约方式
  LICENCE_TYPE = 'licence_type', //证照类型
  INCENTIVE_ITEM = 'incentive_item', //奖惩项目
  INCENTIVE_TYPE = 'incentive_type', //奖惩类型
  TRANSFER_TYPE = 'transfer_type', //调动类型
  CARE_TYPE = 'care_type', //关怀类型
  MATERIAL_CATEGORY = 'material_category', //物资种类
  UNIT = 'unit', //单位
  USE_REASON = 'use_reason', //申请事由
  POSITION_CATEGORY = 'position_category', //岗位分类
  RESUME_SOURCE = 'resume_source', //简历来源
  TALENT_STAR_RATING = 'talent_star_rating', //星级

  // ========== 目标管理 ===========
  BELONG_MODULE = 'belong_module', //所属模块
  EVALUATION_YEAR = 'evaluation_year', //考核年度

  /* 供应商 */
  SUPPLIER_TYPE = 'supplier_type', // 供应商类型
  COMPANY_TYPE = 'company_type', // 企业类型
  COMPANY_SCALE = 'company_scale', // 企业规模
  CREDIT_RATING = 'credit_rating', // 信用等级

  /* 招投标管理 */
  BID_TENDER_STATUS = 'bid_tender_status', // 招标状态
  BID_APPLY_STATUS = 'bid_apply_status' // 投标申请状态
}
