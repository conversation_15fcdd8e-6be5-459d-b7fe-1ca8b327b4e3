<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800">
    <div v-loading="loading" class="notice-detail">
      <div class="notice-header">
        <div class="notice-title">{{ noticeData.title }}</div>
        <div class="notice-meta">
          <el-tag :type="getNoticeTypeTag(noticeData.type)" size="small">
            {{ getDictLabel(DICT_TYPE.SYSTEM_NOTICE_TYPE, noticeData.type) }}
          </el-tag>
          <span class="notice-date">{{ formatTime(noticeData.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </div>
      </div>
      
      <div class="notice-content">
        <div class="content-label">公告内容：</div>
        <div class="content-body" v-html="noticeData.content"></div>
      </div>
      
      <div v-if="noticeData.remark" class="notice-remark">
        <div class="remark-label">备注：</div>
        <div class="remark-body">{{ noticeData.remark }}</div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatTime } from '@/utils'
import * as NoticeApi from '@/api/system/notice'

defineOptions({ name: 'NoticeDetail' })

const dialogVisible = ref(false)
const dialogTitle = ref('通知公告详情')
const loading = ref(false)
const noticeData = ref<NoticeApi.NoticeVO>({
  id: undefined,
  title: '',
  type: 0,
  content: '',
  status: 0,
  remark: '',
  creator: '',
  createTime: new Date()
})

// 获取公告类型对应的标签类型
const getNoticeTypeTag = (type: number) => {
  switch (type) {
    case 1: return 'success' // 通知
    case 2: return 'warning' // 公告
    case 3: return 'danger'  // 警告
    default: return 'info'
  }
}

// 打开弹窗
const open = async (id: number) => {
  dialogVisible.value = true
  loading.value = true
  
  try {
    const data = await NoticeApi.getNotice(id)
    noticeData.value = data
  } catch (error) {
    console.error('获取公告详情失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.notice-detail {
  .notice-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
    
    .notice-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 12px;
      line-height: 1.4;
    }
    
    .notice-meta {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .notice-date {
        font-size: 14px;
        color: #6b7280;
      }
    }
  }
  
  .notice-content {
    margin-bottom: 24px;
    
    .content-label {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 12px;
    }
    
    .content-body {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      min-height: 120px;
      line-height: 1.6;
      color: #374151;
      
      :deep(p) {
        margin: 0 0 8px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 8px 0;
      }
      
      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 8px 0;
        
        th, td {
          border: 1px solid #d1d5db;
          padding: 8px 12px;
          text-align: left;
        }
        
        th {
          background: #f3f4f6;
          font-weight: 600;
        }
      }
    }
  }
  
  .notice-remark {
    margin-bottom: 24px;
    
    .remark-label {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 12px;
    }
    
    .remark-body {
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 8px;
      padding: 12px;
      color: #92400e;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

@media (max-width: 768px) {
  .notice-detail {
    .notice-header {
      .notice-title {
        font-size: 18px;
      }
      
      .notice-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
    
    .notice-content,
    .notice-remark {
      .content-body,
      .remark-body {
        padding: 12px;
      }
    }
  }
}
</style> 