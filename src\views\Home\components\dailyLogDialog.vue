<!--
 * @Description: 日志填写弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-15 17:15:41
 * @LastEditTime: 2024-10-14 10:12:55
-->

<template>
  <el-dialog title="新增日志" v-model="dialogVisible" width="1580px" append-to-body center>
    <!-- <div class="tip">每天最多填写 {{ taskDetail.dailyManhourLimit }} 小时工时</div> -->
    <el-table :data="logTableData">
      <el-table-column label="日期" prop="consumeDate" width="180">
        <template #default="scope">
          <el-date-picker
            clearable
            v-model="scope.row.consumeDate"
            :disabled-date="disabledDate"
            type="date"
            value-format="x"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="耗时" prop="consumeHour" width="200">
        <template #default="scope">
          <el-input-number v-model="scope.row.consumeHour" :min="0" />
        </template>
      </el-table-column>
      <el-table-column label="工作内容" prop="remark">
        <template #default="scope">
          <el-input
            clearable
            v-model="scope.row.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </template>
      </el-table-column>
      <el-table-column label="文件" prop="fileUrls" width="200">
        <template #default="scope">
          <UploadFile
            style="padding-top: 10px"
            v-model="scope.row.fileUrls"
            :file-size="5120"
            :limit="5"
            returnType="array"
            @uploading="handleUploading"
          />
        </template>
      </el-table-column>
      <el-table-column label="成果" prop="resultUrls" width="200">
        <template #default="scope">
          <UploadFile
            style="padding-top: 10px"
            v-model="scope.row.resultUrls"
            :file-size="5120"
            :limit="5"
            returnType="array"
            @uploading="handleUploading"
          />
        </template>
      </el-table-column>
    </el-table>
    <el-table :data="tableData" :border="false" class="history-log-table">
      <el-table-column
        label="日期"
        prop="consumeDate"
        :formatter="dateFormatter"
        width="280"
      ></el-table-column>
      <el-table-column label="耗时" prop="consumeHour" width="80">
        <template #default="scope"> {{ scope.row.consumeHour }} H</template>
      </el-table-column>
      <el-table-column label="记录人" prop="userName" width="100"></el-table-column>
      <el-table-column label="工作内容" prop="remark"></el-table-column>
      <el-table-column label="文件" prop="fileUrls" width="180">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.fileUrls" />
        </template>
      </el-table-column>
      <el-table-column label="成果" prop="resultUrls" width="180">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.resultUrls" />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="formLoading || isUploading" type="primary" @click="save"
          >确 定</el-button
        >
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import { ProjectTaskManhourApi } from '@/api/pms/taskHour'
import { useI18n } from 'vue-i18n'

const formLoading = ref(false)
const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const message = useMessage()
const props = defineProps({
  taskDetail: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})
const dialogVisible = ref(false)

const logTableData: any = ref([
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  },
  {
    consumeDate: new Date().getTime(),
    consumeHour: null,
    remark: '',
    fileUrls: [],
    resultUrls: []
  }
])
//** 弹框打开事件 */
const openDialog = () => {
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  reset()
  dialogVisible.value = false
}

const reset = () => {
  logTableData.value = [
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    },
    {
      consumeDate: new Date().getTime(),
      consumeHour: null,
      remark: '',
      fileUrls: [],
      resultUrls: []
    }
  ]
}
const save = async () => {
  formLoading.value = true
  try {
    let submitData = logTableData.value.filter((item) => item.consumeHour !== null)
    if (submitData.length === 0) {
      message.warning('请至少填写一条日志耗时')
      return
    }
    submitData.forEach((item) => {
      if (!item.remark) {
        message.warning('请填写工作内容')
        throw new Error()
      }
      item.projectId = props.taskDetail.projectId
      item.taskId = props.taskDetail.id
    })
    await ProjectTaskManhourApi.createProjectTaskManhour(submitData)
    message.success(t('common.createSuccess'))
    emit('fetch-data')
    // 关闭当前 Tab
    close()
  } catch (error) {
  } finally {
    formLoading.value = false
  }
}
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(102,126,234,0.12);
  overflow: hidden;
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 28px 40px 16px 40px;
    .el-dialog__title {
      font-size: 20px;
      font-weight: 700;
      color: #fff;
    }
  }
  .el-dialog__body {
    background: #f8fafc;
    padding: 32px 40px 8px 40px;
  }
  .el-dialog__footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 24px 40px;
  }
}
.el-table {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.08);
  overflow: hidden;
  margin-bottom: 24px;
  :deep(.el-table__header) th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e2e8f0;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    td {
      border-bottom: 1px solid #f1f5f9;
      padding: 16px 8px;
      font-size: 14px;
      color: #334155;
    }
  }
  .el-input, .el-input-number, .el-date-picker {
    width: 100% !important;
    border-radius: 10px !important;
    font-size: 15px;
    .el-input__wrapper, .el-date-editor {
      border-radius: 10px !important;
      border: 1px solid #d1d5db !important;
      box-shadow: 0 1px 4px rgba(102,126,234,0.08);
      transition: all 0.3s;
      background: #f8fafc;
      &:hover, &.is-focus {
        border-color: #667eea !important;
        box-shadow: 0 2px 8px rgba(102,126,234,0.15);
        background: #fff;
      }
    }
    .el-input__inner, .el-date-editor input {
      font-size: 15px;
      color: #334155;
      &::placeholder {
        color: #b6bdd3;
        font-weight: 400;
      }
    }
  }
  .el-input-number {
    .el-input-number__decrease, .el-input-number__increase {
      background: #f8fafc;
      border-color: #e2e8f0;
      color: #475569;
      &:hover {
        background: #667eea;
        border-color: #667eea;
        color: white;
      }
    }
    .el-input__inner {
      border-radius: 8px;
      border: 1px solid #d1d5db;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      font-size: 14px;
      color: #1e293b;
      &::placeholder {
        color: #94a3b8;
      }
    }
  }
  .el-textarea__inner {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 14px;
    color: #1e293b;
    &:hover {
      border-color: #667eea;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    }
    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    &::placeholder {
      color: #94a3b8;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  .el-button {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
    }
  }
}
@media (max-width: 900px) {
  :deep(.el-dialog__body) {
    padding: 12px !important;
  }
  .el-table {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  }
  .dialog-footer {
    padding: 12px 8px;
    .el-button {
      padding: 8px 12px;
      font-size: 14px;
    }
  }
}
</style>
