<!--
 * @Description: 修改日志弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-06 13:13:58
 * @LastEditTime: 2025-02-20 09:27:44
-->

<template>
  <el-dialog title="修改日志" v-model="dialogVisible" width="680px" append-to-body center>
    <el-form ref="formRef" label-width="90px" :model="formData" :rules="rules">
      <el-form-item label="日期" prop="consumeDate">
        <el-date-picker
          clearable
          type="date"
          v-model="formData.consumeDate"
          value-format="x"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="耗时" prop="consumeHour">
        <el-input-number v-model="formData.consumeHour" :min="0" />
        &nbsp;&nbsp;小时
      </el-form-item>
      <el-form-item label="工作内容" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
        ></el-input>
      </el-form-item>
      <el-form-item label="文件" prop="fileUrls">
        <UploadFile
          v-model="formData.fileUrls"
          :file-size="5120"
          :limit="5"
          returnType="array"
          @uploading="handleUploading"
        />
      </el-form-item>
      <el-form-item label="成果" prop="resultUrls">
        <UploadFile
          v-model="formData.resultUrls"
          :file-size="5120"
          :limit="5"
          returnType="array"
          @uploading="handleUploading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="formLoading || isUploading" type="primary" @click="save"
          >确 定</el-button
        >
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { ProjectTaskManhourApi } from '@/api/pms/taskHour'
import { useI18n } from 'vue-i18n'

const formLoading = ref(false)
const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const formRef = ref()
const rules = reactive({
  consumeDate: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
  consumeHour: [{ required: true, message: '耗时不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '工作内容不能为空', trigger: 'blur' }]
})

const message = useMessage()
const props = defineProps({
  taskDetail: {
    type: Object,
    default: () => ({})
  }
})
const dialogVisible = ref(false)
const formData: any = ref({
  consumeHour: null,
  consumeDate: new Date().getTime(),
  fileUrls: [],
  resultUrls: [],
  remark: ''
})

// 用于跟踪原始的文件和成果数据
const originalData = ref({
  fileUrls: [],
  resultUrls: []
})

//** 弹框打开事件 */
const openDialog = (item) => {
  formData.value = cloneDeep(item)
  // 保存原始的文件和成果数据
  originalData.value = {
    fileUrls: cloneDeep(item.fileUrls || []),
    resultUrls: cloneDeep(item.resultUrls || [])
  }
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  reset()
  dialogVisible.value = false
}
const save = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return
    formLoading.value = true
    try {
      // 检查成果是否有变化
      const resultChanged = JSON.stringify(originalData.value.resultUrls) !== JSON.stringify(formData.value.resultUrls)

      let submitData = {
        ...formData.value,
        projectId: props.taskDetail.projectId,
        taskId: props.taskDetail.id,
        changeFlag: resultChanged // 如果成果有变化，则为true
      }
      await ProjectTaskManhourApi.updateProjectTaskManhour(submitData)
      message.success(t('common.createSuccess'))
      // 关闭当前 Tab
      emit('fetch-data')
      close()
    } finally {
      formLoading.value = false
    }
  })
}

const reset = () => {
  formRef.value.resetFields()
  formData.value = {
    consumeHour: null,
    actualEndDate: new Date().getTime(),
    fileUrls: [],
    resultUrls: [],
    remark: ''
  }
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.tip {
  margin-left: 10px;
  color: #f56c6c;
  padding-bottom: 10px;
  font-weight: bold;
  font-size: 14px;
}
</style>
