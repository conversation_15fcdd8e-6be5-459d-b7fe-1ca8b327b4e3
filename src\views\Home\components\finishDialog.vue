<!--
 * @Description: 完成任务弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-15 17:22:57
 * @LastEditTime: 2024-10-14 10:15:02
-->

<template>
  <el-dialog
    :title="taskDetail.describeVal"
    v-model="dialogVisible"
    width="680px"
    append-to-body
    center
  >
    <el-form ref="formRef" label-width="90px" :model="formData" :rules="rules">
      <!-- <div class="tip">每天最多填写 {{ taskDetail.dailyManhourLimit }} 小时工时</div> -->
      <el-form-item label="之前消耗" prop="previousConsume">
        <span class="font-bold">{{ previousConsumeHour }}</span> &nbsp;&nbsp;小时
      </el-form-item>
      <el-form-item label="本次消耗" prop="consumeHour">
        <el-input
          class="inline! w-50px!"
          @input="(v) => (formData.consumeHour = v.replace(/[^\d.]/g, ''))"
          v-model="formData.consumeHour"
        />
        &nbsp;&nbsp;小时
      </el-form-item>
      <el-form-item label="工作内容" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
        ></el-input>
      </el-form-item>
      <el-form-item label="实际结束" prop="actualEndDate">
        <el-date-picker
          :disabled-date="disabledDate"
          clearable
          type="datetime"
          v-model="formData.actualEndDate"
          value-format="x"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="文件" prop="fileUrls">
        <UploadFile
          v-model="formData.fileUrls"
          :file-size="5120"
          :limit="5"
          returnType="array"
          @uploading="handleUploading"
        />
      </el-form-item>
      <el-form-item label="成果" prop="resultUrls">
        <UploadFile
          v-model="formData.resultUrls"
          :file-size="5120"
          :limit="5"
          returnType="array"
          @uploading="handleUploading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="formLoading || isUploading" type="primary" @click="save"
          >确 定</el-button
        >
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ProjectTaskManhourApi } from '@/api/pms/taskHour'
import { useI18n } from 'vue-i18n'

const formLoading = ref(false)
const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const formRef = ref()
const rules = reactive({
  consumeHour: [{ required: true, message: '本次消耗不能为空', trigger: 'blur' }],
  actualEndDate: [{ required: true, message: '实际结束不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '工作内容不能为空', trigger: 'blur' }]
})

const message = useMessage()
const props = defineProps({
  taskDetail: {
    type: Object,
    default: () => ({})
  },
  previousConsumeHour: {
    type: Number,
    default: 0
  }
})
const dialogVisible = ref(false)
const formData: any = ref({
  consumeHour: null,
  actualEndDate: new Date().getTime(),
  fileUrls: [],
  resultUrls: [],
  remark: ''
})

//** 弹框打开事件 */
const openDialog = () => {
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  reset()
  dialogVisible.value = false
}
const save = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return
    formLoading.value = true
    if (new Date(formData.value.actualEndDate).getTime() > props.taskDetail.mustBeCompletedDate) {
      return message.warning('实际结束时间不能晚于计划完成日期')
    }
    try {
      let submitData: any = [
        {
          ...formData.value,
          projectId: props.taskDetail.projectId,
          taskId: props.taskDetail.id,
          consumeDate: formData.value.actualEndDate
        }
      ]
      await ProjectTaskManhourApi.createProjectTaskManhour(submitData)
      message.success(t('common.createSuccess'))
      // 关闭当前 Tab
      emit('fetch-data')
      close()
    } finally {
      formLoading.value = false
    }
  })
}

const reset = () => {
  formRef.value.resetFields()
  formData.value = {
    consumeHour: null,
    actualEndDate: new Date().getTime(),
    fileUrls: [],
    resultUrls: [],
    remark: ''
  }
}

const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.tip {
  margin-left: 10px;
  color: #f56c6c;
  padding-bottom: 10px;
  font-weight: bold;
  font-size: 14px;
}
</style>
