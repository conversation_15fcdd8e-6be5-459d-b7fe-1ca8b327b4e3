<!--
 * @Description: 待办流程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-15 16:14:26
 * @LastEditTime: 2025-03-31 14:50:00
-->

<template>
  <el-table v-loading="loading" :data="tableData">
    <el-table-column align="center" label="操作">
      <template #default="scope">
        <el-button link type="primary" @click="handleAudit(scope.row)">审批进度</el-button>
      </template>
    </el-table-column>
    <el-table-column align="center" label="任务名称" prop="name" />
    <el-table-column align="center" label="所属流程" prop="processInstance.name" />
    <el-table-column align="center" label="发起人" prop="processInstance.startUserNickname" />
    <el-table-column
      :formatter="dateFormatter"
      align="center"
      label="创建时间"
      prop="createTime"
      width="180"
    />
    <!-- <el-table-column label="任务状态" prop="suspensionState">
      <template #default="scope">
        <el-tag v-if="scope.row.suspensionState === 1" type="success">激活</el-tag>
        <el-tag v-if="scope.row.suspensionState === 2" type="warning">挂起</el-tag>
      </template>
    </el-table-column> -->
  </el-table>
  <!-- 分页 -->
  <Pagination
    v-model:limit="queryParams.pageSize"
    v-model:page="queryParams.pageNo"
    :total="total"
    @pagination="getList"
  />
</template>
<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'

defineOptions({ name: 'PendingFlow' })
const total = ref(0) // 列表的总页数
const tableData = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const { push } = useRouter() // 路由
const emit = defineEmits(['pushToDetail'])
const loading = ref(false)

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTodoTaskPage(queryParams)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}
getList()
</script>

<style lang="scss" scoped></style>
