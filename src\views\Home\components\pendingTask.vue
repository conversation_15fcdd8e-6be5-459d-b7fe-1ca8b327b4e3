<!--
 * @Description: 待处理任务列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-14 17:15:23
 * @LastEditTime: 2024-05-31 10:38:56
-->
<template>
  <el-table v-loading="loading" :data="tableData" :border="false">
    <el-table-column align="center" label="任务名称" prop="describeVal" width="250">
      <template #default="scope">
        <div class="listLink" @click="handleDetail(scope.row)">
          {{ scope.row.describeVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column align="center" label="项目名称" prop="projectName" />
    <el-table-column
      align="center"
      label="预计开始日期"
      prop="plannedStartDate"
      :formatter="dateFormatter2"
      width="120"
    />
    <el-table-column
      align="center"
      label="预计截止日期"
      prop="plannedEndDate"
      :formatter="dateFormatter2"
      width="120"
    />
    <el-table-column label="任务状态" prop="customerName" width="100">
      <template #default="scope">
        <dict-tag :type="DICT_TYPE.TASK_STATUS" :value="scope.row.customerName" />
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <Pagination
    v-model:limit="queryParams.pageSize"
    v-model:page="queryParams.pageNo"
    :total="total"
    @pagination="getList"
  />
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { getMyTaskPage } from '@/api/bpm/task'

defineOptions({ name: 'PendingTask' })
const emit = defineEmits(['pushToDetail'])
const loading = ref(false)
const total = ref(0) // 列表的总页数
const tableData = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getMyTaskPage(queryParams)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleDetail = (row) => {
  emit('pushToDetail', row)
}
getList()
</script>

<style lang="scss" scoped></style>
