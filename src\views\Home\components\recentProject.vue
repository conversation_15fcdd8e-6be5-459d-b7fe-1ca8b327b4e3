<!--
 * @Description: 首页-近期参与的项目模块
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-14 17:07:49
 * @LastEditTime: 2025-06-13 13:39:32
-->
<template>
  <div class="project-grid">
    <div
      v-if="projectList.length > 0"
      class="project-card"
      v-for="item in projectList"
      @click="pushToDetail(item)"
    >
      <div class="card-header">
        <div class="project-icon">
          <Icon icon="ep:folder" />
        </div>
        <div class="project-info">
          <div class="project-name" :title="item.projectName">{{ item.projectName }}</div>
          <div class="customer-name" :title="item.customerName">{{ item.customerName }}</div>
        </div>
      </div>
      <div class="card-footer">
        <div class="completion-date">
          <Icon icon="ep:calendar" class="date-icon" />
          <span>预计完成：{{ dayjs(item.mustBeCompletedDate).format('YYYY-MM-DD') }}</span>
        </div>
        <div class="view-details">
          <span>查看详情</span>
          <Icon icon="ep:arrow-right" class="arrow-icon" />
        </div>
      </div>
    </div>
    <el-empty v-else class="empty-state"></el-empty>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { getMyProjectPage } from '@/api/bpm/task'
defineOptions({ name: 'RecentProject' })

const projectList = ref<any>([])
const { push } = useRouter()
const route = useRoute()

const getList = async () => {
  const data = await getMyProjectPage({
    pageSize: 2,
    pageNo: 1
  })
  projectList.value = data.list
}

const pushToDetail = (item) => {
  push({
    path: '/project/projectTask',
    query: {
      projectId: item.id,
      backPath: route.path
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.project-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
}

.project-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(79, 70, 229, 0.08);
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex: 1;
  min-width: 280px;
  max-width: 350px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5 0%, #3730a3 100%);
    border-radius: 16px 16px 0 0;
  }
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.12);
    border-color: rgba(79, 70, 229, 0.2);
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    
    .project-icon {
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      transform: scale(1.1);
    }
    
    .view-details {
      color: #4f46e5;
      
      .arrow-icon {
        transform: translateX(4px);
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    
    .project-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 12px;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      transition: all 0.3s ease;
      flex-shrink: 0;
    }
    
    .project-info {
      flex: 1;
      min-width: 0;
      
      .project-name {
        font-size: 16px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 6px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .customer-name {
        font-size: 14px;
        font-weight: 500;
        color: #64748b;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
  
  .card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .completion-date {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #6b7280;
      
      .date-icon {
        font-size: 14px;
        margin-right: 6px;
        color: #f59e0b;
      }
    }
    
    .view-details {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 600;
      color: #6b7280;
      transition: all 0.3s ease;
      
      .arrow-icon {
        font-size: 12px;
        margin-left: 4px;
        transition: transform 0.3s ease;
      }
    }
  }
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .project-card {
    padding: 16px;
    
    .card-header {
      .project-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
      }
      
      .project-info {
        .project-name {
          font-size: 15px;
        }
        
        .customer-name {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
