<template>
  <div class="homepage-container">
    <!-- 快捷入口区域 -->
    <div class="quick-access-card">
      <div class="card-header">
        <div class="header-icon">
          <Icon icon="ep:star" />
        </div>
        <h2 class="card-title">快捷入口</h2>
      </div>
      <el-skeleton :loading="loading" animated>
        <div class="quick-access-grid" v-if="menuList.length">
          <div
            class="quick-access-item"
            @click="jumpTo(item)"
            v-for="(item, index) in menuList"
            :key="index"
          >
            <div class="item-icon-wrapper">
              <img
                v-if="item.meta?.customizeIcon"
                class="item-icon"
                :src="item.meta.customizeIcon"
                alt=""
              />
              <div v-else class="default-icon">
                <Icon icon="ep:menu" />
              </div>
            </div>
            <div class="item-title">{{ item.meta.title }}</div>
          </div>
        </div>
        <el-empty class="empty-state" :image-size="70" v-else></el-empty>
      </el-skeleton>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容 -->
      <div class="left-content">
        <!-- 近期项目 -->
        <div class="content-card project-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon project-icon">
                <Icon icon="ep:folder" />
              </div>
              <h3 class="card-title">我近期参与的项目</h3>
            </div>
            <el-button type="primary" link @click="pushToProject" class="more-link">
              全部项目 <Icon icon="ep:arrow-right" class="ml-1" />
            </el-button>
          </div>
          <el-skeleton :loading="loading" animated>
            <RecentProject v-if="!loading" />
          </el-skeleton>
        </div>

        <!-- 待处理任务 -->
        <div class="content-card task-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon task-icon">
                <Icon icon="ep:clock" />
              </div>
              <h3 class="card-title">待处理任务</h3>
            </div>
          </div>
          <el-skeleton :loading="loading" animated>
            <PendingTask @pushToDetail="updateCurrentView" />
          </el-skeleton>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 待办流程 -->
        <div class="content-card flow-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon flow-icon">
                <Icon icon="ep:document" />
              </div>
              <h3 class="card-title">待办流程</h3>
            </div>
            <el-button type="primary" link @click="pushToTodo" class="more-link">
              更多 <Icon icon="ep:arrow-right" class="ml-1" />
            </el-button>
          </div>
          <el-skeleton :loading="loading" animated>
            <PendingFlow />
          </el-skeleton>
        </div>

        <!-- 通知公告 -->
        <div class="content-card notice-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon notice-icon">
                <Icon icon="ep:bell" />
              </div>
              <h3 class="card-title">{{ t('workplace.notice') }}</h3>
            </div>
          </div>
          <el-skeleton :loading="loading" animated>
            <div class="notice-list">
              <div 
                v-for="(item, index) in notice" 
                :key="`dynamics-${index}`"
                class="notice-item"
                @click="openNoticeDetail(item.id)"
              >
                <div class="notice-avatar">
                  <img :src="avatar" alt="" />
                </div>
                <div class="notice-content">
                  <div class="notice-title">
                    <Highlight
                      :keys="getIntDictOptions(DICT_TYPE.SYSTEM_NOTICE_TYPE).map((v) => v.label)"
                    >
                      {{ getDictLabel(DICT_TYPE.SYSTEM_NOTICE_TYPE, item.type) }} :
                      {{ item.title }}
                    </Highlight>
                  </div>
                  <div class="notice-date">
                    {{ formatTime(item.createTime, 'yyyy-MM-dd') }}
                  </div>
                </div>
              </div>
            </div>
          </el-skeleton>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 通知公告详情弹窗 -->
  <NoticeDetail ref="noticeDetailRef" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import RecentProject from './components/recentProject.vue'
import PendingTask from './components/pendingTask.vue'
import PendingFlow from './components/pendingFlow.vue'
import NoticeDetail from './components/NoticeDetail.vue'
import { formatTime } from '@/utils'
import { isArray } from '@/utils/is'
import { useUserStore } from '@/store/modules/user'
import avatarImg from '@/assets/imgs/avatar.png'
import * as NoticeApi from '@/api/system/notice'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { isUrl } from '@/utils/is'

defineOptions({ name: 'HomePage' })

const props = defineProps({
  row: {
    type: Object,
    default: () => ({})
  }
})
const permissionStore = usePermissionStoreWithOut()
const emit = defineEmits(['updateCurrentView'])
const { push } = useRouter() // 路由
const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(true)
const avatar = userStore.getUser.avatar ? `${userStore.getUser.avatar}` : avatarImg

// 获取通知公告
const notice = ref<NoticeApi.NoticeVO[]>([])
const getNotice = async () => {
  try {
    const data = await NoticeApi.getNoticePage({ pageNo: 1, pageSize: 5 })
    notice.value = data.list
  } finally {
  }
}

const updateCurrentView = (row) => {
  push({
    path: '/user/taskDetail',
    query: {
      taskId: row.id,
      projectId: row.projectId
    }
  })
}

const pushToNotice = () => {
  push('/system/notice')
}

const pushToProject = () => {
  push('/project/projectPlan')
}

const pushToTodo = () => {
  push('/bpm/task/todo')
}

/* ==================== 快捷入口模块相关逻辑 ==================== */
const menuList = ref<any>([])
const filterMenuList = (items) => {
  return items.reduce((acc, item) => {
    // 检查customizeIcon是否存在
    if (item.meta?.customizeIcon) {
      // 将符合条件的对象添加到结果中
      acc.push({ ...item })
    } else if (isArray(item.children) && item.children.length > 0) {
      const filteredChildren = filterMenuList(item.children)
      if (filteredChildren.length > 0) {
        acc = [...acc, ...filteredChildren]
      }
    }
    return acc
  }, [])
}

const jumpTo = (item) => {
  // 自定义事件
  if (isUrl(item.path)) {
    // 跳转学习平台特殊处理
    const userInfo = userStore.getUser
    const userName = userInfo.id === 1 ? 'baike_admin' : userInfo.nickname
    if (item.path.includes('eduuser') || item.path.includes('eduadmin')) {
      window.open(
        `${item.path}?username=${userName}&tenantName=柏科&appId=6645cbae-27d5-11ec-805b-525400b8bd2d&tenantCode=baike&abc=123`
      )
    } else {
      window.open(item.path)
    }
  } else {
    push({ name: item.name })
  }
}

const getAllApi = async () => {
  await Promise.all([getNotice()])
  loading.value = false
}

onMounted(() => {
  getAllApi()
  menuList.value = filterMenuList(permissionStore.routers)
})

// 通知公告详情弹窗
const noticeDetailRef = ref()
const openNoticeDetail = (id: number | undefined) => {
  if (id) {
    noticeDetailRef.value?.open(id)
  }
}
</script>

<style lang="scss" scoped>
.homepage-container {
  padding: 10px 20px 20px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* 快捷入口卡片 */
.quick-access-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(79, 70, 229, 0.08), 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 24px;
  margin-bottom: 10px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .header-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 12px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    .card-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }
  
  .quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    
    .quick-access-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 16px;
      border-radius: 16px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-color: #4f46e5;
      }
      
      .item-icon-wrapper {
        margin-bottom: 12px;
        
        .item-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          object-fit: cover;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .default-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
      }
      
      .item-title {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        text-align: center;
        line-height: 1.4;
      }
    }
  }
  
  .empty-state {
    padding: 40px 20px;
  }
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 10px;
  
  .left-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 60%;
  }
  
  .right-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 40%;
  }
}

/* 内容卡片通用样式 */
.content-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .header-icon {
        width: 36px;
        height: 36px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        margin-right: 12px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        
        &.project-icon {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        &.task-icon {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        &.flow-icon {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        
        &.notice-icon {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
      }
      
      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
      }
    }
    
    .more-link {
      font-weight: 600;
      font-size: 14px;
      color: #4f46e5;
      transition: all 0.2s ease;
      
      &:hover {
        color: #3730a3;
        transform: translateX(2px);
      }
    }
  }
}

/* 通知列表样式 */
.notice-list {
  .notice-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    
    &:hover {
      background: #f8fafc;
      padding-left: 8px;
      padding-right: 8px;
      margin: 0 -8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    &:last-child {
      border-bottom: none;
    }
    
    .notice-avatar {
      margin-right: 16px;
      flex-shrink: 0;
      
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid #e2e8f0;
        object-fit: cover;
      }
    }
    
    .notice-content {
      flex: 1;
      
      .notice-title {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        line-height: 1.5;
        margin-bottom: 6px;
        transition: color 0.2s ease;
      }
      
      .notice-date {
        font-size: 12px;
        color: #6b7280;
        font-weight: 400;
      }
    }
    
    &:hover .notice-content .notice-title {
      color: #4f46e5;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    
    .right-content {
      order: -1;
    }
  }
}

@media (max-width: 768px) {
  .homepage-container {
    padding: 16px;
  }
  
  .quick-access-card {
    padding: 20px;
    
    .quick-access-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 16px;
      
      .quick-access-item {
        padding: 16px 12px;
        
        .item-icon-wrapper {
          .item-icon,
          .default-icon {
            width: 50px;
            height: 50px;
          }
        }
      }
    }
  }
  
  .content-card {
    padding: 20px;
    
    .card-header {
      .header-left {
        .card-title {
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .homepage-container {
    padding: 12px;
  }
  
  .quick-access-card {
    padding: 16px;
    
    .quick-access-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }
  }
  
  .content-card {
    padding: 16px;
  }
}
</style>
