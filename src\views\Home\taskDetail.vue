<!--
 * @Description: 项目/任务详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-15 09:08:22
 * @LastEditTime: 2025-03-28 15:13:49
-->
<template>
  <div class="pt-2">
    <div class="detail-header">
      <el-button type="primary" @click="handleBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>

      <div class="task-name">
        {{ type === 'project' ? taskDetail.projectName : taskDetail.describeVal }}
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="17">
        <el-card shadow="hover">
          <div class="card-container">
            <div class="card-title">{{ type === 'project' ? '项目' : '任务' }}描述 </div>
            <div class="card-text">
              {{ type === 'project' ? taskDetail.projectName : taskDetail.describeVal }}
            </div>
          </div>
        </el-card>
        <el-card shadow="hover">
          <div class="card-container">
            <div class="card-title">任务备注 </div>
            <div class="card-text">
              {{ taskDetail.remark }}
            </div>
          </div>
        </el-card>
        <el-card shadow="hover">
          <div class="card-container">
            <div class="card-title">历史记录 </div>
            <div
              v-if="tableData.length > 0"
              class="history-item card-text"
              v-for="(item, index) in tableData"
            >
              <div class="history-head">
                {{ index + 1 }}.填报人:<span class="font-bold mx-5px"> {{ item.userName }} </span>，
                {{ item.completeFlag === '1' ? '完成' : '填报工时' }}:
                <span class="font-bold mx-5px"> {{ item.consumeHour }} </span> 小时，
                发生日期：<span class="font-bold">
                  {{ dayjs(item.consumeDate).format('YYYY-MM-DD') }}
                </span>
                ， 填报时间：
                <span class="font-bold">
                  {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                </span>

                <div
                  class="operation-container"
                  v-if="type !== 'project' && userId === item.userId"
                >
                  <i class="iconfont icon-icon-edit" @click="handleEdit(item)" />
                  <!-- <i class="iconfont icon-cuowu1" @click="handleDelete(item)" /> -->
                </div>
              </div>
              <div class="history-remark" v-if="item.remark">{{ item.remark }}</div>
            </div>
            <el-empty v-else></el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div class="main-actions" v-if="type !== 'project' && taskDetail.customerName !== '20'">
      <div class="btn-toolbar">
        <el-button icon="Flag" @click="addDailyLog">日志</el-button>
        <el-button icon="CircleCheck" @click="handleFinish"> 完成 </el-button>
      </div>
    </div>
    <!-- 日志弹窗 -->
    <DailyLogDialog
      ref="dailyLogDialogRef"
      :tableData="tableData"
      :taskDetail="taskDetail"
      @fetch-data="getHistoryList"
    />
    <!-- 完成任务弹窗 -->
    <FinishDialog
      ref="finishDialogRef"
      :previousConsumeHour="previousConsumeHour"
      :taskDetail="taskDetail"
      @fetch-data="getList"
    />
    <!-- 修改日志弹窗 -->
    <EditLogDialog ref="editLogDialogRef" :taskDetail="taskDetail" @fetch-data="getHistoryList" />
  </div>
</template>
<script lang="ts" setup>
import { TaskApi } from '@/api/project/projectTask'
import { ProjectApi } from '@/api/project/applyProject'
import dayjs from 'dayjs'
import { ProjectTaskManhourApi } from '@/api/pms/taskHour'
import DailyLogDialog from './components/dailyLogDialog.vue'
import FinishDialog from './components/finishDialog.vue'
import EditLogDialog from './components/editLogDialog.vue'
import { useUserStore } from '@/store/modules/user'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'TaskDetail' })

const emit = defineEmits(['updateCurrentView'])
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const { projectId, taskId, backPath, backQuery, type } = route.query

const { t } = useI18n() // 国际化
const message = useMessage()
const userId = useUserStore().getUser.id // 当前登录的编号
const taskDetail = ref<any>({})
const dailyLogDialogRef = ref()
const editLogDialogRef = ref()
const finishDialogRef = ref()

const addDailyLog = () => {
  dailyLogDialogRef.value.openDialog()
}
const handleEdit = (item) => {
  editLogDialogRef.value.openDialog(item)
}
const handleFinish = () => {
  finishDialogRef.value.openDialog()
}

const tableData = ref<any>([])
const getHistoryList = async () => {
  let queryData = {
    pageNo: 1,
    pageSize: 999,
    projectId,
    taskId: type === 'project' ? undefined : taskId
  }
  const data = await ProjectTaskManhourApi.getProjectTaskManhourPage(queryData)
  tableData.value = data.list.reverse()
}

const getTaskDetail = async () => {
  let data
  if (type === 'project') {
    data = await ProjectApi.getProject(projectId as any)
  } else {
    data = await TaskApi.getTask(taskId as any)
  }
  taskDetail.value = data
}

// 当前任务 当前登录人 之前总消耗工时
const previousConsumeHour = computed(() => {
  return tableData.value
    .filter((item) => item.userId === userId)
    .reduce((total, item) => {
      return total + item.consumeHour
    }, 0)
})

const getList = () => {
  getHistoryList()
  getTaskDetail()
}

const handleBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  if (backQuery) {
    push({
      path: backPath as string,
      query: JSON.parse(backQuery as any)
    })
  } else {
    push((backPath || '/index') as string)
  }
}

const handleDelete = async (item) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjectTaskManhourApi.deleteProjectTaskManhour(item.id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getHistoryList()
  } catch {}
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.page-bg {
  background: #f8fafc;
  min-height: 100vh;
  padding: 32px 0 0 0;
}
.detail-header {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102,126,234,0.10);
  padding: 28px 40px 18px 40px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 32px;
  .el-button {
    border-radius: 8px;
    font-weight: 500;
    height: 40px;
    font-size: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: #fff;
    &:hover {
      box-shadow: 0 4px 12px rgba(102,126,234,0.18);
      transform: translateY(-1px);
    }
  }
  .task-name {
    font-size: 24px;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-left: 32px;
    letter-spacing: 1px;
  }
}
:deep(.el-card) {
  margin-bottom: 18px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.08);
  border: none;
}
.card-container {
  display: flex;
  flex-direction: column;
  padding: 18px 8px 8px 8px;
  .card-title {
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 17px;
    color: #667eea;
    letter-spacing: 0.5px;
  }
  .card-text {
    font-size: 15px;
    margin-bottom: 8px;
    color: #334155;
    line-height: 1.7;
  }
  .history-item {
    background: #f8fafc;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(102,126,234,0.06);
    margin-bottom: 14px;
    padding: 16px 18px 10px 18px;
    transition: box-shadow 0.2s, background 0.2s;
    border: 1px solid #e2e8f0;
    &:hover {
      background: #eef2ff;
      box-shadow: 0 4px 16px rgba(102,126,234,0.10);
      border-color: #a5b4fc;
    }
    .history-head {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      font-size: 15px;
      .font-bold {
        color: #764ba2;
        font-weight: 700;
      }
      .operation-container {
        margin-left: auto;
        display: flex;
        gap: 8px;
        .iconfont {
          margin-left: 10px;
          cursor: pointer;
          font-size: 20px;
          transition: color 0.2s;
        }
        .icon-icon-edit {
          color: #0096c7;
          &:hover { color: #3b82f6; }
        }
        .icon-cuowu1 {
          color: #f56c6c;
          &:hover { color: #b91c1c; }
        }
      }
    }
    .history-remark {
      margin: 8px 0 0 0;
      padding: 10px 16px;
      background-color: #f3f4f6;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      color: #64748b;
      font-size: 14px;
    }
  }
}
.main-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 32px;
  width: 100vw;
  display: flex;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
  .btn-toolbar {
    display: flex;
    gap: 18px;
    background: #fff;
    box-shadow: 0 4px 16px rgba(102,126,234,0.10);
    border-radius: 16px;
    padding: 10px 36px;
    pointer-events: auto;
    .el-button {
      border-radius: 8px;
      font-weight: 600;
      font-size: 15px;
      min-width: 100px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      border: none;
      transition: box-shadow 0.2s, transform 0.2s;
      &:hover {
        box-shadow: 0 4px 12px rgba(102,126,234,0.18);
        transform: translateY(-1px);
      }
    }
  }
}
@media (max-width: 900px) {
  .page-bg {
    padding: 8px 0 0 0;
  }
  .detail-header {
    flex-direction: column;
    gap: 12px;
    padding: 18px 10px 10px 10px;
    .task-name {
      font-size: 18px;
      margin-left: 0;
    }
  }
  :deep(.el-card) {
    margin-bottom: 10px;
    border-radius: 10px;
  }
  .card-container {
    padding: 10px 4px 4px 4px;
    .card-title {
      font-size: 15px;
    }
    .card-text {
      font-size: 13px;
    }
  }
  .main-actions {
    bottom: 12px;
    .btn-toolbar {
      padding: 8px 8px;
      border-radius: 10px;
      gap: 8px;
      .el-button {
        min-width: 80px;
        font-size: 13px;
      }
    }
  }
}
</style>
