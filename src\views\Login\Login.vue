<template>
  <div :class="prefixCls" class="fixed inset-0 overflow-hidden">
    <div class="absolute right-8 top-8 flex items-center space-x-4 z-10">
      <ThemeSwitch />
      <LocaleDropdown />
    </div>

    <div class="h-full">
      <!-- 左侧装饰区域 -->
      <div
        class="h-full w-3/5 fixed left-0 top-0 bg-gradient-to-br from-blue-500 to-cyan-400 lt-xl:hidden"
      >
        <!-- <div class="absolute text-white text-4xl font-bold left-5 top-5">
          {{ appStore.getTitle }}
        </div> -->
        <div class="relative h-full flex flex-col justify-between p-12">
          <!-- 左上角logo -->
          <div class="flex items-center space-x-4">
            <span class="text-4xl font-bold text-white">
              {{ appStore.getTitle }}
            </span>
          </div>

          <!-- 中间标语 -->
          <div class="flex flex-col items-center text-white">
            <TransitionGroup
              appear
              enter-active-class="animate__animated animate__fadeInUp"
              tag="div"
              class="text-center"
            >
              <!-- <h1 key="title" class="text-4xl font-bold mb-6">柏科经营管理平台-QEHS体系落地</h1> -->
              <!-- <p key="subtitle" class="text-xl opacity-80">科技赋能安全，智慧守护未来</p> -->
            </TransitionGroup>
          </div>

          <!-- 底部装饰图 -->
          <!-- <div class="flex justify-center">
            <TransitionGroup
              appear
              enter-active-class="animate__animated animate__fadeInUp"
              tag="div"
            >
              <img
                key="decoration"
                src="@/assets/svgs/login-box-bg.svg"
                alt=""
                class="w-96 opacity-80"
              />
            </TransitionGroup>
          </div> -->
        </div>
      </div>

      <!-- 右侧登录区域 -->
      <div
        class="right-bg h-full w-2/5 fixed right-0 top-0 flex items-center justify-center bg-gray-50 dark:bg-[var(--login-bg-color)] p-8 lt-xl:w-full"
      >
        <div class="w-full max-w-md">
          <!-- 移动端logo -->
          <div class="text-center mb-8 xl:hidden">
            <h2 class="text-xl font-bold text-gray-800 dark:text-white">
              {{ underlineToHump(appStore.getTitle) }}
            </h2>
          </div>

          <!-- 登录表单 -->
          <Transition appear enter-active-class="animate__animated animate__fadeIn">
            <div
              class="bg-#DEEFFF opacity-90 dark:bg-[var(--el-bg-color)] rounded-lg shadow-lg px-4 py-10 pb-14 border-solid border-2 border-#fff"
            >
              <LoginForm />
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { underlineToHump } from '@/utils'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { ThemeSwitch } from '@/layout/components/ThemeSwitch'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown'
import { LoginForm } from './components'

defineOptions({ name: 'Login' })

const { t } = useI18n()
const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.#{$prefix-cls} {
  background-color: var(--el-bg-color);
}
.bg-gradient-to-br {
  background-image: url('@/assets/imgs/login-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.right-bg {
  background-image: url('@/assets/imgs/login-bg2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
:deep(.el-input__wrapper) {
  background-color: var(--el-fill-color-blank);
  box-shadow: 0 0 0 1px var(--el-border-color) inset;

  &:hover {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-button--primary) {
  background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-light-3));
  border: none;

  &:hover {
    background: linear-gradient(to right, var(--el-color-primary-dark-2), var(--el-color-primary));
  }
}
</style>
