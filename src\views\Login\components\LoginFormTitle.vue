<!--
 * @Description: LoginFormTitle
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:51
 * @LastEditTime: 2025-03-31 14:14:06
-->
<template>
  <h2 class="enter-x mb-3 text-center text-2xl xl:text-center xl:text-3xl">
    <div class="mb-1">欢迎登录</div>
    <div class="text-center text-sm">WELCOME</div>
  </h2>
</template>
<script lang="ts" setup>
import { LoginStateEnum, useLoginState } from './useLogin'

defineOptions({ name: 'LoginFormTitle' })

const { t } = useI18n()

const { getLoginState } = useLoginState()

const getFormTitle = computed(() => {
  const titleObj = {
    [LoginStateEnum.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
    [LoginStateEnum.LOGIN]: t('sys.login.signInFormTitle'),
    [LoginStateEnum.REGISTER]: t('sys.login.signUpFormTitle'),
    [LoginStateEnum.MOBILE]: t('sys.login.mobileSignInFormTitle'),
    [LoginStateEnum.QR_CODE]: t('sys.login.qrSignInFormTitle'),
    [LoginStateEnum.SSO]: t('sys.login.ssoFormTitle')
  }
  return titleObj[unref(getLoginState)]
})
</script>
