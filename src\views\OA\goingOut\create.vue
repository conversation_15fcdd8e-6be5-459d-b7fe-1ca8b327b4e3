<!--
 * @Description: 外出申请新增页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-25 11:22:44
 * @LastEditTime: 2024-07-25 13:31:55
-->

<template>
  <div class="going-out-create">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <svg-icon name="ep:calendar" />
        </div>
        <div class="header-text">
          <h1>外出申请</h1>
          <p>请填写外出申请信息，提交后将进入审批流程</p>
        </div>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="160px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 时间信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:clock" />
            </div>
            <h3>时间安排</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startTime">
                  <el-date-picker
                    class="modern-date-picker"
                    v-model="formData.startTime"
                    type="datetime"
                    value-format="x"
                    placeholder="选择开始时间"
                    :disabled-date="disablePastDates"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker
                    class="modern-date-picker"
                    v-model="formData.endTime"
                    type="datetime"
                    value-format="x"
                    placeholder="选择结束时间"
                    :disabled-date="disablePastDates"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="time-duration" v-if="formData.startTime && formData.endTime">
              <div class="duration-info">
                <svg-icon name="ep:timer" class="duration-icon" />
                <span>预计外出时长：{{ calculateDuration() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 外出信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:location" />
            </div>
            <h3>外出信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="外出事由" prop="reason">
                  <el-input 
                    v-model="formData.reason" 
                    placeholder="请详细描述外出事由"
                    class="modern-input"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 5 }"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="外出地址" prop="location">
                  <el-input 
                    v-model="formData.location" 
                    placeholder="请输入公司名称或外出地址"
                    class="modern-input"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 联系信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:phone" />
            </div>
            <h3>联系信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contact">
                  <el-input 
                    v-model="formData.contact" 
                    placeholder="请输入联系人姓名"
                    class="modern-input"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input 
                    v-model="formData.contactPhone" 
                    placeholder="请输入联系电话"
                    class="modern-input"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 备注信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:edit" />
            </div>
            <h3>备注信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6 }"
                    v-model="formData.remark"
                    placeholder="请输入备注信息（可选）"
                    class="modern-textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 提交按钮区域 -->
        <div class="form-actions">
          <el-button @click="handleClose" class="cancel-btn">
            <svg-icon name="ep:close" class="btn-icon" />
            取 消
          </el-button>
          <el-button @click="submitForm" type="primary" :disabled="formLoading" class="submit-btn">
            <svg-icon name="ep:check" class="btn-icon" />
            提 交 申 请
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { OutApplyApi } from '@/api/OA/goingOut'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isArray } from '@/utils/is'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'GoingOutCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const formData = ref<any>({
  id: undefined,
  userId: undefined,
  userName: undefined,
  deptId: undefined,
  deptName: undefined,
  applyDate: undefined,
  startTime: undefined,
  endTime: undefined,
  reason: undefined,
  location: undefined,
  contact: undefined,
  contactPhone: undefined,
  attachment: undefined,
  remark: undefined
})

const formRules = reactive({
  startTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
  reason: [{ required: true, message: '外出事由不能为空', trigger: 'blur' }],
  location: [{ required: true, message: '外出地址不能为空', trigger: 'blur' }],
  contact: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

// 禁用过去的日期
const disablePastDates = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000 // 禁用昨天及之前的日期
}

// 计算外出时长
const calculateDuration = () => {
  if (!formData.value.startTime || !formData.value.endTime) return ''
  
  const start = new Date(formData.value.startTime)
  const end = new Date(formData.value.endTime)
  const diff = end.getTime() - start.getTime()
  
  if (diff <= 0) return '时间设置有误'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else {
    return `${minutes}分钟`
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 验证时间逻辑
  if (formData.value.startTime && formData.value.endTime) {
    if (formData.value.startTime >= formData.value.endTime) {
      message.error('结束时间必须晚于开始时间')
      return
    }
  }
  
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    const data = cloneDeep(formData.value)
    delete data.id
    await OutApplyApi.createOutApply(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  if (route.query.id) {
    formData.value = await OutApplyApi.getOutApply(route.query.id as any)
  }
})
</script>

<style lang="scss" scoped>
.going-out-create {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.page-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .header-content {
    display: flex;
    align-items: center;
    
    .header-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      
      .svg-icon {
        width: 32px;
        height: 32px;
        color: white;
      }
    }
    
    .header-text {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      p {
        margin: 0;
        font-size: 16px;
        color: #64748b;
        line-height: 1.5;
      }
    }
  }
}

.form-container {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.modern-form {
  .form-section {
    margin-bottom: 40px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    .section-header {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      
      .section-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
    
    .section-content {
      padding: 24px;
      
      .el-row {
        .el-col {
          margin-bottom: 8px;
        }
      }
      
      .time-duration {
        margin-top: 16px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
        border-radius: 8px;
        border-left: 4px solid #667eea;
        
        .duration-info {
          display: flex;
          align-items: center;
          color: #1e293b;
          font-weight: 500;
          
          .duration-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #667eea;
          }
        }
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #475569;
      font-size: 14px;
    }
    
    .el-form-item__content {
      .modern-input {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
        
        .el-input__inner {
          font-size: 14px;
          color: #1e293b;
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
      
      .modern-date-picker {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-textarea {
        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          font-size: 14px;
          color: #1e293b;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
  
  .cancel-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid #d1d5db;
    color: #475569;
    background: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      border-color: #94a3b8;
      color: #1e293b;
      background: #f8fafc;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
  
  .submit-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .going-out-create {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px;
    
    .header-content {
      flex-direction: column;
      text-align: center;
      
      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
      
      .header-text {
        h1 {
          font-size: 24px;
        }
        
        p {
          font-size: 14px;
        }
      }
    }
  }
  
  .form-container {
    padding: 24px;
  }
  
  .modern-form {
    .form-section {
      .section-header {
        padding: 16px 20px;
        
        .section-icon {
          width: 28px;
          height: 28px;
          
          .svg-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        h3 {
          font-size: 15px;
        }
      }
      
      .section-content {
        padding: 20px;
      }
    }
  }
  
  .form-actions {
    flex-direction: column;
    
    .cancel-btn,
    .submit-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
