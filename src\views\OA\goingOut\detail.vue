<!--
 * @Description: 外出申请单详情
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-25 11:22:44
 * @LastEditTime: 2024-07-25 13:30:41
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="所属部门">
        {{ detailData.deptName }}
      </el-descriptions-item>
      <el-descriptions-item label="申请日期">
        {{ formatDate(detailData.applyDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="开始日期">
        {{ formatDate(detailData.startTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="结束日期">
        {{ formatDate(detailData.endTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="外出事由">
        {{ detailData.reason }}
      </el-descriptions-item>
      <el-descriptions-item label="公司名称、外出地址">
        {{ detailData.location }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人">
        {{ detailData.contact }}
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">
        {{ detailData.contactPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { OutApplyApi } from '@/api/OA/goingOut'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'GoingOutDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await OutApplyApi.getOutApply(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
