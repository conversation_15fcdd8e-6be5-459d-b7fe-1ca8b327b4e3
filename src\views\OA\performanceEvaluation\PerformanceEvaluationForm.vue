<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="流程ID" prop="processId">
        <el-input v-model="formData.processId" placeholder="请输入流程ID" />
      </el-form-item>
      <el-form-item label="模板ID" prop="templateId">
        <el-input v-model="formData.templateId" placeholder="请输入模板ID" />
      </el-form-item>
      <el-form-item label="自评" prop="selfEvaluation">
        <el-input v-model="formData.selfEvaluation" placeholder="请输入自评" />
      </el-form-item>
      <el-form-item label="上级评" prop="superiorEvaluation">
        <el-input v-model="formData.superiorEvaluation" placeholder="请输入上级评" />
      </el-form-item>
      <el-form-item label="领导评" prop="leaderEvaluation">
        <el-input v-model="formData.leaderEvaluation" placeholder="请输入领导评" />
      </el-form-item>
      <el-form-item label="考核日期" prop="evaluationDate">
        <el-date-picker
          v-model="formData.evaluationDate"
          type="date"
          value-format="x"
          placeholder="选择考核日期"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PerformanceEvaluationApi, PerformanceEvaluationVO } from '@/api/OA/performanceEvaluation'

/** 用户绩效评分 表单 */
defineOptions({ name: 'PerformanceEvaluationForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  processId: undefined,
  templateId: undefined,
  selfEvaluation: undefined,
  superiorEvaluation: undefined,
  leaderEvaluation: undefined,
  evaluationDate: undefined,
  remark: undefined
})
const formRules = reactive({
  processId: [{ required: true, message: '流程ID不能为空', trigger: 'blur' }],
  templateId: [{ required: true, message: '模板ID不能为空', trigger: 'blur' }],
  evaluationDate: [{ required: true, message: '考核日期不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PerformanceEvaluationApi.getPerformanceEvaluation(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PerformanceEvaluationVO
    if (formType.value === 'create') {
      await PerformanceEvaluationApi.createPerformanceEvaluation(data)
      message.success(t('common.createSuccess'))
    } else {
      await PerformanceEvaluationApi.updatePerformanceEvaluation(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    processId: undefined,
    templateId: undefined,
    selfEvaluation: undefined,
    superiorEvaluation: undefined,
    leaderEvaluation: undefined,
    evaluationDate: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
