<template>
  <el-dialog title="新签合同信息" v-model="dialogVisible" width="1240px">
    <el-table v-loading="formLoading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="合同编号" align="center" prop="contractCode" />
      <el-table-column label="合同名称" align="center" prop="contractName" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="客户签约人" align="center" prop="customerContractor" />
      <el-table-column label="签约人有效绩效比(%)" align="center" prop="signedCommissionRatio" />
      <el-table-column label="协作人" align="center" prop="collaboratorsName" />
      <el-table-column
        label="协作人有效绩效比(%)"
        align="center"
        prop="collaboratorsCommissionRatio"
      />
      <el-table-column label="回款说明" align="center" prop="details" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>
<script setup lang="ts">
import { getContractListPage } from '@/api/crm/contract'
import { dateFormatter, formatDateString } from '@/utils/formatTime'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const list = ref<[]>([]) // 列表的数据

const formRef = ref() // 表单 Ref
const total = ref(0) // 列表的总页数
let currentUserId = ref()
let currentYearMonth = ref()
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10
})

/** 打开弹窗 */
const open = async (userId, yearMonth) => {
  dialogVisible.value = true
  currentUserId.value = userId
  currentYearMonth.value = formatDateString(yearMonth)
  getList()
}

/** 查询列表 */
const getList = async () => {
  formLoading.value = true
  try {
    queryParams.userId = currentUserId.value
    queryParams.yearMonth = currentYearMonth.value
    const data = await getContractListPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    formLoading.value = false
  }
}

/** 提交表单 */
const emit = defineEmits(['fetch-data']) // 定义 success 事件，用于操作成功后的回调

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
