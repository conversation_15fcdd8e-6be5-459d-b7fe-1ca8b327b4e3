<template>
  <div class="perf-card">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="158px"
      v-loading="formLoading"
      class="perf-form"
    >
      <div class="perf-section">
        <div class="perf-section-title">
          <el-icon><Document /></el-icon>
          绩效考核信息
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="考核名称" prop="examineName">
              <el-input
                :disabled="reviewFlag"
                v-model="formData.examineName"
                placeholder="请输入考核名称"
                clearable
                class="perf-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核期限" prop="examineDeadline">
              <el-input
                :disabled="reviewFlag"
                v-model="formData.examineDeadline"
                placeholder="考核期限"
                clearable
                class="perf-input"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="perf-section">
        <div class="perf-section-title">
          <el-icon><List /></el-icon>
          绩效考核明细
        </div>
        <el-row :gutter="10">
          <el-form-item style="width: 100%" label-position="top" prop="subtableSaveReqVOS">
            <el-table :data="formData.subtableSaveReqVOS" class="perf-table">
              <!-- <el-table-column label="序号" type="index" width="70" /> -->
              <el-table-column
                label="考核维度"
                prop="examineDimension"
                min-width="70"
                :show-overflow-tooltip="false"
              />
              <el-table-column
                label="绩效目标内容"
                min-width="250"
                prop="performanceTarget"
                align="left"
                header-align="center"
              >
                <template #default="{ row, $index }">
                  <Editor
                    :readonly="[0, 1, 4, 2].includes($index) && currentTemplateId === 24"
                    height="150px"
                    :showToolBar="false"
                    v-if="row.examineDimension === '考核指标（100%）' && !reviewFlag"
                    v-model="row.performanceTarget"
                  />
                  <div style="white-space: normal" v-else v-html="row.performanceTarget"></div>
                </template>
              </el-table-column>
              <el-table-column label="权重" min-width="50" prop="weight" />
              <el-table-column
                label="考核衡量标准"
                min-width="300"
                prop="assessmentCriteria"
                :show-overflow-tooltip="false"
              >
                <template #default="{ row, $index }">
                  <div>
                    <div
                      @click="handleManHourInfo($index)"
                      style="color: #606bfb; text-decoration: underline; cursor: pointer"
                      v-if="
                        currentTemplateId === 24 && ($index === 1 || $index === 4 || $index === 2)
                      "
                      >{{
                        $index === 1
                          ? `月度签定合同总额:${manHourInfo?.contractAmount}元`
                          : $index === 2
                            ? `月度实际呼出有效电话数量:${manHourInfo?.followUpRecords}个`
                            : `实际每月新增有效客户数:${manHourInfo?.newCustomerCount}家`
                      }}</div
                    >
                    <div
                      @click="handleManHourInfo($index)"
                      style="color: #606bfb; text-decoration: underline; cursor: pointer"
                      v-if="(currentTemplateId === 31 || currentTemplateId === 32) && $index === 0"
                      >当月现场咨询完成情况</div
                    >
                    <div v-html="row.assessmentCriteria"></div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="考核依据"
                min-width="70"
                prop="assessmentBasis"
                :show-overflow-tooltip="false"
              />
              <el-table-column
                label="考核主体"
                min-width="70"
                prop="assessmentSubject"
                :show-overflow-tooltip="false"
              />
              <el-table-column label="考核结果与评分" fixed="right">
                <el-table-column
                  align="left"
                  label="实际完成情况"
                  min-width="200"
                  prop="actualCompletionStatus"
                >
                  <template #default="{ row }">
                    <Editor
                      height="150px"
                      :showToolBar="false"
                      v-if="
                        (row.examineDimension === '考核指标（100%）' ||
                          row.examineDimension === '加分项') &&
                        !reviewFlag
                      "
                      v-model="row.actualCompletionStatus"
                    />
                    <div
                      style="white-space: normal"
                      v-else
                      v-html="row.actualCompletionStatus"
                    ></div>
                  </template>
                </el-table-column>
                <el-table-column label="自评" min-width="80" prop="selfEvaluation10">
                  <template #default="{ row, $index }">
                    <el-input
                      :showToolBar="false"
                      v-if="
                        (row.examineDimension === '考核指标（100%）' ||
                          row.examineDimension === '加分项') &&
                        !reviewFlag
                      "
                      v-model="row.selfEvaluation10"
                      type="number"
                      :disabled="
                        ((currentTemplateId === 31 || currentTemplateId === 32) && $index === 0) ||
                        (currentTemplateId === 24 &&
                          ($index === 0 || $index === 1 || $index === 4 || $index === 2))
                      "
                    />
                    <div style="white-space: normal" v-else v-html="row.selfEvaluation10"></div>
                  </template>
                </el-table-column>
                <template v-if="reviewFlag">
                  <el-table-column
                    align="left"
                    label="领导评价"
                    min-width="200"
                    prop="leadershipEvaluation"
                  >
                    <template #default="{ row }">
                      <Editor
                        height="150px"
                        :showToolBar="false"
                        v-if="
                          (row.examineDimension === '考核指标（100%）' ||
                            row.examineDimension === '加分项' ||
                            row.examineDimension === '否决项' ||
                            row.examineDimension === '扣分项') &&
                          tasks &&
                          tasks.length > 0 &&
                          tasks[tasks.length - 1].assigneeUser.id === userId
                        "
                        v-model="row.leadershipEvaluation"
                      />
                      <div
                        style="white-space: normal"
                        v-else
                        v-html="row.leadershipEvaluation"
                      ></div>
                    </template>
                  </el-table-column>
                  <el-table-column label="领导评" min-width="100" prop="leadershipEvaluation90">
                    <template #default="{ row }">
                      <el-input
                        :showToolBar="false"
                        v-if="
                          (row.examineDimension === '考核指标（100%）' ||
                            row.examineDimension === '加分项' ||
                            row.examineDimension === '否决项' ||
                            row.examineDimension === '扣分项') &&
                          tasks &&
                          tasks.length > 0 &&
                          tasks[tasks.length - 1].assigneeUser.id === userId
                        "
                        type="number"
                        v-model="row.leadershipEvaluation90"
                      />
                      <div
                        style="white-space: normal"
                        v-else
                        v-html="row.leadershipEvaluation90"
                      ></div>
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        <el-row :gutter="10">
          <div class="perf-stats">
            <div
              >小计：<span class="perf-red">{{ totalScore.toFixed(2) }}分</span></div
            >
            <div
              >自评总分：<span class="perf-red">{{ totalSelfScore.toFixed(2) }}分</span></div
            >
            <div
              >领导总评分：<span class="perf-red">{{ totalLeaderScore.toFixed(2) }}分</span></div
            >
            <div
              >本月绩效得分：<span class="perf-red"
                >{{ (totalSelfScore * 0.1 + totalLeaderScore * 0.9).toFixed(2) }}分</span
              ></div
            >
          </div>
        </el-row>
      </div>
      <div class="perf-section">
        <div class="perf-section-title">
          <el-icon><InfoFilled /></el-icon>
          说明
        </div>
        <div class="perf-desc">
          <div>1、业务类指标总分90分，通用指标总分10分，否决项指标不占权重，但有一项否决权。</div>
          <div>2、在目标设定过程中应按月制订分阶段分步骤实施目标计划，并与直接领导达成共识。</div>
          <div
            >3、年度绩效目标的设定主要来自于公司或部门年度目标的分解并结合本岗位的具体工作内容而设置。</div
          >
          <div
            >4、每月常规考核，每季度进行季度考核并评估汇总，根据考核目标完成情况，如连续三个月业绩绩效考核不合格或不能胜任工作，则公司有权调岗降职降薪重新聘用。</div
          >
          <div
            >5、绩效得分全年平均分低于70分不参与年终奖励，月度考核低于60分按绩效考核不合格处理。</div
          >
        </div>
      </div>
      <div class="perf-section">
        <div class="perf-section-title">
          <el-icon><EditPen /></el-icon>
          本月工作总结
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label-position="top" label="" prop="examineSummarize">
              <Editor
                :style="isFullScreen ? 'z-index: 999' : ''"
                ref="editorRef"
                class="mt-10px w-100%"
                height="450px"
                v-model="formData.examineSummarize"
                :readonly="reviewFlag"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="perf-section">
        <div class="perf-section-title">
          <el-icon><Calendar /></el-icon>
          下月工作计划
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label-position="top" label="" prop="nextPlan">
              <Editor
                ref="nextPlanEditorRef"
                :style="isFullScreen1 ? 'z-index: 999' : ''"
                v-show="!isFullScreen"
                class="mt-10px w-100%"
                height="450px"
                v-model="formData.nextPlan"
                :readonly="reviewFlag"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="perf-section" v-if="reviewFlag">
        <div class="perf-section-title">
          <el-icon><UserFilled /></el-icon>
          领导总结
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label-position="top" label="" prop="leaderSummarize">
              <el-input
                type="textarea"
                :rows="6"
                v-model="formData.leaderSummarize"
                :disabled="
                  !(
                    tasks &&
                    tasks.length > 0 &&
                    tasks[tasks.length - 1]?.assigneeUser.id === userId
                  )
                "
                class="perf-input"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="10" v-if="!reviewFlag">
        <div class="perf-btns">
          <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
          <el-button @click="handleClose" type="warning" :disabled="formLoading">关 闭</el-button>
        </div>
      </el-row>
      <ContractInfoDialog ref="contractInfoDialogRef" />
      <FollowUpInfoDialog ref="followUpInfoDialogRef" />
      <CustomerInfoDialog ref="customerInfoDialogRef" />
      <ManListDialog ref="manListDialogRef" />
    </el-form>
  </div>
</template>
<script setup lang="ts">
import ContractInfoDialog from './contractInfoDialog.vue'
import FollowUpInfoDialog from './followUpInfoDialog.vue'
import CustomerInfoDialog from './customerInfoDialog.vue'
import ManListDialog from './manListDialog.vue'
import dayjs from 'dayjs'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { PerformanceTemplateMainApi } from '@/api/OA/performanceTemplateMain'
import { ExamineProcessApi, ExamineProcessVO } from '@/api/OA/performanceProcess'
import { useUserStore } from '@/store/modules/user'
import { FollowUpRecordApi, FollowUpRecordVO } from '@/api/crm/followup'
import { formatDateString } from '@/utils/formatTime'

const props = defineProps({
  reviewFlag: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number
  },
  tasks: {
    type: Array
  }
})
const editorRef = ref()
const nextPlanEditorRef = ref()
const { query } = useRoute() // 查询参数
const id = query.id as unknown as number
const userId = useUserStore().getUser.id // 当前登录的编号
const templateName = useUserStore().getUser.templateName // 当前登录的编号
const templateId = useUserStore().getUser.templateId // 当前登录的编号
let currentTemplateId = ref()
let currentUserId = ref()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const formLoading = ref(false)
const formData = ref<any>({
  templateName: undefined,
  id: undefined,
  examineName: undefined,
  examineDeadline: undefined,
  examineSummarize: undefined,
  nextPlan: undefined,
  leaderSummarize: undefined,
  subtableSaveReqVOS: []
})
let manHourInfo = ref<any>()
const contractInfoDialogRef = ref()
const followUpInfoDialogRef = ref()
const customerInfoDialogRef = ref()
const manListDialogRef = ref()

const formRules = reactive({
  examineName: [{ required: true, message: '请输入考核名称', trigger: 'blur' }],
  examineDeadline: [{ required: true, message: '请输入考核期限', trigger: 'blur' }],
  examineSummarize: [{ required: true, message: '请输入本月工作总结', trigger: 'blur' }],
  nextPlan: [{ required: true, message: '请输入下月工作计划', trigger: 'blur' }],
  leaderSummarize: [{ required: true, message: '请输入领导总结', trigger: 'blur' }],
  subtableSaveReqVOS: [
    {
      validator: (rule, value, callback) => {
        // 1. 必填校验
        if (!value) {
          callback(new Error('子项不能为空'))
          return
        }
        // 2. 只校验 selfEvaluation10 是否超出 weight
        const errors: any = []
        value.forEach((item, idx) => {
          // 允许超出weight的情况
          let canExceed = false
          if (currentTemplateId.value === 31 || currentTemplateId.value === 32) {
            canExceed = idx === 0
          } else if (currentTemplateId.value === 24) {
            canExceed = [0, 1, 2, 4].includes(idx)
          }
          // 只要不是允许超出的情况就校验
          if (!canExceed) {
            const weightNum = Number(item.weight)
            if (
              typeof item.selfEvaluation10 !== 'undefined' &&
              item.selfEvaluation10 !== null &&
              !isNaN(weightNum) &&
              Number(item.selfEvaluation10) > weightNum
            ) {
              errors.push(`第${idx + 1}项自评分不能超过权重`)
            }
          }
          // 原有的必填校验
          if (item.examineDimension === '考核指标（100%）' && !item.selfEvaluation10) {
            errors.push('所有自评必须填写')
          }
          if (
            props.reviewFlag &&
            item.examineDimension === '考核指标（100%）' &&
            !item.leadershipEvaluation90
          ) {
            errors.push('所有领导评必须填写')
          }
        })

        if (errors.length > 0) {
          callback(new Error(errors[0]))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})
const formRef = ref() // 表单 Ref

let formType = ref('create')
const isFullScreen = ref(false)
const isFullScreen1 = ref(false)
onMounted(async () => {
  if (props.reviewFlag) {
    let data = await ExamineProcessApi.getExamineProcess(props.id as any)
    formData.value = {
      ...data,
      leaderSummarize: data.leaderSummarize,
      subtableSaveReqVOS: data.subtableRespVOS.reverse()
    }
    currentTemplateId.value = data.moduleId
    currentUserId.value = data.userId
    if (data.moduleId === 24) {
      let res = await ExamineProcessApi.getContractManHour({
        userId: data.userId,
        yearMonth: formatDateString(data.examineDeadline)
      })
      manHourInfo.value = res
    }
  } else {
    if (query.id) {
      let data = await ExamineProcessApi.getExamineProcess(query.id as any)
      formData.value = {
        ...data,
        subtableSaveReqVOS: data.subtableRespVOS.reverse()
      }
      currentTemplateId.value = data.moduleId
      currentUserId.value = data.userId
      if (data.moduleId === 31 || data.moduleId === 32) {
        let res = await ExamineProcessApi.getManHour({
          userId: data.userId,
          yearMonth: formatDateString(data.examineDeadline)
        })
        formData.value.subtableSaveReqVOS[0].selfEvaluation10 = res
      }
      if (data.moduleId === 24) {
        let res = await ExamineProcessApi.getContractManHour({
          userId: data.userId,
          yearMonth: formatDateString(data.examineDeadline)
        })
        manHourInfo.value = res
        formData.value.subtableSaveReqVOS[0].assessmentCriteria =
          '得分=月度累计有效到账额(' + res.receiveAmount + '元)/目标销售额×权重'
        formData.value.subtableSaveReqVOS[0].performanceTarget =
          '月度销售实际到账额（目标值：' + res.targetSaleAmount + '元）'
        formData.value.subtableSaveReqVOS[1].performanceTarget =
          '月度新签合同额（目标值：' + res.targetContractAmount + '元）'
        formData.value.subtableSaveReqVOS[4].performanceTarget =
          '有效新客户的开发（目标值：' + res.targetCustomerCount + '家）'
        formData.value.subtableSaveReqVOS[2].performanceTarget =
          '月度有效呼出电话数量（目标值：非外出拜访日每天不少于' +
          res.targetFollowUpRecords +
          '个）'
        formData.value.subtableSaveReqVOS[0].selfEvaluation10 = res.score1 + ''
        formData.value.subtableSaveReqVOS[1].selfEvaluation10 = res.score2 + ''
        formData.value.subtableSaveReqVOS[4].selfEvaluation10 = res.score3 + ''
        formData.value.subtableSaveReqVOS[2].selfEvaluation10 = res.score4 + ''
      }
    } else {
      chooseTemplateDone()
    }
  }

  const editor = await editorRef.value.getEditorRef()
  const editor1 = await nextPlanEditorRef.value.getEditorRef()

  editor.on('fullScreen', () => {
    isFullScreen.value = true
  })
  editor.on('unFullScreen', () => {
    isFullScreen.value = false
  })
  editor1.on('fullScreen', () => {
    isFullScreen1.value = true
  })
  editor1.on('unFullScreen', () => {
    isFullScreen1.value = false
  })
})

const totalScore = computed(() => {
  if (currentTemplateId.value === 31 || currentTemplateId.value === 32) {
    return 110
  } else {
    return formData.value.subtableSaveReqVOS.reduce((acc, item) => {
      const weight = Number(item.weight) // 将字符串转换为数字
      return acc + (isNaN(weight) ? 0 : weight)
    }, 0)
  }
})

// 计算总自评
const totalSelfScore = computed(() => {
  if (currentTemplateId.value === 31) {
    const items = formData.value.subtableSaveReqVOS.filter(
      (item) => item.examineDimension === '考核指标（100%）'
    )
    if (items.length === 0) return 0
    const firstItemScore = Number(items[0].selfEvaluation10) || 0
    const remainingItemsTotalScore = items.slice(1).reduce((acc, item) => {
      const selfEvaluation = Number(item.selfEvaluation10) || 0
      return acc + selfEvaluation
    }, 0)
    // 剩余项的自评分总和
    const otherScore = formData.value.subtableSaveReqVOS
      .filter((item) => item.examineDimension !== '考核指标（100%）')
      .reduce((acc, item) => {
        const selfEvaluation = Number(item.selfEvaluation10) || 0
        return acc + selfEvaluation
      }, 0)

    return firstItemScore * (remainingItemsTotalScore / 100) + otherScore
  } else if (currentTemplateId.value === 32) {
    const items = formData.value.subtableSaveReqVOS
    if (items.length < 13) return 0 // 确保有足够的项进行计算

    // 第一项的自评分
    const firstItemSelfScore = Number(items[0].selfEvaluation10) || 0
    // 第二项到第六项的自评分总和
    const secondToSixthItemsSelfScoreTotal = items.slice(1, 6).reduce((acc, item) => {
      const selfEvaluation = Number(item.selfEvaluation10) || 0
      return acc + selfEvaluation
    }, 0)

    // 第七项的自评分
    const seventhItemSelfScore = Number(items[6].selfEvaluation10) || 0
    // 第八项到第十三项的自评分总和
    const eighthToThirteenthItemsSelfScoreTotal = items.slice(7, 13).reduce((acc, item) => {
      const selfEvaluation = Number(item.selfEvaluation10) || 0
      return acc + selfEvaluation
    }, 0)

    // 计算百分比后的分数
    const firstPartScore = firstItemSelfScore * (secondToSixthItemsSelfScoreTotal / 100)
    const secondPartScore = seventhItemSelfScore * (eighthToThirteenthItemsSelfScoreTotal / 100)

    // 剩余项的自评分总和
    const remainingItemsTotalScore = formData.value.subtableSaveReqVOS
      .filter((item) => item.examineDimension !== '考核指标（100%）')
      .reduce((acc, item) => {
        const selfEvaluation = Number(item.selfEvaluation10) || 0
        return acc + selfEvaluation
      }, 0)

    return firstPartScore + secondPartScore + remainingItemsTotalScore
  } else {
    return formData.value.subtableSaveReqVOS.reduce((acc, item) => {
      const selfEvaluation = Number(item.selfEvaluation10) // 将字符串转换为数字
      return acc + (isNaN(selfEvaluation) ? 0 : selfEvaluation)
    }, 0)
  }
})

// 计算总领导评
const totalLeaderScore = computed(() => {
  if (currentTemplateId.value === 31) {
    const items = formData.value.subtableSaveReqVOS.filter(
      (item) => item.examineDimension === '考核指标（100%）'
    )
    if (items.length === 0) return 0
    // 第一项的领导评分
    const firstItemLeaderScore = Number(items[0].leadershipEvaluation90) || 0
    // 剩余项的领导评分总和
    const remainingItemsSelfScoreTotal = items.slice(1).reduce((acc, item) => {
      const selfEvaluation = Number(item.leadershipEvaluation90) || 0
      return acc + selfEvaluation
    }, 0)
    // 计算百分比后的分数
    const percentageScore = firstItemLeaderScore * (remainingItemsSelfScoreTotal / 100)
    // 其他项的领导评分总和
    const otherItemsLeaderScoreTotal = formData.value.subtableSaveReqVOS
      .filter((item) => item.examineDimension !== '考核指标（100%）')
      .reduce((acc, item) => {
        const leaderEvaluation = Number(item.leadershipEvaluation90) || 0
        return acc + leaderEvaluation
      }, 0)
    return percentageScore + otherItemsLeaderScoreTotal
  } else if (currentTemplateId.value === 32) {
    const items = formData.value.subtableSaveReqVOS
    if (items.length < 13) return 0 // 确保有足够的项进行计算
    // 第一项的领导评分
    const firstItemLeaderScore = Number(items[0].leadershipEvaluation90) || 0
    // 第二项到第六项的领导评分总和
    const secondToSixthItemsLeaderScoreTotal = items.slice(1, 6).reduce((acc, item) => {
      const leadershipEvaluation = Number(item.leadershipEvaluation90) || 0
      return acc + leadershipEvaluation
    }, 0)

    // 第七项的领导评分
    const seventhItemLeaderScore = Number(items[6].leadershipEvaluation90) || 0
    // 第八项到第十三项的领导评分总和
    const eighthToThirteenthItemsLeaderScoreTotal = items.slice(7, 13).reduce((acc, item) => {
      const leadershipEvaluation = Number(item.leadershipEvaluation90) || 0
      return acc + leadershipEvaluation
    }, 0)

    // 计算百分比后的分数
    const firstPartScore = firstItemLeaderScore * (secondToSixthItemsLeaderScoreTotal / 100)
    const secondPartScore = seventhItemLeaderScore * (eighthToThirteenthItemsLeaderScoreTotal / 100)

    // 其他项的领导评分总和
    const otherItemsLeaderScoreTotal = formData.value.subtableSaveReqVOS
      .filter((item) => item.examineDimension !== '考核指标（100%）')
      .reduce((acc, item) => {
        const leaderEvaluation = Number(item.leadershipEvaluation90) || 0
        return acc + leaderEvaluation
      }, 0)

    return firstPartScore + secondPartScore + otherItemsLeaderScoreTotal
  } else {
    return formData.value.subtableSaveReqVOS.reduce((acc, item) => {
      const leaderEvaluation = Number(item.leadershipEvaluation90) // 将字符串转换为数字
      return acc + (isNaN(leaderEvaluation) ? 0 : leaderEvaluation)
    }, 0)
  }
})

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ExamineProcessVO as any
    if (props.reviewFlag) {
      // 审核人审核 TODO=====
      await ExamineProcessApi.updateExamineProcess(data)
      message.success(t('common.updateSuccess'))
    } else {
      // 正常新增/修改
      if (formType.value === 'create') {
        data.moduleId = templateId
        delete data.id
        await ExamineProcessApi.createExamineProcess(data)
        message.success(t('common.createSuccess'))
      } else {
        await ExamineProcessApi.updateExamineProcess(data)
        message.success(t('common.updateSuccess'))
      }
    }
  } finally {
    formLoading.value = false
    if (!props.reviewFlag) handleClose()
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

const chooseTemplateDone = async () => {
  currentTemplateId.value = templateId
  formData.value.examineDeadline = dayjs().subtract(1, 'month').format('YYYY年M月')
  formData.value.templateName = templateName
  formData.value.examineName = templateName + formData.value.examineDeadline
  let data =
    await PerformanceTemplateMainApi.getPerformanceTemplateDetailsListByMainTemplateId(templateId)

  formData.value.subtableSaveReqVOS = data.map((item) => {
    return {
      ...item,
      examineDimension: item.dimension,
      performanceTarget: item.target,
      assessmentSubject: item.performanceIndicator,
      id: undefined
    }
  })
  console.log('formData.value', formData.value)

  if (templateId === 31 || templateId === 32) {
    let res = await ExamineProcessApi.getManHour({
      userId,
      yearMonth: dayjs().subtract(1, 'month').format('YYYY-MM')
    })
    formData.value.subtableSaveReqVOS[0].selfEvaluation10 = res
  }
  if (templateId === 24) {
    let res = await ExamineProcessApi.getContractManHour({
      userId,
      yearMonth: dayjs().subtract(1, 'month').format('YYYY-MM')
    })
    manHourInfo.value = res
    formData.value.subtableSaveReqVOS[0].assessmentCriteria =
      '得分=月度累计有效到账额(' + res.receiveAmount + '元)/目标销售额×权重'
    formData.value.subtableSaveReqVOS[0].performanceTarget =
      '月度销售实际到账额（目标值：' + res.targetSaleAmount + '元）'
    formData.value.subtableSaveReqVOS[1].performanceTarget =
      '月度新签合同额（目标值：' + res.targetContractAmount + '元）'
    formData.value.subtableSaveReqVOS[4].performanceTarget =
      '有效新客户的开发（目标值：' + res.targetCustomerCount + '家）'
    formData.value.subtableSaveReqVOS[2].performanceTarget =
      '月度有效呼出电话数量（目标值：非外出拜访日每天不少于' + res.targetFollowUpRecords + '个）'
    formData.value.subtableSaveReqVOS[0].selfEvaluation10 = res.score1 + ''
    formData.value.subtableSaveReqVOS[1].selfEvaluation10 = res.score2 + ''
    formData.value.subtableSaveReqVOS[4].selfEvaluation10 = res.score3 + ''
    formData.value.subtableSaveReqVOS[2].selfEvaluation10 = res.score4 + ''
  }
}

const handleManHourInfo = async (index) => {
  if (currentTemplateId.value === 31 || currentTemplateId.value === 32) {
    manListDialogRef.value.open(
      currentUserId.value ? currentUserId.value : userId,
      formData.value.examineDeadline
    )
  } else {
    switch (index) {
      case 1:
        contractInfoDialogRef.value.open(
          currentUserId.value ? currentUserId.value : userId,
          formData.value.examineDeadline
        )
        break
      case 2:
        followUpInfoDialogRef.value.open(
          currentUserId.value ? currentUserId.value : userId,
          formData.value.examineDeadline
        )
        break
      case 4:
        customerInfoDialogRef.value.open(
          currentUserId.value ? currentUserId.value : userId,
          formData.value.examineDeadline
        )
        break
      default:
        break
    }
  }
}

defineExpose({ submitForm, formRef })
</script>

<style lang="scss" scoped>
.perf-card {
  background: none;
  border-radius: 0;
  box-shadow: none;
  padding: 5px;
  max-width: 100vw;
  margin: 0;
  margin-bottom: 100px;
}
.perf-form {
  .el-form-item {
    margin-bottom: 22px;
  }
}
.perf-section {
  background: none;
  border-radius: 0;
  margin-bottom: 0;
  padding: 0;
  box-shadow: none;
}
.perf-section-title {
  font-size: 20px;
  font-weight: 700;
  color: #22223b;
  margin-bottom: 12px;
  gap: 8px;
  .el-icon {
    background: none;
    border-radius: 0;
    padding: 0;
    color: #4f46e5;
    font-size: 22px;
  }
}
.perf-input {
  border-radius: 0 !important;
  box-shadow: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 16px;
  transition: border 0.2s;
  &:focus {
    border-color: #4f46e5;
  }
}
.perf-table {
  border-radius: 0 !important;
  box-shadow: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 16px;
  :deep(.el-table__header th) {
    background: #f4f6fa;
    font-weight: 800;
    color: #3b3b4f;
    font-size: 16px;
    border-bottom: 2px solid #e0e7ef;
    border-top: none;
    border-left: none;
    border-right: none;
    letter-spacing: 0.5px;
    height: 48px;
    text-align: center;
  }
  :deep(.el-table__body tr) {
    background: #fff;
    border-bottom: 1.5px solid #f1f1f1;
    transition: background 0.2s;
    &:hover {
      background: #f5f7fa;
    }
  }
  :deep(.el-table__cell) {
    padding: 10px 6px;
    border-bottom: none;
    font-size: 15px;
    color: #23233a;
    background: none;
    text-align: center;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
  }
  :deep(.el-table__footer) {
    background: #f4f6fa;
    font-weight: 700;
    color: #3b3b4f;
    border-top: 2px solid #e0e7ef;
  }
  :deep(.el-table__empty-block) {
    background: #fafbfc;
  }
}
.perf-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  font-size: 19px;
  font-weight: 700;
  margin: 22px 0 0 0;
  .perf-red {
    color: #eab308;
    font-weight: 800;
    letter-spacing: 1px;
  }
}
.perf-desc {
  color: #6f6f6f;
  font-size: 16px;
  margin: 12px 0 0 0;
  line-height: 2.1;
  border-left: 4px solid #f59e42;
  padding-left: 18px;
  background: #f9fafb;
  border-radius: 0;
}
.perf-btns {
  z-index: 999;
  position: fixed;
  bottom: 50px;
  left: 50%;
  display: flex;
  justify-content: center;
  gap: 22px;
  // margin-top: 32px;
  // width: 100%;

  .el-button[type='primary'] {
    background: linear-gradient(135deg, #4f46e5 0%, #f59e42 100%);
    color: #fff;
    border: none;
    font-weight: 700;
    font-size: 16px;
    border-radius: 12px;
    padding: 12px 32px;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #3b36b6 0%, #eab308 100%);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .el-button[type='warning'] {
    background: #fff7ed;
    color: #f59e42;
    border: 2px solid #f59e42;
    font-weight: 700;
    font-size: 16px;
    border-radius: 12px;
    padding: 12px 32px;
    transition: all 0.3s ease;

    &:hover {
      background: #fef3c7;
      color: #b45309;
      border-color: #eab308;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
@media (max-width: 1100px) {
  .perf-card {
    padding: 12px 2vw 12px 2vw;
    max-width: 100vw;
  }
  .perf-section {
    padding: 16px 4vw 10px 4vw;
  }
}
@media (max-width: 600px) {
  .perf-card {
    padding: 2vw 0 2vw 0;
    margin: 0;
  }
  .perf-section-title {
    font-size: 17px;
    margin-bottom: 10px;
  }
  .perf-stats {
    font-size: 15px;
    gap: 10px;
  }
  .perf-desc {
    font-size: 13px;
    padding-left: 8px;
  }
  .perf-section {
    padding: 8px 2vw 6px 2vw;
    margin-bottom: 10px;
  }
}
</style>
