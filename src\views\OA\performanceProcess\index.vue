<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="考核名称" prop="examineName">
        <el-input
          v-model="queryParams.examineName"
          placeholder="请输入考核名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="考核期限" prop="examineDeadline">
        <el-date-picker
          placeholder="请选择考核期限"
          clearable
          type="month"
          v-model="queryParams.examineDeadline"
          value-format="YYYY年MM月"
          class="!w-240px"
          @keyup.enter="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          class="!w-240px"
          :data="deptList"
          default-expand-all
          :props="defaultProps"
          check-strictly
          node-key="id"
          placeholder="请选择归属部门"
          @change="changeDept"
        />
      </el-form-item>

      <el-form-item label="申请人" prop="userId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.userId"
          placeholder="请先选择申请人"
          value-key="id"
          lable-key="nickname"
          @change="getList"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-select v-model="queryParams.result" placeholder="请选择审核状态" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="handleCreate">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="考核ID" align="center" prop="id" /> -->
      <el-table-column label="考核名称" align="center" prop="examineName" />
      <!-- <el-table-column label="考核类型" align="center" prop="examineType" /> -->
      <el-table-column label="考核期限" align="center" prop="examineDeadline" />
      <el-table-column label="审核状态" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="userId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="流程实例的编号" align="center" prop="processInstanceId" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <!-- <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button> -->
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
          <!-- <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ExamineProcessForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ExamineProcessApi, ExamineProcessVO } from '@/api/OA/performanceProcess'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'

/** 绩效考核流程 列表 */
defineOptions({ name: 'ExamineProcess' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const route = useRoute()
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const deptList = ref<Tree[]>([]) // 树形结构

const loading = ref(true) // 列表的加载中
const list = ref<ExamineProcessVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined,
  examineName: undefined,
  examineType: undefined,
  examineDeadline: undefined,
  examineSummarize: undefined,
  nextPlan: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ExamineProcessApi.getExamineProcessPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  router.push({ name: 'PerformanceProcessCreate' })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ExamineProcessApi.deleteExamineProcess(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ExamineProcessApi.exportExamineProcess(queryParams)
    download.excel(data, '绩效考核流程.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const reissue = (row) => {
  router.push({
    name: 'PerformanceProcessCreate',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

const changeDept = async (value) => {
  userList.value = await UserApi.getSimpleUserList({ deptId: value })
}

/** 初始化 **/
onMounted(async () => {
  getList()
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style scoped lang="scss">
.performance-process-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}

/* 搜索条件美化增强 */
:deep(.el-form) {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 32px 32px 8px 32px;
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 0 32px;
  align-items: flex-end;
}
:deep(.el-form-item) {
  margin-bottom: 24px;
  margin-right: 0;
  min-width: 220px;
  .el-form-item__label {
    font-weight: 600;
    color: #334155;
    font-size: 15px;
    padding-bottom: 2px;
  }
}
:deep(.el-input), :deep(.el-select), :deep(.el-date-picker), :deep(.el-tree-select) {
  width: 240px !important;
  border-radius: 10px !important;
  font-size: 15px;
  .el-input__wrapper, .el-select__wrapper, .el-date-editor, .el-tree-select__wrapper {
    border-radius: 10px !important;
    border: 1px solid #d1d5db !important;
    box-shadow: 0 1px 4px rgba(102,126,234,0.08);
    transition: all 0.3s;
    background: #f8fafc;
    &:hover, &.is-focus {
      border-color: #667eea !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.15);
      background: #fff;
    }
  }
  .el-input__inner, .el-select__selected-value, .el-date-editor input {
    font-size: 15px;
    color: #334155;
    &::placeholder {
      color: #b6bdd3;
      font-weight: 400;
    }
  }
}
:deep(.el-form-item:last-child) {
  flex-basis: 100%;
  margin-top: 8px;
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
  display: flex;
  gap: 16px;
  background: transparent;
}

@media (max-width: 768px) {
  :deep(.el-form) {
    padding: 12px 8px 8px 8px;
    gap: 0 8px;
  }
  :deep(.el-form-item) {
    margin-bottom: 12px;
    min-width: 100px;
    .el-form-item__label {
      font-size: 14px;
    }
  }
  :deep(.el-input), :deep(.el-select), :deep(.el-date-picker), :deep(.el-tree-select) {
    width: 100% !important;
    min-width: 0;
    font-size: 14px;
  }
  :deep(.el-form-item:last-child) {
    padding-top: 8px;
    gap: 8px;
  }
}

.el-table {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  overflow: hidden;
  margin-bottom: 16px;
  :deep(.el-table__header) th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e2e8f0;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    td {
      border-bottom: 1px solid #f1f5f9;
      padding: 16px 8px;
      font-size: 14px;
      color: #334155;
    }
  }
}

.el-table .el-button {
  min-width: 72px;
  padding: 0 12px;
  font-size: 13px;
  height: 32px;
  border-radius: 6px;
}

.el-table .el-button + .el-button {
  margin-left: 8px;
}

// 分页卡片样式
.Pagination {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  padding: 16px 0;
  margin-top: 0;
}

:deep(.el-form-item__label) {
  min-width: 90px;
  white-space: nowrap;
}
</style>
