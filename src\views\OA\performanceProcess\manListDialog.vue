<template>
  <el-dialog title="当月现场咨询完成情况" v-model="dialogVisible" width="1240px">
    <el-table v-loading="formLoading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="合同编号" align="center" prop="contractCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="成果文件" align="center" prop="resultUrl" min-width="250">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.resultUrl?.split(';;').join(',')" />
        </template>
      </el-table-column>
      <el-table-column label="文件" align="center" prop="fileUrl" min-width="250">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.fileUrl" />
        </template>
      </el-table-column>
      <el-table-column label="上传人" align="center" prop="userId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" width="250" />
      <el-table-column
        label="实际发生日期"
        align="center"
        prop="consumeDate"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>
<script setup lang="ts">
import { ExamineProcessApi, ExamineProcessVO } from '@/api/OA/performanceProcess'
import { dateFormatter, formatDateString } from '@/utils/formatTime'
import * as UserApi from '@/api/system/user'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const list = ref<[]>([]) // 列表的数据
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const formRef = ref() // 表单 Ref
const total = ref(0) // 列表的总页数
let currentUserId = ref()
let currentYearMonth = ref()
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10
})

/** 打开弹窗 */
const open = async (userId, yearMonth) => {
  userList.value = await UserApi.getSimpleUserList()
  dialogVisible.value = true
  currentUserId.value = userId
  currentYearMonth.value = formatDateString(yearMonth)
  getList()
}

/** 查询列表 */
const getList = async () => {
  formLoading.value = true
  try {
    queryParams.userId = currentUserId.value
    queryParams.yearMonth = currentYearMonth.value
    const data = await ExamineProcessApi.getManHourListPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    formLoading.value = false
  }
}

/** 提交表单 */
const emit = defineEmits(['fetch-data']) // 定义 success 事件，用于操作成功后的回调

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
