<template>
  <Dialog title="考核模板弹窗" v-model="dialogVisible">
    <el-table
      v-loading="formLoading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板版本" align="center" prop="templateVersion" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {
  PerformanceTemplateMainApi,
  PerformanceTemplateMainVO
} from '@/api/OA/performanceTemplateMain'
import { dateFormatter } from '@/utils/formatTime'

/** 绩效考核流程 表单 */
defineOptions({ name: 'PerformanceProcessForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
let currentRow = ref()

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  templateId: undefined,
  evaluationId: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  remark: undefined
})

const list = ref<PerformanceTemplateMainVO[]>([]) // 列表的数据

const formRef = ref() // 表单 Ref
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const formRules = reactive({
  templateId: [{ required: true, message: '模板ID不能为空', trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  resetForm()
  getList()
}

/** 查询列表 */
const getList = async () => {
  formLoading.value = true
  try {
    const data = await PerformanceTemplateMainApi.getPerformanceTemplateMainPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    formLoading.value = false
  }
}

const handleCurrentChange = (val) => {
  currentRow.value = val
}

/** 提交表单 */
const emit = defineEmits(['fetch-data']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (currentRow.value) {
    dialogVisible.value = false
    emit('fetch-data', currentRow.value)
    message.success('选择成功')
  } else {
    message.error('请选择模板')
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    templateId: undefined,
    evaluationId: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
