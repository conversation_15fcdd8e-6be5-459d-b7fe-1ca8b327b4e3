<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="formData.templateName" placeholder="请输入模板名称" />
      </el-form-item>
      <el-form-item label="模板版本" prop="templateVersion">
        <el-input v-model="formData.templateVersion" placeholder="请输入模板版本" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="绩效考核模板子" name="performanceTemplateDetails">
        <PerformanceTemplateDetailsForm
          ref="performanceTemplateDetailsFormRef"
          :main-template-id="formData.id"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {
  PerformanceTemplateMainApi,
  PerformanceTemplateMainVO
} from '@/api/OA/performanceTemplateMain'
import PerformanceTemplateDetailsForm from './components/PerformanceTemplateDetailsForm.vue'

/** 绩效考核模板主 表单 */
defineOptions({ name: 'PerformanceTemplateMainForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  templateName: undefined,
  templateVersion: undefined,
  remark: undefined
})
const formRules = reactive({
  templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
  templateVersion: [{ required: true, message: '模板版本不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('performanceTemplateDetails')
const performanceTemplateDetailsFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PerformanceTemplateMainApi.getPerformanceTemplateMain(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await performanceTemplateDetailsFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'performanceTemplateDetails'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PerformanceTemplateMainVO as any
    // 拼接子表的数据
    data.performanceTemplateDetailss = performanceTemplateDetailsFormRef.value.getData()
    if (formType.value === 'create') {
      await PerformanceTemplateMainApi.createPerformanceTemplateMain(data)
      message.success(t('common.createSuccess'))
    } else {
      await PerformanceTemplateMainApi.updatePerformanceTemplateMain(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    templateName: undefined,
    templateVersion: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
