<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="考核维度" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.dimension`" :rules="formRules.dimension" class="mb-0px!">
            <el-input v-model="row.dimension" placeholder="请输入考核维度" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column
        label="目标"
        min-width="450"
        align="left"
        header-align="center"
        :show-overflow-tooltip="false"
      >
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.target`" :rules="formRules.target" class="mb-0px!">
            <Editor height="150px" style="width: 100%" :showToolBar="false" v-model="row.target" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="权重" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.weight`" :rules="formRules.weight" class="mb-0px!">
            <el-input v-model="row.weight" placeholder="请输入权重" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column
        label="考核衡量标准"
        min-width="450"
        align="left"
        header-align="center"
        :show-overflow-tooltip="false"
      >
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.assessmentCriteria`"
            :rules="formRules.assessmentCriteria"
            class="mb-0px!"
          >
            <Editor
              height="150px"
              style="width: 100%"
              :showToolBar="false"
              v-model="row.assessmentCriteria"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="考核依据" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.assessmentBasis`"
            :rules="formRules.assessmentBasis"
            class="mb-0px!"
          >
            <el-input v-model="row.assessmentBasis" placeholder="请输入考核依据" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="考核主体" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.performanceIndicator`"
            :rules="formRules.performanceIndicator"
            class="mb-0px!"
          >
            <el-input v-model="row.performanceIndicator" placeholder="请输入考核主体" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button type="danger" @click="handleDelete($index)" link>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加绩效考核模板子</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PerformanceTemplateMainApi } from '@/api/OA/performanceTemplateMain'

const props: any = defineProps<{
  mainTemplateId: undefined // 关联主表的ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any>([])
const formRules: any = reactive({
  mainTemplateId: [{ required: true, message: '关联主表的ID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.mainTemplateId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value =
        await PerformanceTemplateMainApi.getPerformanceTemplateDetailsListByMainTemplateId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    mainTemplateId: undefined,
    assessmentCriteria: undefined,
    assessmentBasis: undefined,
    dimension: undefined,
    performanceIndicator: undefined,
    target: undefined,
    weight: undefined,
    remark: undefined
  }
  row.mainTemplateId = props.mainTemplateId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
