<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="岗位分类" prop="positionCategory">
        <el-select
          v-model="formData.positionCategory"
          clearable
          placeholder="请输入岗位分类"
          class="w-100%"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.POSITION_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="星级" prop="star">
        <el-rate v-model="formData.star" :max="6" />
      </el-form-item>
      <el-form-item label="联系邮箱" prop="email">
        <el-input v-model="formData.email" placeholder="请输入联系邮箱" />
      </el-form-item>
      <el-form-item label="联系时间" prop="contactTime">
        <el-date-picker
          v-model="formData.contactTime"
          type="datetime"
          value-format="x"
          placeholder="选择联系时间"
        />
      </el-form-item>
      <el-form-item label="学历" prop="education">
        <el-select v-model="formData.education" clearable placeholder="请输入学历" class="w-100%">
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.QUALIFICATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="简历来源" prop="resumeSource">
        <el-select
          v-model="formData.resumeSource"
          clearable
          placeholder="请输入简历来源"
          class="w-100%"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.RESUME_SOURCE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="附件" prop="attachment">
        <UploadFile v-model="formData.attachment" :file-size="5120" :limit="5" />
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="人才库面试记录" name="talentInterviewRecord">
        <TalentInterviewRecordForm ref="talentInterviewRecordFormRef" :talent-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TalentPoolApi, TalentPoolVO } from '@/api/OA/talent'
import TalentInterviewRecordForm from './components/TalentInterviewRecordForm.vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/** 人才库 表单 */
defineOptions({ name: 'TalentPoolForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  positionCategory: undefined,
  name: undefined,
  phone: undefined,
  email: undefined,
  contactTime: undefined,
  education: undefined,
  resumeSource: undefined,
  attachment: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('talentInterviewRecord')
const talentInterviewRecordFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TalentPoolApi.getTalentPool(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await talentInterviewRecordFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'talentInterviewRecord'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as any
    // 拼接子表的数据
    data.talentInterviewRecords = talentInterviewRecordFormRef.value.getData()
    if (formType.value === 'create') {
      await TalentPoolApi.createTalentPool(data)
      message.success(t('common.createSuccess'))
    } else {
      await TalentPoolApi.updateTalentPool(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    positionCategory: undefined,
    name: undefined,
    phone: undefined,
    email: undefined,
    contactTime: undefined,
    education: undefined,
    resumeSource: undefined,
    attachment: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.dialog-container {
  background: #f8fafc;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(102,126,234,0.10);
  padding: 0;
}
:deep(.el-dialog) {
  border-radius: 20px;
  overflow: hidden;
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 28px 40px 16px 40px;
    .el-dialog__title {
      font-size: 22px;
      font-weight: 700;
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 0 40px 0 40px;
    background: #f8fafc;
  }
  .el-dialog__footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 24px 40px;
  }
}
.el-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  padding: 32px 32px 8px 32px;
  margin-bottom: 24px;
  .el-form-item {
    margin-bottom: 24px;
    .el-form-item__label {
      font-weight: 600;
      color: #334155;
      font-size: 16px;
      min-width: 100px;
      white-space: nowrap;
    }
  }
  .el-input, .el-select, .el-date-picker {
    width: 100% !important;
    border-radius: 10px !important;
    font-size: 15px;
    .el-input__wrapper, .el-select__wrapper, .el-date-editor {
      border-radius: 10px !important;
      border: 1px solid #d1d5db !important;
      box-shadow: 0 1px 4px rgba(102,126,234,0.08);
      transition: all 0.3s;
      background: #f8fafc;
      &:hover, &.is-focus {
        border-color: #667eea !important;
        box-shadow: 0 2px 8px rgba(102,126,234,0.15);
        background: #fff;
      }
    }
    .el-input__inner, .el-select__selected-value, .el-date-editor input {
      font-size: 15px;
      color: #334155;
      &::placeholder {
        color: #b6bdd3;
        font-weight: 400;
      }
    }
  }
  .el-rate {
    font-size: 28px;
    margin-top: 2px;
    .el-rate__icon {
      filter: drop-shadow(0 2px 6px rgba(102,126,234,0.10));
    }
  }
  .el-upload {
    .el-upload-dragger {
      border-radius: 10px;
      background: #f8fafc;
      border: 1px dashed #d1d5db;
      transition: border-color 0.3s;
      &:hover {
        border-color: #667eea;
      }
    }
  }
}
.el-tabs {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  margin-bottom: 24px;
  padding: 0 32px 16px 32px;
  .el-tabs__header {
    margin-bottom: 12px;
    .el-tabs__item {
      font-weight: 600;
      font-size: 15px;
      color: #667eea;
      border-radius: 8px 8px 0 0;
      background: #f8fafc;
      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
      }
    }
  }
}
.el-button {
  min-width: 96px;
  height: 40px;
  font-size: 15px;
  border-radius: 8px;
}
@media (max-width: 900px) {
  :deep(.el-dialog__body), .el-form, .el-tabs {
    padding: 12px !important;
  }
}
</style>
