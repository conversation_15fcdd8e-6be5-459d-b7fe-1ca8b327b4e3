<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="面试时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.interviewTime`"
            :rules="formRules.interviewTime"
            class="mb-0px!"
          >
            <el-date-picker
              v-model="row.interviewTime"
              type="datetime"
              value-format="x"
              placeholder="选择面试时间"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="面试评价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.interviewEvaluation`"
            :rules="formRules.interviewEvaluation"
            class="mb-0px!"
          >
            <el-input
              type="textarea"
              :rows="6"
              v-model="row.interviewEvaluation"
              placeholder="请输入面试评价"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="面试结果（通过/未通过/待定）" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.interviewResult`"
            :rules="formRules.interviewResult"
            class="mb-0px!"
          >
            <el-radio-group v-model="row.interviewResult">
              <el-radio-button label="通过">通过</el-radio-button>
              <el-radio-button label="未通过">未通过</el-radio-button>
              <el-radio-button label="待定">待定</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="面试附件" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.interviewAttachment`"
            :rules="formRules.interviewAttachment"
            class="mb-0px!"
          >
            <UploadFile v-model="row.interviewAttachment" :file-size="5120" :limit="5" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link style="color: red">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加人才库面试记录</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { TalentPoolApi } from '@/api/OA/talent'

const props: any = defineProps<{
  talentId: undefined // 关联人才ID（hrm_talent_pool.id）（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any>([])
const formRules = reactive<any>({
  talentId: [{ required: true, message: '关联人才不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.talentId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await TalentPoolApi.getTalentInterviewRecordListByTalentId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row: any = {
    id: undefined,
    talentId: undefined,
    interviewTime: undefined,
    interviewEvaluation: undefined,
    interviewResult: undefined,
    interviewAttachment: undefined
  }
  row.talentId = props.talentId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
