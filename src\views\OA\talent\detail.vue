<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="岗位分类">
        <dict-tag :type="DICT_TYPE.POSITION_CATEGORY" :value="detailData.positionCategory" />
      </el-descriptions-item>
      <el-descriptions-item label="姓名">
        {{ detailData.name }}
      </el-descriptions-item>
      <el-descriptions-item label="星级">
        <el-rate v-model="detailData.star" disabled :max="6" />
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">
        {{ detailData.phone }}
      </el-descriptions-item>
      <el-descriptions-item label="联系邮箱">
        {{ detailData.email }}
      </el-descriptions-item>
      <el-descriptions-item label="联系时间">
        {{ formatDate(detailData.contactTime, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="学历">
        <dict-tag :type="DICT_TYPE.QUALIFICATION" :value="detailData.education" />
      </el-descriptions-item>
      <el-descriptions-item label="简历来源">
        <dict-tag :type="DICT_TYPE.RESUME_SOURCE" :value="detailData.resumeSource" />
      </el-descriptions-item>
      <el-descriptions-item label="附件">
        <FileListPreview :fileUrl="detailData.attachment" />
      </el-descriptions-item>
    </el-descriptions>
    <el-table :data="formData" class="mt-10px">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="面试时间" prop="interviewTime" :formatter="dateFormatter" />
      <el-table-column label="面试评价" prop="interviewEvaluation" />
      <el-table-column label="面试结果（通过/未通过/待定）" prop="interviewResult" />
      <el-table-column label="面试附件" prop="attachment">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate, dateFormatter } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { TalentPoolApi, TalentPoolVO } from '@/api/OA/talent'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'TalentDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const formData = ref<any>()
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await TalentPoolApi.getTalentPool(props.id || queryId)
    formData.value = await TalentPoolApi.getTalentInterviewRecordListByTalentId(detailData.value.id)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}
/** 初始化 **/
onMounted(() => {
  getInfo()
})
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped lang="scss">
.talent-detail-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}
.el-descriptions {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 32px 32px 8px 32px;
  margin-bottom: 24px;
  .el-descriptions__header {
    font-size: 18px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 12px;
  }
  .el-descriptions__label {
    font-weight: 600;
    color: #334155;
    font-size: 15px;
    background: #f8fafc;
    border-radius: 6px 0 0 6px;
    padding: 8px 12px;
  }
  .el-descriptions__content {
    font-size: 15px;
    color: #334155;
    background: #f8fafc;
    border-radius: 0 6px 6px 0;
    padding: 8px 12px;
  }
}
.el-table {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  overflow: hidden;
  margin-bottom: 16px;
  :deep(.el-table__header) th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e2e8f0;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    td {
      border-bottom: 1px solid #f1f5f9;
      padding: 16px 8px;
      font-size: 14px;
      color: #334155;
    }
  }
}
.el-table .el-rate {
  font-size: 22px;
  .el-rate__icon {
    filter: drop-shadow(0 2px 6px rgba(102,126,234,0.10));
  }
}
@media (max-width: 900px) {
  .talent-detail-page {
    padding: 8px;
  }
  .el-descriptions {
    padding: 12px 8px 8px 8px;
  }
  .el-table {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  }
}
</style>
