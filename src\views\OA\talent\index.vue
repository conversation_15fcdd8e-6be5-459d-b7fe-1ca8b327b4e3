<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="岗位分类" prop="positionCategory">
        <el-select
          v-model="queryParams.positionCategory"
          clearable
          placeholder="请输入岗位分类"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.POSITION_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="星级" prop="star">
        <el-select v-model="queryParams.star" clearable placeholder="请选择星级" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TALENT_STAR_RATING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="人才ID" align="center" prop="id" /> -->
      <el-table-column label="岗位分类" align="center" prop="positionCategory">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.POSITION_CATEGORY" :value="scope.row.positionCategory" />
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="联系电话" align="center" prop="phone" width="200px"/>
      <el-table-column label="星级" align="center" prop="star" width="200px">
        <template #default="scope">
          <el-rate v-model="scope.row.star" disabled :max="6" />
        </template>
      </el-table-column>
      <el-table-column label="联系邮箱" align="center" prop="email" />
      <el-table-column
        label="联系时间"
        align="center"
        prop="contactTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="学历" align="center" prop="education">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.QUALIFICATION" :value="scope.row.education" />
        </template>
      </el-table-column>
      <el-table-column label="简历来源" align="center" prop="resumeSource">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RESUME_SOURCE" :value="scope.row.resumeSource" />
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="attachment">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="300">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleEdit(scope.row)"> 详情 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TalentPoolForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TalentPoolApi, TalentPoolVO } from '@/api/OA/talent'
import TalentPoolForm from './TalentPoolForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 人才库 列表 */
defineOptions({ name: 'OATalent' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<TalentPoolVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  positionCategory: undefined,
  name: undefined,
  phone: undefined,
  email: undefined,
  contactTime: [],
  education: undefined,
  resumeSource: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TalentPoolApi.getTalentPoolPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const handleEdit = (row) => {
  router.push({
    name: 'TalentDetail',
    query: {
      id: row.id
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TalentPoolApi.deleteTalentPool(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TalentPoolApi.exportTalentPool(queryParams)
    download.excel(data, '人才库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.talent-list-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}
:deep(.el-form) {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 32px 32px 8px 32px;
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 0 32px;
  align-items: flex-end;
}
:deep(.el-form-item) {
  margin-bottom: 24px;
  margin-right: 0;
  min-width: 180px;
  .el-form-item__label {
    font-weight: 600;
    color: #334155;
    font-size: 15px;
    min-width: 80px;
    white-space: nowrap;
  }
}
:deep(.el-input), :deep(.el-select) {
  width: 240px !important;
  border-radius: 10px !important;
  font-size: 15px;
  .el-input__wrapper, .el-select__wrapper {
    border-radius: 10px !important;
    border: 1px solid #d1d5db !important;
    box-shadow: 0 1px 4px rgba(102,126,234,0.08);
    transition: all 0.3s;
    background: #f8fafc;
    &:hover, &.is-focus {
      border-color: #667eea !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.15);
      background: #fff;
    }
  }
  .el-input__inner, .el-select__selected-value {
    font-size: 15px;
    color: #334155;
    &::placeholder {
      color: #b6bdd3;
      font-weight: 400;
    }
  }
}
:deep(.el-form-item:last-child) {
  flex-basis: 100%;
  margin-top: 8px;
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
  display: flex;
  gap: 16px;
  background: transparent;
}
.el-table {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  overflow: hidden;
  margin-bottom: 16px;
  :deep(.el-table__header) th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e2e8f0;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    td {
      border-bottom: 1px solid #f1f5f9;
      padding: 16px 8px;
      font-size: 14px;
      color: #334155;
    }
  }
}
.el-table .el-rate {
  font-size: 22px;
  .el-rate__icon {
    filter: drop-shadow(0 2px 6px rgba(102,126,234,0.10));
  }
}
.el-table .el-button {
  min-width: 72px;
  padding: 0 12px;
  font-size: 13px;
  height: 32px;
  border-radius: 6px;
}
.el-table .el-button + .el-button {
  margin-left: 8px;
}
.Pagination {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  padding: 16px 0;
  margin-top: 0;
}
@media (max-width: 900px) {
  .talent-list-page {
    padding: 8px;
  }
  :deep(.el-form) {
    padding: 12px 8px 8px 8px;
    gap: 0 8px;
  }
  :deep(.el-form-item) {
    margin-bottom: 12px;
    min-width: 100px;
    .el-form-item__label {
      font-size: 14px;
    }
  }
  :deep(.el-input), :deep(.el-select) {
    width: 100% !important;
    min-width: 0;
    font-size: 14px;
  }
  :deep(.el-form-item:last-child) {
    padding-top: 8px;
    gap: 8px;
  }
  .el-table {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  }
  .Pagination {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(102,126,234,0.04);
    padding: 8px 0;
  }
}
</style>
