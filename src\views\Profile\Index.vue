<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1 class="profile-title">{{ t('profile.user.title') }}</h1>
    </div>
    <div class="profile-content flex">
      <el-card class="user w-1/3" shadow="never">
        <template #header>
          <div class="card-header">
            <span>个人名片</span>
          </div>
        </template>
        <ProfileUser />
      </el-card>
      <el-card class="user ml-5 w-2/3" shadow="never">
        <template #header>
          <div class="card-header">
            <span>{{ t('profile.info.title') }}</span>
          </div>
        </template>
        <div>
          <el-tabs v-model="activeName" class="profile-tabs" style="min-height: 450px" tab-position="top">
            <el-tab-pane :label="t('profile.info.basicInfo')" name="basicInfo">
              <BasicInfo />
            </el-tab-pane>
            <el-tab-pane :label="t('profile.info.resetPwd')" name="resetPwd">
              <ResetPwd />
            </el-tab-pane>
            <!-- <el-tab-pane :label="t('profile.info.userSocial')" name="userSocial">
              <UserSocial v-model:activeName="activeName" />
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { BasicInfo, ProfileUser, ResetPwd, UserSocial } from './components'

const { t } = useI18n()
defineOptions({ name: 'Profile' })
const activeName = ref('basicInfo')
const route = useRoute()
onMounted(() => {
  if(route.query.tab){
    activeName.value = route.query.tab as string
  }
  // activeName.value = route.query.activeName as string
})
</script>
<style scoped>
.profile-container {
  padding: 30px 20px;
  background-color: #f8f9fa;
  min-height: calc(100vh - 120px);
}

.profile-header {
  margin-bottom: 30px;
  text-align: center;
}

.profile-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  letter-spacing: 0.5px;
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
}

.user {
  max-height: 960px;
  padding: 20px 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  background: #ffffff;
  border: none;
  transition: transform 0.3s ease;
}

.user:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-card .el-card__header) {
  border-bottom: none;
  padding: 20px 25px 15px !important;
}

:deep(.el-card .el-card__body) {
  padding: 25px !important;
}

.profile-tabs {
  --el-tabs-header-height: 50px;
}

.profile-tabs > .el-tabs__content {
  padding: 25px;
  font-weight: 500;
  color: #34495e;
  font-size: 15px;
}

.el-tabs--left .el-tabs__content {
  height: 100%;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  font-weight: 500;
  color: #7f8c8d;
  padding: 0 25px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) {
  color: #3498db;
  font-weight: 600;
}

:deep(.el-tabs__item:hover) {
  color: #3498db;
}

:deep(.el-tabs__active-bar) {
  background-color: #3498db;
  height: 3px;
  border-radius: 3px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #ebeef5;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
  }
  
  .user {
    width: 100% !important;
    margin-left: 0 !important;
    margin-bottom: 20px;
  }
}
</style>
