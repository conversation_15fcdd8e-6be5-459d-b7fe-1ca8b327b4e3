<template>
  <div class="overtime-create">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <svg-icon name="ep:clock" />
        </div>
        <div class="header-text">
          <h1>加班申请</h1>
          <p>请填写加班申请信息，提交后将进入审批流程</p>
        </div>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="160px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 加班信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:document" />
            </div>
            <h3>加班信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="加班事由及工作内容" prop="overtimeReason">
                  <el-input
                    :disabled="reviewFlag"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6 }"
                    v-model="formData.overtimeReason"
                    placeholder="请详细描述加班事由及工作内容"
                    class="modern-textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 时间安排区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:calendar" />
            </div>
            <h3>时间安排</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="加班开始时间" prop="startDate">
                  <el-date-picker
                    :disabled="reviewFlag"
                    class="modern-date-picker"
                    v-model="formData.startDate"
                    type="datetime"
                    value-format="x"
                    placeholder="选择加班开始时间"
                    :disabled-date="disablePastDates"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="加班结束时间" prop="endDate">
                  <el-date-picker
                    :disabled="reviewFlag"
                    class="modern-date-picker"
                    v-model="formData.endDate"
                    type="datetime"
                    value-format="x"
                    placeholder="选择加班结束时间"
                    :disabled-date="disablePastDates"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="加班时长" prop="totalHours">
                  <el-input
                    :disabled="reviewFlag"
                    v-model="formData.totalHours"
                    placeholder="请输入加班时长（小时）"
                    class="modern-input"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="time-duration" v-if="formData.startDate && formData.endDate">
              <div class="duration-info">
                <svg-icon name="ep:timer" class="duration-icon" />
                <span>预计加班时长：{{ calculateDuration() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 实际执行区域 -->
        <div class="form-section" v-if="tasks && tasks.length > 0 && tasks.length !== 1">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:finished" />
            </div>
            <h3>实际执行情况</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="实际开始时间" prop="actualStartDate">
                  <el-date-picker
                    :disabled="tasks && tasks.length > 2"
                    class="modern-date-picker"
                    v-model="formData.actualStartDate"
                    type="datetime"
                    value-format="x"
                    placeholder="选择实际开始时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际结束时间" prop="actualEndDate">
                  <el-date-picker
                    :disabled="tasks && tasks.length > 2"
                    class="modern-date-picker"
                    v-model="formData.actualEndDate"
                    type="datetime"
                    value-format="x"
                    placeholder="选择实际结束时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际加班时长" prop="actualTotalHours">
                  <el-input
                    :disabled="tasks && tasks.length > 2"
                    v-model="formData.actualTotalHours"
                    placeholder="请输入实际加班时长（小时）"
                    class="modern-input"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="实际完成内容" prop="actualCompletionContent">
                  <el-input
                    :disabled="tasks && tasks.length > 2"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6 }"
                    v-model="formData.actualCompletionContent"
                    placeholder="请描述实际完成的工作内容"
                    class="modern-textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="time-duration" v-if="formData.actualStartDate && formData.actualEndDate">
              <div class="duration-info">
                <svg-icon name="ep:timer" class="duration-icon" />
                <span>实际加班时长：{{ calculateActualDuration() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 提交按钮区域 -->
        <div class="form-actions" v-if="!reviewFlag">
          <el-button @click="handleClose" class="cancel-btn">
            <svg-icon name="ep:close" class="btn-icon" />
            取 消
          </el-button>
          <el-button @click="submitForm" type="primary" :disabled="formLoading" class="submit-btn">
            <svg-icon name="ep:check" class="btn-icon" />
            提 交 申 请
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { OaOvertimeApplicationApi, OaOvertimeApplicationVO } from '@/api/bpm/overtimeApplication'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'OAOvertimeApplicationCreate' })

const props = defineProps({
  reviewFlag: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number
  },
  tasks: {
    type: Array
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { currentRoute, back } = useRouter() // 路由
const route = useRoute()

const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const formData = ref({
  id: undefined,
  userId: undefined,
  deptId: undefined,
  deptName: undefined,
  overtimeReason: undefined,
  startDate: undefined,
  endDate: undefined,
  totalHours: undefined,
  actualStartDate: undefined,
  actualEndDate: undefined,
  actualTotalHours: undefined,
  actualCompletionContent: undefined,
  processInstanceId: undefined
})

const formRules = reactive({
  userId: [{ required: true, message: '申请人的用户编号不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '申请人所属部门编号不能为空', trigger: 'blur' }],
  deptName: [{ required: true, message: '申请人所属部门名称不能为空', trigger: 'blur' }],
  overtimeReason: [{ required: true, message: '加班事由及工作内容不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '加班开始时间不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '加班结束时间不能为空', trigger: 'blur' }],
  totalHours: [{ required: true, message: '加班时长不能为空', trigger: 'blur' }],
  actualStartDate: [{ required: true, message: '实际开始时间不能为空', trigger: 'blur' }],
  actualEndDate: [{ required: true, message: '实际结束时间不能为空', trigger: 'blur' }],
  actualTotalHours: [{ required: true, message: '实际加班时长不能为空', trigger: 'blur' }],
  actualCompletionContent: [{ required: true, message: '实际完成内容不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

// 禁用过去的日期
const disablePastDates = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000 // 禁用昨天及之前的日期
}

// 计算预计加班时长
const calculateDuration = () => {
  if (!formData.value.startDate || !formData.value.endDate) return ''
  
  const start = new Date(formData.value.startDate)
  const end = new Date(formData.value.endDate)
  const diff = end.getTime() - start.getTime()
  
  if (diff <= 0) return '时间设置有误'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else {
    return `${minutes}分钟`
  }
}

// 计算实际加班时长
const calculateActualDuration = () => {
  if (!formData.value.actualStartDate || !formData.value.actualEndDate) return ''
  
  const start = new Date(formData.value.actualStartDate)
  const end = new Date(formData.value.actualEndDate)
  const diff = end.getTime() - start.getTime()
  
  if (diff <= 0) return '时间设置有误'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else {
    return `${minutes}分钟`
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 验证时间逻辑
  if (formData.value.startDate && formData.value.endDate) {
    if (formData.value.startDate >= formData.value.endDate) {
      message.error('结束时间必须晚于开始时间')
      return
    }
  }
  
  if (formData.value.actualStartDate && formData.value.actualEndDate) {
    if (formData.value.actualStartDate >= formData.value.actualEndDate) {
      message.error('实际结束时间必须晚于实际开始时间')
      return
    }
  }
  
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OaOvertimeApplicationVO as any
    if (props.reviewFlag) {
      await OaOvertimeApplicationApi.updateOaOvertimeApplication(data)
    } else {
      delete data.id
      await OaOvertimeApplicationApi.createOaOvertimeApplication(data)
      message.success(t('common.createSuccess'))
      // 发送操作成功的事件
      handleClose()
    }
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  if (props.reviewFlag) {
    formData.value = await OaOvertimeApplicationApi.getOaOvertimeApplication(props.id as any)
  } else {
    if (route.query.id) {
      formData.value = await OaOvertimeApplicationApi.getOaOvertimeApplication(
        route.query.id as any
      )
    }
  }
})

defineExpose({ submitForm, formRef })
</script>

<style lang="scss" scoped>
.overtime-create {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.page-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .header-content {
    display: flex;
    align-items: center;
    
    .header-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      
      .svg-icon {
        width: 32px;
        height: 32px;
        color: white;
      }
    }
    
    .header-text {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      p {
        margin: 0;
        font-size: 16px;
        color: #64748b;
        line-height: 1.5;
      }
    }
  }
}

.form-container {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.modern-form {
  .form-section {
    margin-bottom: 40px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    .section-header {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      
      .section-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
    
    .section-content {
      padding: 24px;
      
      .el-row {
        .el-col {
          margin-bottom: 8px;
        }
      }
      
      .time-duration {
        margin-top: 16px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
        border-radius: 8px;
        border-left: 4px solid #667eea;
        
        .duration-info {
          display: flex;
          align-items: center;
          color: #1e293b;
          font-weight: 500;
          
          .duration-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #667eea;
          }
        }
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #475569;
      font-size: 14px;
    }
    
    .el-form-item__content {
      .modern-input {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
        
        .el-input__inner {
          font-size: 14px;
          color: #1e293b;
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
      
      .modern-date-picker {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-textarea {
        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          font-size: 14px;
          color: #1e293b;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
  
  .cancel-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid #d1d5db;
    color: #475569;
    background: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      border-color: #94a3b8;
      color: #1e293b;
      background: #f8fafc;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
  
  .submit-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overtime-create {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px;
    
    .header-content {
      flex-direction: column;
      text-align: center;
      
      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
      
      .header-text {
        h1 {
          font-size: 24px;
        }
        
        p {
          font-size: 14px;
        }
      }
    }
  }
  
  .form-container {
    padding: 24px;
  }
  
  .modern-form {
    .form-section {
      .section-header {
        padding: 16px 20px;
        
        .section-icon {
          width: 28px;
          height: 28px;
          
          .svg-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        h3 {
          font-size: 15px;
        }
      }
      
      .section-content {
        padding: 20px;
      }
    }
  }
  
  .form-actions {
    flex-direction: column;
    
    .cancel-btn,
    .submit-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
