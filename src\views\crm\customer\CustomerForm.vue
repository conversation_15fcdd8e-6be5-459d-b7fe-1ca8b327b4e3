<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1200px" class="customer-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        class="customer-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><User /></el-icon>
            </div>
            <h2 class="section-title">基本信息</h2>
            <div class="section-line"></div>
          </div>

          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="客户名称" prop="name" class="custom-form-item">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.name"
                    placeholder="请填写客户名称"
                    class="custom-input"
                    size="large"
                  />
                  <el-button
                    type="primary"
                    @click="handleCheck(formData.name)"
                    class="check-button"
                    size="large"
                  >
                    <Icon class="mr-5px" icon="ep:search" />
                    客户查重
                  </el-button>
                </div>
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="客户性质" prop="customerNature" class="custom-form-item">
                <el-select
                  v-model="formData.customerNature"
                  placeholder="请选择客户性质"
                  class="custom-select"
                  size="large"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CUSTOMER_NATURE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="客户行业" prop="industryId" class="custom-form-item">
                <el-cascader
                  v-model="formData.industryId"
                  :options="industryList"
                  :props="industryProps"
                  class="custom-cascader"
                  clearable
                  filterable
                  placeholder="请选择行业"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="客户级别" prop="level" class="custom-form-item">
                <el-select
                  v-model="formData.level"
                  placeholder="请选择客户级别"
                  class="custom-select"
                  size="large"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CRM_CUSTOMER_LEVEL)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="地区" prop="areaId" class="custom-form-item">
                <el-cascader
                  v-model="formData.areaId"
                  :options="areaList"
                  :props="defaultProps"
                  class="custom-cascader"
                  clearable
                  filterable
                  placeholder="请选择城市"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="详细地址" prop="detailAddress" class="custom-form-item">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.detailAddress"
                    disabled
                    placeholder="请选择详细地址"
                    class="custom-input"
                    size="large"
                  />
                  <el-button
                    type="primary"
                    @click="chooseLocation('detailAddress')"
                    class="location-button"
                    size="large"
                  >
                    <el-icon><Location /></el-icon>
                    选择地址
                  </el-button>
                </div>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="社会信用代码" prop="socialCreditCode" class="custom-form-item">
                <el-input
                  v-model="formData.socialCreditCode"
                  placeholder="请输入社会信用代码"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="客户来源" prop="source" class="custom-form-item">
                <el-select
                  v-model="formData.source"
                  placeholder="请选择客户来源"
                  class="custom-select"
                  size="large"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CRM_CUSTOMER_SOURCE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="所属上级" prop="superiorLevelName" class="custom-form-item">
                <el-input
                  v-model="formData.superiorLevelName"
                  placeholder="请输入所属上级"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="合伙人" prop="partner" class="custom-form-item">
                <el-input
                  v-model="formData.partner"
                  placeholder="请输入合伙人"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="公司" prop="company" class="custom-form-item">
                <el-input
                  v-model="formData.company"
                  placeholder="请输入公司"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 客户联系人信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Phone /></el-icon>
            </div>
            <h2 class="section-title">客户联系人信息</h2>
            <div class="section-line"></div>
          </div>

          <div class="form-grid">
            <div class="form-item-wrapper full-width">
              <el-form-item label="联系人1" prop="contacts1" class="custom-form-item">
                <el-input
                  v-model="formData.contacts1"
                  placeholder="请输入联系人"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="手机/电话1" prop="mobile" class="custom-form-item">
                <el-input
                  v-model="formData.mobile"
                  placeholder="请输入手机"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="联系方式1" prop="contact1" class="custom-form-item">
                <el-input
                  v-model="formData.contact1"
                  placeholder="请输入联系方式"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="所属部门1" prop="dept1" class="custom-form-item">
                <el-input
                  v-model="formData.dept1"
                  placeholder="请输入所属部门"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="所属岗位1" prop="position1" class="custom-form-item">
                <el-input
                  v-model="formData.position1"
                  placeholder="请输入所属岗位"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper full-width">
              <el-form-item label="联系人2" prop="contacts2" class="custom-form-item">
                <el-input
                  v-model="formData.contacts2"
                  placeholder="请输入联系人"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="手机/电话  2" prop="mobile2" class="custom-form-item">
                <el-input
                  v-model="formData.mobile2"
                  placeholder="请输入手机"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="联系方式2" prop="contact2" class="custom-form-item">
                <el-input
                  v-model="formData.contact2"
                  placeholder="请输入联系方式"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="所属部门2" prop="dept2" class="custom-form-item">
                <el-input
                  v-model="formData.dept2"
                  placeholder="请输入所属部门"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="所属岗位2" prop="position2" class="custom-form-item">
                <el-input
                  v-model="formData.position2"
                  placeholder="请输入所属岗位"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 销售负责人信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <h2 class="section-title">销售负责人信息</h2>
            <div class="section-line"></div>
          </div>

          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="负责人" prop="ownerUserId" class="custom-form-item">
                <el-select
                  v-model="formData.ownerUserId"
                  :disabled="formType !== 'create'"
                  class="custom-select"
                  filterable
                  size="large"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="下次联系时间" prop="contactNextTime" class="custom-form-item">
                <el-date-picker
                  v-model="formData.contactNextTime"
                  placeholder="选择下次联系时间"
                  type="datetime"
                  value-format="x"
                  class="custom-date-picker"
                  size="large"
                />
              </el-form-item>
            </div>

            <div class="form-item-wrapper full-width">
              <el-form-item label="备注" prop="remark" class="custom-form-item">
                <el-input
                  type="textarea"
                  v-model="formData.remark"
                  placeholder="请输入备注"
                  class="custom-textarea"
                  :rows="4"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          :disabled="formLoading"
          type="primary"
          @click="submitForm"
          class="submit-button"
          size="large"
        >
          <el-icon><Check /></el-icon>
          确认提交
        </el-button>
        <el-button @click="dialogVisible = false" class="cancel-button" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </div>
    </template>

    <Map ref="mapChooseRef" @btnAddress="chooseLocationDone" />
    <CheckDialog ref="checkDialogRef" />
  </Dialog>
</template>

<script lang="ts" setup>
import type { CascaderProps } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as CustomerApi from '@/api/crm/customer'
import * as AreaApi from '@/api/system/area'
import { defaultProps, nameValueProps, handleTree, industryProps } from '@/utils/tree'
import * as UserApi from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'
import { IndustryApi } from '@/api/system/industry'
import CheckDialog from './checkDialog.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const customerList = ref<any>([]) // 客户列表的数据
const checkDialogRef = ref()
const industryList = ref([])
const mapChooseRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const areaList = ref([]) // 地区列表
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
const formData = ref({
  id: undefined,
  name: undefined,
  partner: undefined,
  company: undefined,
  contactNextTime: undefined,
  ownerUserId: 0,
  industry: undefined,
  mobile: undefined,
  telephone: undefined,
  qq: undefined,
  wechat: undefined,
  email: undefined,
  areaId: undefined,
  detailAddress: undefined,
  industryId: undefined,
  level: undefined,
  source: undefined,
  remark: undefined,
  contacts1: undefined,
  contacts2: undefined,
  socialCreditCode: undefined,
  businessLongitude: undefined,
  businessLatitude: undefined,
  customerNature: undefined,
  // firstSigningDate: undefined,
  superiorLevelId: undefined,
  superiorLevelName: undefined,
  contact1: undefined,
  contact2: undefined,
  dept1: undefined,
  dept2: undefined,
  position1: undefined,
  position2: undefined,
  mobile2: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
  customerNature: [{ required: true, message: '客户性质不能为空', trigger: 'change' }],
  industryId: [{ required: true, message: '客户行业不能为空', trigger: 'change' }],
  level: [{ required: true, message: '客户级别不能为空', trigger: 'change' }],
  areaId: [{ required: true, message: '地区不能为空', trigger: 'change' }],
  detailAddress: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
  source: [{ required: true, message: '客户来源不能为空', trigger: 'change' }],
  socialCreditCode: [{ required: true, message: '社会信用代码不能为空', trigger: 'blur' }],
  contacts1: [{ required: true, message: '联系人1不能为空', trigger: 'blur' }],
  mobile: [{ required: true, message: '手机/电话1不能为空', trigger: 'blur' }],
  // contact1: [{ required: true, message: '联系方式1不能为空', trigger: 'blur' }],
  dept1: [{ required: true, message: '所属部门1不能为空', trigger: 'blur' }],
  position1: [{ required: true, message: '所属岗位1不能为空', trigger: 'blur' }],
  ownerUserId: [{ required: true, message: '负责人不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const props: CascaderProps = {
  ...industryProps,
  lazy: true,
  emitPath: false,
  checkStrictly: true,
  async lazyLoad(node: any, resolve) {
    const { level, data } = node
    const parentId = data?.id
    if (!parentId) {
      const parRes = await getIndustryList()
      resolve(parRes)
    } else {
      const res = await getNot0IndustryList(data.id)
      const nodes = res.map((item) => ({
        ...item,
        leaf: level >= 1
      }))
      resolve(nodes)
    }
  }
}

const handleCheck = (name) => {
  checkDialogRef.value.open(name)
}

/** 查询行业列表 */
const getIndustryList = async () => {
  try {
    const data = await IndustryApi.getCategory0List()
    return handleTree(data)
  } finally {
  }
}
const getNot0IndustryList = async (parentId) => {
  try {
    const data = await IndustryApi.getCategoryNot0List({ parentId })
    return handleTree(data)
  } finally {
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomerApi.getCustomer(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得客户列表
  // customerList.value = await CustomerApi.getCustomerSimpleList()
  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
  industryList.value = await IndustryApi.getIndustryTree()
  // 获得用户列表
  userOptions.value = await UserApi.getSimpleUserList()
  // 默认新建时选中自己
  if (formType.value === 'create') {
    formData.value.ownerUserId = useUserStore().getUser.id
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CustomerApi.CustomerVO
    if (formType.value === 'create') {
      await CustomerApi.createCustomer(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomerApi.updateCustomer(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const chooseLocation = (key) => {
  mapChooseRef.value.openDialog(key)
}

const changeSelect = (val) => {
  formData.value.superiorLevelName = customerList.value.find((item) => item.id === val).name
}

const chooseLocationDone = (coord, addresstext, adcode, field) => {
  if (!coord || !addresstext || !field) return
  const coordArr = coord.split(',')
  formData.value.businessLongitude = coordArr[0]
  formData.value.businessLatitude = coordArr[1]
  formData.value[field] = addresstext
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    partner: undefined,
    company: undefined,
    contactNextTime: undefined,
    ownerUserId: 0,
    industry: undefined,
    mobile: undefined,
    telephone: undefined,
    qq: undefined,
    wechat: undefined,
    email: undefined,
    areaId: undefined,
    detailAddress: undefined,
    industryId: undefined,
    level: undefined,
    source: undefined,
    remark: undefined,
    contacts1: undefined,
    contacts2: undefined,
    socialCreditCode: undefined,
    businessLongitude: undefined,
    businessLatitude: undefined,
    // firstSigningDate: undefined,
    customerNature: undefined,
    superiorLevelId: undefined,
    superiorLevelName: undefined,
    contact1: undefined,
    contact2: undefined,
    dept1: undefined,
    dept2: undefined,
    position1: undefined,
    position2: undefined,
    mobile2: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.customer-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;

          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      background: #f8fafc;
    }

    .el-dialog__footer {
      background: white;
      padding: 24px 32px;
      border-top: 1px solid #e5e7eb;
    }
  }
}

.form-container {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.customer-form {
  .form-section {
    background: white;
    border-radius: 0;
    padding: 24px 32px;
    margin-bottom: 0;
    box-shadow: none;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: none;
      transform: none;
    }

    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

      .el-icon {
        font-size: 20px;
        color: white;
      }
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
      flex: 1;
    }

    .section-line {
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      border-radius: 1px;
      flex: 1;
      margin-left: 16px;
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    .form-item-wrapper {
      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }

  .custom-form-item {
    margin-bottom: 0;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
      line-height: 1.5;
    }

    :deep(.el-form-item__content) {
      line-height: 1;
    }

    :deep(.el-form-item__error) {
      font-size: 12px;
      color: #ef4444;
      margin-top: 4px;
    }
  }

  .custom-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #d1d5db;
        background: white;
      }

      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;

      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  .custom-select {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #d1d5db;
        background: white;
      }

      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }

  .custom-cascader {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #d1d5db;
        background: white;
      }

      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }

  .custom-date-picker {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #d1d5db;
        background: white;
      }

      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }

  .custom-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      font-size: 14px;
      color: #1f2937;
      resize: vertical;

      &:hover {
        border-color: #d1d5db;
        background: white;
      }

      &:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  .input-with-button {
    display: flex;
    gap: 12px;
    align-items: flex-end;

    .custom-input {
      flex: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .customer-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }

  .customer-form {
    .form-section {
      padding: 20px;
    }

    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .input-with-button {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .dialog-footer {
    flex-direction: column;

    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}
</style>
