<template>
  <el-dialog
    title="客户查重"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
    center
  >
    <el-form @submit.prevent class="mb-5px" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="客户名">
        <el-input
          v-model="name"
          @change="handleQuery"
          placeholder="手机、电话、客户名称"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column label="公司名称" align="center" prop="name" />
      <!-- <el-table-column label="负责人" align="center" prop="ownerUserId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.ownerUserId)?.nickname }}</span>
        </template>
      </el-table-column> -->
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="chooseCustomer">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({ name: 'CustomerDialog' })

let dialogVisible = ref(false)
import * as CustomerApi from '@/api/crm/customer'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE } from '@/utils/dict'

const emit = defineEmits(['fetch-data'])

const message = useMessage() // 消息弹窗
let currentRow = ref()
const loading = ref(false) // 列表的加载中
const list = ref<any>([]) // 列表的数据
let name = ref(undefined)
const queryFormRef = ref() // 搜索的表单
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 查询列表 */
const getList = async () => {
  if (!name.value) return
  loading.value = true
  try {
    const data = await CustomerApi.getCustomerDuplicateCheckPage({ name: name.value })
    list.value = data
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  name.value = undefined
  handleQuery()
}

const open = async (data?) => {
  name.value = undefined
  list.value = []
  dialogVisible.value = true
  if (data) {
    name.value = data
    getList()
  }

  userList.value = await UserApi.getSimpleUserList()
}

const handleCurrentChange = (val) => {
  currentRow.value = val
}

const chooseCustomer = () => {
  if (currentRow.value) {
    dialogVisible.value = false
    emit('fetch-data', currentRow.value)
    message.success('选择成功')
  } else {
    message.error('请选择客户')
  }
}

defineExpose({
  open
})
</script>
