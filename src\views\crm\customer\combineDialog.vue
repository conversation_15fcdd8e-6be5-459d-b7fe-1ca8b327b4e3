<template>
  <el-dialog
    title="客户合并"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
    center
  >
    <el-alert title="提示：" type="warning">
      <template #default>
        <div
          >1、合并后系统只保留目标客户，同时将另一个客户的联系人、商机、合同、附件、销售动态等迁移到目标客户。</div
        >
        <div style="margin-top: 10px">2、红色字段表示两个客户该字的值不同。</div>
      </template>
    </el-alert>
    <div style="margin: 30px 0 10px 0">请选择合并后需要保留的值：</div>
    <el-descriptions :column="1" border>
      <el-descriptions-item
        v-for="(field, index) in fields"
        :label-class-name="
          selectedRows[0][field.value] !== selectedRows[1][field.value] ? 'my-label' : ''
        "
        :label="field.label"
      >
        <div v-if="selectedRows[0][field.value] === null && selectedRows[1][field.value] === null"
          >当前两个客户该字段都为空</div
        >
        <div v-else-if="selectedRows[0][field.value] === selectedRows[1][field.value]"
          >当前两个客户该字段值相同</div
        >
        <el-radio-group v-else v-model="formData[field.value]">
          <el-radio v-for="item in selectedRows" :value="item[field.value]">
            <span v-if="field.value === 'customerNature'">{{
              getDictLabel(DICT_TYPE.CUSTOMER_NATURE, item.customerNature)
            }}</span>
            <span v-else-if="field.value === 'level'">{{
              getDictLabel(DICT_TYPE.CRM_CUSTOMER_LEVEL, item.level)
            }}</span>
            <span v-else-if="field.value === 'source'">{{
              getDictLabel(DICT_TYPE.CRM_CUSTOMER_SOURCE, item.source)
            }}</span>
            <span v-else-if="field?.show">{{ item[field.show] }}</span>
            <span v-else>{{ item[field.value] }}</span>
          </el-radio>
        </el-radio-group>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="combineCustomer">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import * as CustomerApi from '@/api/crm/customer'

defineOptions({ name: 'CustomerDialog' })
const emit = defineEmits(['success'])
let dialogVisible = ref(false)

const message = useMessage() // 消息弹窗
let selectedRows = ref()

let formData = ref({
  id: undefined,
  deleteCustomerId: undefined,
  name: undefined,
  socialCreditCode: undefined,
  customerNature: undefined,
  industryId: undefined,
  level: undefined,
  areaId: undefined,
  detailAddress: undefined,
  superiorLevelName: undefined,
  source: undefined,
  partner: undefined,
  company: undefined,
  contacts1: undefined,
  mobile: undefined,
  contact1: undefined,
  dept1: undefined,
  position1: undefined,
  contacts2: undefined,
  mobile2: undefined,
  contact2: undefined,
  dept2: undefined,
  position2: undefined,
  ownerUserId: undefined
})

let fields = ref([
  { label: '客户名称', value: 'name' },
  { label: '社会信用代码', value: 'socialCreditCode' },
  { label: '客户性质', value: 'customerNature' },
  { label: '客户行业', value: 'industryId', show: 'industryName' },
  { label: '客户级别', value: 'level' },
  { label: '地区', value: 'areaId', show: 'areaName' },
  { label: '详细地址', value: 'detailAddress' },
  { label: '所属上级', value: 'superiorLevelName' },
  { label: '客户来源', value: 'source' },
  { label: '合伙人', value: 'partner' },
  { label: '公司', value: 'company' },
  { label: '联系人1', value: 'contacts1' },
  { label: '手机1', value: 'mobile' },
  { label: '联系方式1', value: 'contact1' },
  { label: '所属部门1', value: 'dept1' },
  { label: '所属岗位1', value: 'position1' },
  { label: '联系人2', value: 'contacts2' },
  { label: '手机2', value: 'mobile2' },
  { label: '联系方式2', value: 'contact2' },
  { label: '所属部门2', value: 'dept2' },
  { label: '所属岗位2', value: 'position2' },
  { label: '销售负责人', value: 'ownerUserId', show: 'ownerUserName' }
])

const open = async (data) => {
  console.log('data', data)

  selectedRows.value = data
  dialogVisible.value = true
  //默认选择第一项
  Object.keys(formData.value).forEach((key) => {
    if (data[0].hasOwnProperty(key)) {
      if (data[0][key] === null) {
        formData.value[key] = undefined
      } else {
        formData.value[key] = data[0][key]
      }
    }
  })
  formData.value.deleteCustomerId = data[1].id
}

const combineCustomer = async () => {
  console.log('formData', formData.value)
  try {
    // 删除的二次确认
    await message.confirm('确认合并吗')
    // 发起删除
    await CustomerApi.combineCustomer(formData.value)
    message.success('合并成功')
    dialogVisible.value = false
    // 刷新列表
    emit('success')
  } catch {}
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
:deep(.my-label) {
  color: red !important;
}
</style>
