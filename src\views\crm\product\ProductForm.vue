<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1280px" class="business-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="business-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Goods /></el-icon>
            </div>
            <h2 class="section-title">基本信息</h2>
            <div class="section-line"></div>
          </div>
          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="产品名称" prop="name" class="custom-form-item">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入产品名称"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="负责人" prop="ownerUserId" class="custom-form-item">
                <el-select
                  v-model="formData.ownerUserId"
                  placeholder="请选择负责人"
                  :disabled="formData.id"
                  class="custom-select"
                  size="large"
                >
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.nickname"
                    :value="user.id"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="产品类型" prop="categoryId" class="custom-form-item">
                <el-cascader
                  v-model="formData.categoryId"
                  :options="productCategoryList"
                  :props="defaultProps"
                  class="custom-cascader"
                  clearable
                  placeholder="请选择产品类型"
                  filterable
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="产品单位" prop="unit" class="custom-form-item">
                <el-select
                  v-model="formData.unit"
                  class="custom-select"
                  placeholder="请选择单位"
                  size="large"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CRM_PRODUCT_UNIT)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="产品编码" prop="no" class="custom-form-item">
                <el-input
                  v-model="formData.no"
                  placeholder="请输入产品编码"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="价格" prop="price" class="custom-form-item">
                <el-input-number
                  v-model="formData.price"
                  placeholder="请输入价格"
                  :min="0"
                  :precision="2"
                  :step="0.1"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="产品描述" prop="description" class="custom-form-item">
                <el-input
                  v-model="formData.description"
                  placeholder="请输入产品描述"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="上架状态" prop="status" class="custom-form-item">
                <el-select
                  v-model="formData.status"
                  placeholder="请选择状态"
                  class="custom-select"
                  size="large"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CRM_PRODUCT_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="submitForm"
          type="primary"
          :disabled="formLoading"
          class="submit-button"
          size="large"
        >
          <el-icon><Check /></el-icon>
          确认提交
        </el-button>
        <el-button @click="dialogVisible = false" class="cancel-button" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as ProductApi from '@/api/crm/product'
import * as ProductCategoryApi from '@/api/crm/product/category'
import { defaultProps, handleTree } from '@/utils/tree'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'CrmProductForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const userId = useUserStore().getUser.id // 当前登录的编号
const formData = ref({
  id: undefined,
  name: undefined,
  no: undefined,
  unit: undefined,
  price: Number(undefined),
  status: undefined,
  categoryId: undefined,
  description: undefined,
  ownerUserId: -1
})
const formRules = reactive({
  name: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  no: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  categoryId: [{ required: true, message: '产品分类ID不能为空', trigger: 'blur' }],
  ownerUserId: [{ required: true, message: '负责人不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProductApi.getProduct(id)
    } finally {
      formLoading.value = false
    }
  } else {
    formData.value.ownerUserId = userId
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProductApi.ProductVO
    if (formType.value === 'create') {
      await ProductApi.createProduct(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProductApi.updateProduct(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    no: undefined,
    unit: undefined,
    price: Number(undefined),
    status: undefined,
    categoryId: undefined,
    description: undefined,
    ownerUserId: -1
  }
  formRef.value?.resetFields()
}

/** 初始化 */
const productCategoryList = ref<any[]>([]) // 产品分类树
const userList = ref<UserVO[]>([]) // 系统用户
onMounted(async () => {
  // 产品分类树
  const data = await ProductCategoryApi.getProductCategoryList({})
  productCategoryList.value = handleTree(data, 'id', 'parentId')
  // 系统用户列表
  userList.value = await getSimpleUserList()
})
</script>

<style lang="scss" scoped>
.business-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    .el-dialog__body {
      padding: 0;
      background: #f8fafc;
    }
    .el-dialog__footer {
      background: white;
      padding: 24px 32px;
      border-top: 1px solid #e5e7eb;
    }
  }
}
.form-container {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    &:hover {
      background: #a8a8a8;
    }
  }
}
.business-form {
  .form-section {
    background: white;
    border-radius: 0;
    padding: 24px 32px;
    margin-bottom: 0;
    box-shadow: none;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: none;
      transform: none;
    }
    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
  }
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;
    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      .el-icon {
        font-size: 20px;
        color: white;
      }
    }
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
      flex: 1;
    }
    .section-line {
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      border-radius: 1px;
      flex: 1;
      margin-left: 16px;
    }
  }
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    .form-item-wrapper {
      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }
  .custom-form-item {
    margin-bottom: 0;
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
      line-height: 1.5;
    }
    :deep(.el-form-item__content) {
      line-height: 1;
    }
    :deep(.el-form-item__error) {
      font-size: 12px;
      color: #ef4444;
      margin-top: 4px;
    }
  }
  .custom-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-select {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-date-picker {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      font-size: 14px;
      color: #1f2937;
      resize: vertical;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-radio-group {
    :deep(.el-radio) {
      margin-right: 20px;
      .el-radio__label {
        color: #374151;
        font-size: 14px;
      }
      .el-radio__input.is-checked .el-radio__inner {
        border-color: #667eea;
        background: #667eea;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #667eea;
      }
    }
  }
  .input-with-button {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    .custom-input {
      flex: 1;
    }
    .customer-button {
      white-space: nowrap;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
  .product-content {
    padding: 0;
    :deep(.el-tabs) {
      .el-tabs__header {
        margin-bottom: 20px;
        .el-tabs__nav-wrap {
          &::after {
            display: none;
          }
        }
        .el-tabs__item {
          color: #6b7280;
          font-weight: 500;
          &.is-active {
            color: #667eea;
          }
        }
        .el-tabs__active-bar {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
@media (max-width: 768px) {
  .business-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  .business-form {
    .form-section {
      padding: 20px;
    }
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    .input-with-button {
      flex-direction: column;
      align-items: stretch;
    }
  }
  .dialog-footer {
    flex-direction: column;
    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}
</style>
