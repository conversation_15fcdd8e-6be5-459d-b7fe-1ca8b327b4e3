<template>
  <!-- 操作栏 -->
  <el-row justify="end">
    <el-button @click="openForm('create', id, customerName)">
      <Icon class="mr-5px" icon="ep:opportunity" />
      创建报价单
    </el-button>
  </el-row>

  <!-- 列表 -->
  <ContentWrap class="mt-10px">
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="报价单编号" align="center" prop="id" /> -->
      <el-table-column label="报价单名称" align="center" prop="quoteName" width="170px" />

      <el-table-column label="报价总金额" align="center" prop="totalAmount" width="180px" />
      <!-- <el-table-column label="关联客户" align="center" prop="customerName" /> -->
      <el-table-column label="关联商机" align="center" prop="merchantName" width="250px" />
      <el-table-column label="对应联系人" align="center" prop="contactPerson" width="150px" />
      <el-table-column label="报价单附件" align="center" prop="attachment" width="120px">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
      <el-table-column label="项目方案" align="center" prop="projectScheme" width="120px">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.projectScheme" />
        </template>
      </el-table-column>
      <el-table-column
        label="报价时间"
        align="center"
        prop="quoteTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效开始时间"
        align="center"
        prop="validStartTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效结束时间"
        align="center"
        prop="validEndTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <QuoteForm ref="formRef" @success="getList" />
</template>
<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { QuoteApi, QuoteVO } from '@/api/crm/quote'
import QuoteForm from '../QuoteForm.vue'

/** ERP 报价单 列表 */
defineOptions({ name: 'Quote' })

const props = defineProps<{
  customerId: number // 客户
  businessId: number
  customerName: string
  id: number
}>()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<QuoteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  quoteName: undefined,
  quoteTime: [],
  validStartTime: [],
  validEndTime: [],
  totalAmount: undefined,
  customerId: undefined,
  merchantId: undefined,
  contactPerson: undefined,
  attachment: undefined,
  projectScheme: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    if (props.customerId) queryParams.customerId = props.customerId
    if (props.businessId) queryParams.merchantId = props.businessId
    const data = await QuoteApi.getQuotePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, name?: string) => {
  formRef.value.open(type, id, name)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await QuoteApi.deleteQuote(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await QuoteApi.exportQuote(queryParams)
    download.excel(data, 'ERP 报价单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
