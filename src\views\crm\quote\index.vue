<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="报价单名称" prop="quoteName">
        <el-input
          v-model="queryParams.quoteName"
          placeholder="请输入报价单名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="报价单编号" align="center" prop="id" /> -->
      <el-table-column label="报价单名称" align="center" prop="quoteName" width="180px" />

      <el-table-column label="报价总金额" align="center" prop="totalAmount" width="180px" />
      <el-table-column label="关联客户" align="center" prop="customerName" width="300px" />
      <!-- <el-table-column label="关联商机" align="center" prop="merchantName" width="250px">
        <template #default="scope">
          <el-link :underline="false" type="primary" @click="openDetail(scope.row.merchantId)">
            {{ scope.row.merchantName }}
          </el-link>
        </template>
      </el-table-column> -->
      <el-table-column label="对应联系人" align="center" prop="contactPerson" width="150px" />
      <el-table-column label="报价单附件" align="center" prop="attachment" width="120px">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
      <el-table-column label="项目方案" align="center" prop="projectScheme" width="120px">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.projectScheme" />
        </template>
      </el-table-column>
      <el-table-column
        label="报价时间"
        align="center"
        prop="quoteTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效开始时间"
        align="center"
        prop="validStartTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效结束时间"
        align="center"
        prop="validEndTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <QuoteForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { QuoteApi, QuoteVO } from '@/api/crm/quote'
import QuoteForm from './QuoteForm.vue'

/** ERP 报价单 列表 */
defineOptions({ name: 'Quote' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<QuoteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  quoteName: undefined,
  quoteTime: [],
  validStartTime: [],
  validEndTime: [],
  totalAmount: undefined,
  customerId: undefined,
  merchantId: undefined,
  contactPerson: undefined,
  attachment: undefined,
  projectScheme: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 打开客户详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'CrmBusinessDetail', params: { id } })
}

/** 打开客户详情 */
const openCustomerDetail = (id: number) => {
  push({ name: 'CrmCustomerDetail', params: { id } })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await QuoteApi.getQuotePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await QuoteApi.deleteQuote(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await QuoteApi.exportQuote(queryParams)
    download.excel(data, 'ERP 报价单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
