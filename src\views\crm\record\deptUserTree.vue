<!--
 * @Description: DeptTree
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:51
 * @LastEditTime: 2024-06-24 09:44:21
-->
<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门名称">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :current-node-key="100"
      :default-expanded-keys="[100]"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      :highlight-current="true"
      node-key="id"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'

defineOptions({ name: 'SystemDeptUserTree' })

const props = defineProps({
  deptId: {
    type: Number
  }
})
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async () => {
  const res = await DeptApi.getSimpleDeptUserList({
    deptId: props.deptId,
    queryLower: true
  })
  deptList.value = []
  deptList.value.push(...handleTree(res))
  console.log('deptList.value', deptList.value)
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  // if (!row.children || row.children.length === 0) {
  emits('node-click', row)
  // }
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
