<template>
  <ContentWrap>
    <el-row :gutter="20">
      <!-- 左侧部门树 -->
      <el-col :span="4" :xs="24" v-if="onlySeeSelf">
        <ContentWrap class="h-1/1">
          <DeptUserTree ref="deptUserTreeRef" @node-click="handleDeptNodeClick" />
        </ContentWrap>
      </el-col>
      <el-col :span="onlySeeSelf ? 20 : 24" :xs="24">
        <el-tabs v-model="activeName" @tab-change="handleTabChange">
          <el-tab-pane label="工作报告" name="1">
            <el-timeline v-if="workReportList && workReportList.length > 0">
              <el-timeline-item
                v-for="item in workReportList"
                :timestamp="formatDate(item.createTime, 'YYYY-MM-DD HH:mm:ss')"
                placement="top"
                :key="item.id"
              >
                <el-card>
                  <p
                    ><el-tag>{{ item.type }}</el-tag></p
                  >
                  <p style="font-size: 12px; color: #aaa"
                    >报告日期：{{ formatDate(item.reportTime, 'YYYY-MM-DD') }}</p
                  >
                  <p style="font-size: 12px; color: #aaa"
                    >报告人：{{ userList.find((item1) => item1.id === item.userId)?.nickname }}</p
                  >
                  <p style="line-height: 28px"><b>总结：</b>{{ item.summary }}</p>
                  <p style="line-height: 28px"><b>计划：</b>{{ item.plan }}</p>

                  <p v-if="item.image">图片：</p>
                  <p v-if="item.image">
                    <el-image
                      style="width: 50%; height: 50%"
                      :src="item.image"
                      :preview-src-list="[item.image]"
                    />
                  </p>
                  <p v-if="item.attachment">
                    附件：<FileListPreview :fileUrl="item.attachment" />
                  </p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无工作报告" />
          </el-tab-pane>
          <el-tab-pane label="跟进记录" name="2">
            <el-timeline v-if="followupList && followupList.length > 0">
              <el-timeline-item
                v-for="item in followupList"
                :timestamp="formatDate(item.createTime, 'YYYY-MM-DD HH:mm:ss')"
                placement="top"
                :key="item.id"
              >
                <el-card style="cursor: pointer" @click="jumpTo(item)">
                  <p
                    ><el-tag>{{ getDictLabel(DICT_TYPE.CRM_FOLLOW_UP_TYPE, item.type) }}</el-tag></p
                  >
                  <p style="font-size: 12px; color: #aaa">填写人：{{ item.creatorName }}</p>
                  <p v-if="item.bizType"
                    ><b>{{ item.name }}</b></p
                  >
                  <p style="line-height: 28px"
                    ><b>类型：</b
                    >{{ item.bizType === 1 ? '线索' : item.bizType === 2 ? '客户' : '商机' }}</p
                  >
                  <p style="line-height: 28px"><b>跟进内容：</b>{{ item.content }}</p>
                  <p v-if="item.fileUrls"> 附件：<FileListPreview :fileUrl="item.fileUrls" /> </p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无跟进记录" />
          </el-tab-pane>
          <el-tab-pane label="打卡记录" name="3">
            <el-timeline v-if="clockList && clockList.length > 0">
              <el-timeline-item
                v-for="item in clockList"
                :timestamp="formatDate(item.createTime, 'YYYY-MM-DD HH:mm:ss')"
                placement="top"
                :key="item.id"
              >
                <el-card>
                  <p
                    ><el-tag>{{ item.customerName }}</el-tag></p
                  >
                  <p style="line-height: 28px"><b>打卡人：</b>{{ item.userName }}</p>
                  <p style="line-height: 28px"><b>客户地址：</b>{{ item.destination }}</p>
                  <p style="line-height: 28px"><b>打卡地址：</b>{{ item.punchLocation }}</p>
                  <p style="line-height: 28px"><b>偏差：</b>{{ item.distance }} m</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无打卡记录" />
          </el-tab-pane>
        </el-tabs>
        <Pagination
          v-model:limit="pageSize"
          v-model:page="pageNo"
          :total="
            activeName === '1' ? workReportTotal : activeName === '2' ? followupTotal : clockTotal
          "
          @pagination="getWhichList"
        />
      </el-col>
    </el-row>
  </ContentWrap>
</template>
<script lang="ts" setup>
import * as UserApi from '@/api/system/user'
import { FollowUpRecordApi, FollowUpRecordVO } from '@/api/crm/followup'
import { WorkReportApi, WorkReportVO } from '@/api/crm/workReport'
import { ClockApi } from '@/api/clock'
import { formatDate } from '@/utils/formatTime'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { useUserStore } from '@/store/modules/user'
import DeptUserTree from './deptUserTree.vue'

defineOptions({ name: 'CrmRecord' })
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const userStore = useUserStore()
const roles = userStore.getRoles // 当前登录人角色
const user = userStore.getUser // 当前登录人角色
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数
let workReportTotal = ref(0)
let followupTotal = ref(0)
let clockTotal = ref(0)
let pageNo = ref(1)
let pageSize = ref(10)
// 只看自己的日历的角色
const onlySeeSelf = computed(() => {
  return roles.includes('super_admin') || roles.includes('pms')
})
const { currentRoute, push } = useRouter()
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 100,
  mobile: undefined,
  status: undefined,
  deptIds: '124,134,139',
  createTime: [],
  username: onlySeeSelf.value ? undefined : user.nickname
})
let currentId = ref<any>()
let currentDeptId = ref(100)
let activeName = ref('1')
let followupList = ref<any>([])
let workReportList = ref<any>([])
let clockList = ref<any>([])

const handleTabChange = async (e) => {
  pageNo.value = 1
  pageSize.value = 10
  await getWhichList()
}

const handleClick = async (id) => {
  currentId.value = id
  await getWorkReportList()
}

const getWhichList = async () => {
  if (activeName.value === '1') {
    await getWorkReportList()
  } else if (activeName.value === '2') {
    await getFollowUpList()
  } else {
    await getClockList()
  }
}

/** 查询列表 */
// const getList = async () => {
//   loading.value = true
//   try {
//     const data = await UserApi.getUserPage(queryParams)
//     list.value = data.list
//     if (onlySeeSelf.value) {
//       list.value.push({
//         id: undefined,
//         username: '全部'
//       })
//     }
//     currentId.value = data.list[0].id
//   } finally {
//     loading.value = false
//   }
// }

/** 处理部门被点击 */
const handleDeptNodeClick = async (node) => {
  if (!node.userId) {
    currentDeptId.value = node.id
    currentId.value = undefined
  } else {
    currentId.value = node.userId
    currentDeptId.value = undefined
  }
  await getWhichList()
}

/** 查询列表 */
const getFollowUpList = async () => {
  loading.value = true
  try {
    const data = await FollowUpRecordApi.getFollowUpRecordPage1({
      userId: currentId.value,
      deptId: currentDeptId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value
    })
    followupList.value = data.list
    followupTotal.value = data.total
  } finally {
    loading.value = false
  }
}

const jumpTo = ({ bizId, bizType }) => {
  // 根据 bizType 跳转至不同路径
  // 1.线索 2.客户
  switch (bizType) {
    case 1:
      push({ name: 'CrmClueDetail', params: { id: bizId } })
      break
    case 2:
      push({ name: 'CrmCustomerDetail', params: { id: bizId } })
      break
    default:
      break
  }
}

/** 查询列表 */
const getWorkReportList = async () => {
  loading.value = true
  try {
    const data = await WorkReportApi.getWorkReportPage({
      userId: currentId.value,
      deptId: currentDeptId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value
    })
    workReportList.value = data.list
    workReportTotal.value = data.total
  } finally {
    loading.value = false
  }
}
/** 查询列表 */
const getClockList = async () => {
  loading.value = true
  try {
    const data = await ClockApi.getClockRecordPage({
      userId: currentId.value,
      deptId: currentDeptId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value
    })
    clockList.value = data.list
    clockTotal.value = data.total
  } finally {
    loading.value = false
  }
}

/** 初始化 */
onMounted(async () => {
  // await getList()
  await handleClick(onlySeeSelf.value ? currentId.value : user.id)
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style lang="scss" scoped>
.tab-item {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: all 0.3s;
  border-radius: 8px;
  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #536dfe;
    color: #fff;
  }
}
</style>
