<template>
  <div class="workreport-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      class="workreport-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          基本信息
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告时间" prop="reportTime">
              <el-date-picker
                :disabled="reviewFlag"
                v-model="formData.reportTime"
                type="date"
                value-format="x"
                placeholder="选择报告时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select :disabled="reviewFlag" v-model="formData.type" placeholder="请选择类型" style="width: 100%">
                <el-option label="月报" value="月报" />
                <el-option label="周报" value="周报" />
                <el-option label="日报" value="日报" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 总结与计划 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><EditPen /></el-icon>
          总结与计划
        </div>
        <el-form-item label="总结" prop="summary">
          <el-input
            :disabled="reviewFlag"
            type="textarea"
            :rows="6"
            v-model="formData.summary"
            placeholder="请输入总结"
          />
        </el-form-item>
        <el-form-item label="计划" prop="plan">
          <el-input
            :disabled="reviewFlag"
            type="textarea"
            :rows="6"
            v-model="formData.plan"
            placeholder="请输入计划"
          />
        </el-form-item>
      </div>

      <!-- 图片与附件 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon><PictureFilled /></el-icon>
          图片与附件
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片" prop="image">
              <UploadImg v-model="formData.image" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件" prop="attachment">
              <UploadFile v-model="formData.attachment" :file-size="5120" :limit="5" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions" v-if="!reviewFlag">
        <el-button @click="submitForm" type="primary" :disabled="formLoading" size="large">
          保存
        </el-button>
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { useTagsViewStore } from '@/store/modules/tagsView'
import { WorkReportApi, WorkReportVO } from '@/api/crm/workReport'

/** 工作报告 表单 */
defineOptions({ name: 'WorkReportForm' })

const props = defineProps({
  reviewFlag: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number
  },
  tasks: {
    type: Array
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { push, currentRoute, back } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const { query } = useRoute() // 查询参数
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('create') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  type: undefined,
  reportTime: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  summary: undefined,
  plan: undefined,
  image: undefined,
  attachment: undefined
})
const formRules = reactive({
  type: [{ required: true, message: '类型 日报、周报、月报不能为空', trigger: 'change' }],
  reportTime: [{ required: true, message: '报告时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WorkReportApi.getWorkReport(id)
    } finally {
      formLoading.value = false
    }
  }
}

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WorkReportVO
    if (formType.value === 'create') {
      await WorkReportApi.createWorkReport(data)
      message.success(t('common.createSuccess'))
    } else {
      await WorkReportApi.updateWorkReport(data)
      message.success(t('common.updateSuccess'))
    }
    // 发送操作成功的事件
  } finally {
    formLoading.value = false
    handleClose()
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    type: undefined,
    reportTime: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
    summary: undefined,
    plan: undefined,
    image: undefined,
    attachment: undefined
  }
  formRef.value?.resetFields()
}

onMounted(async () => {
  if (props.reviewFlag) {
    formData.value = await WorkReportApi.getWorkReport(props.id)
  }
})
</script>

<style lang="scss" scoped>
.workreport-container {
  background: #ffffff;
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.workreport-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .el-form-item__label {
    font-weight: 500;
    color: #374151;
  }
}

.form-section {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 24px;
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  gap: 8px;
  
  .el-icon {
    color: #3b82f6;
    font-size: 18px;
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  
  .el-button {
    min-width: 100px;
    border-radius: 6px;
    font-weight: 500;
  }
  
  .el-button--primary {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workreport-container {
    padding: 16px;
  }
  
  .form-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 14px;
    margin-bottom: 16px;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
