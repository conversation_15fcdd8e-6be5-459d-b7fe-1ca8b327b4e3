<!--
 * @Description: 工时延长详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-11 13:58:23
 * @LastEditTime: 2024-07-10 15:36:57
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="报告时间">
        {{ formatDate(detailData.reportTime, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        {{ detailData.type }}
      </el-descriptions-item>
      <el-descriptions-item label="总结">
        {{ detailData.summary }}
      </el-descriptions-item>
      <el-descriptions-item label="计划">
        {{ detailData.plan }}
      </el-descriptions-item>
      <el-descriptions-item label="图片">
        <FileListPreview :fileUrl="detailData.image" />
      </el-descriptions-item>
      <el-descriptions-item label="附件">
        <FileListPreview :fileUrl="detailData.attachment" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { WorkReportApi, WorkReportVO } from '@/api/crm/workReport'

import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'CrmWorkReportDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await WorkReportApi.getWorkReport(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
