<template>
  <div class="workreport-index-wrap">
    <div class="workreport-card">
      <!-- 搜索工作栏 -->
      <div class="search-card">
        <el-form
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
          class="search-form"
        >
          <el-form-item label="所属部门">
            <el-tree-select
              v-model="deptId"
              :data="deptList"
              default-expand-all
              :props="defaultProps"
              check-strictly
              node-key="id"
              placeholder="请选择归属部门"
              @change="changeDept"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="申请人" prop="userId">
            <el-select
              filterable
              v-model="queryParams.userId"
              placeholder="请先选择申请人"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" prop="result">
            <el-select v-model="queryParams.result" clearable placeholder="请选择状态" class="!w-240px">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="报告时间" prop="reportTime">
            <el-date-picker
              clearable
              type="daterange"
              v-model="queryParams.reportTime"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="!w-240px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="queryParams.type" placeholder="请选择类型" class="!w-240px">
              <el-option label="月报" value="月报" />
              <el-option label="周报" value="周报" />
              <el-option label="日报" value="日报" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button type="primary" plain @click="handleCreate">
              <Icon icon="ep:plus" class="mr-5px" /> 发起工作报告
            </el-button>
            <el-button type="success" plain @click="handleExport" :loading="exportLoading">
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 列表 -->
      <div class="table-card">
        <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" class="custom-table">
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column
            label="报告时间"
            align="center"
            prop="reportTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="审核状态" align="center" prop="result">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
            </template>
          </el-table-column>
          <el-table-column label="申请人" align="center" prop="userId">
            <template #default="scope">
              <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总结" align="center" prop="summary" />
          <el-table-column label="计划" align="center" prop="plan" />
          <el-table-column label="图片" align="center" prop="image">
            <template #default="scope">
              <el-popover placement="right" :width="400" trigger="hover">
                <img :src="scope.row.image" width="375" height="375" />
                <template #reference>
                  <img :src="scope.row.image" style="max-height: 60px; max-width: 60px" />
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="附件" align="center" prop="attachment">
            <template #default="scope">
              <FileListPreview :fileUrl="scope.row.attachment" />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                link
                type="warning"
                @click="reissue(scope.row)"
                v-if="scope.row.type !== '日报' && (scope.row.result === 3 || scope.row.result === 4)"
              >
                重新发起
              </el-button>
              <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
              <el-button
                link
                type="primary"
                @click="handleProcessDetail(scope.row)"
                v-if="scope.row.type !== '日报'"
              >
                进度
              </el-button>
              <el-button
                link
                type="danger"
                @click="cancelLeave(scope.row)"
                v-if="scope.row.result === 1"
              >
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          class="custom-pagination"
        />
      </div>
    </div>
    <!-- 表单弹窗：添加/修改 -->
    <WorkReportForm ref="formRef" @success="getList" />
  </div>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { WorkReportApi, WorkReportVO } from '@/api/crm/workReport'
import WorkReportForm from './workReportForm.vue'
import * as UserApi from '@/api/system/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { UserVO } from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

const deptList = ref<Tree[]>([]) // 树形结构

const route = useRoute()

/** 工作报告 列表 */
defineOptions({ name: 'WorkReport' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const userList = ref<UserVO[]>([]) // 用户列表
const deptId = ref<any>(124)
const loading = ref(true) // 列表的加载中
const list = ref<WorkReportVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  type: undefined,
  reportTime: [],
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WorkReportApi.getWorkReportPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  deptId.value = undefined
  queryParams.pageNo = 1
  getList()
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'CrmWorkReportDetail',
    query: {
      id: row.id
    }
  })
}

const reissue = (row) => {
  router.push({
    name: 'CrmWorkReportCreate',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const changeDept = async (value) => {
  userList.value = await UserApi.getSimpleUserList({ deptId: value })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WorkReportApi.deleteWorkReport(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorkReportApi.exportWorkReport(queryParams)
    download.excel(data, '工作报告.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleCreate = () => {
  router.push({ name: 'CrmWorkReportCreate' })
}

/** 初始化 **/
onMounted(async () => {
  getList()
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  changeDept(deptId.value)
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style lang="scss" scoped>
.workreport-index-wrap {
  padding: 5px;
  background: #f7f8fa;
  min-height: 100vh;
}
.workreport-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.10), 0 1.5px 8px rgba(0,0,0,0.04);
  padding: 18px 18px 10px 18px;
  max-width: 100vw;
  margin: 0;
}
.search-card {
  background: #f9fafb;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.04);
  padding: 18px 18px 2px 18px;
  margin-bottom: 12px;
}
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 12px 24px;
}
.table-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(79, 70, 229, 0.06);
  padding: 10px 0 0 0;
}
.custom-table {
  border-radius: 10px;
  overflow: hidden;
  font-size: 15px;
  :deep(.el-table__header th) {
    background: #f4f6fa;
    font-weight: 800;
    color: #3b3b4f;
    font-size: 16px;
    border-bottom: 2px solid #e0e7ef;
    border-top: none;
    border-left: none;
    border-right: none;
    letter-spacing: 0.5px;
    height: 48px;
    text-align: center;
  }
  :deep(.el-table__body tr) {
    background: #fff;
    border-bottom: 1.5px solid #f1f1f1;
    transition: background 0.2s;
    &:hover {
      background: #f5f7fa;
    }
  }
  :deep(.el-table__cell) {
    padding: 10px 6px;
    border-bottom: none;
    font-size: 15px;
    color: #23233a;
    background: none;
    text-align: center;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
  }
  :deep(.el-table__footer) {
    background: #f4f6fa;
    font-weight: 700;
    color: #3b3b4f;
    border-top: 2px solid #e0e7ef;
  }
  :deep(.el-table__empty-block) {
    background: #fafbfc;
  }
}
.custom-pagination {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
@media (max-width: 900px) {
  .workreport-card {
    padding: 8px 2vw 8px 2vw;
    max-width: 100vw;
  }
  .search-card {
    padding: 8px 2vw 2px 2vw;
  }
}
@media (max-width: 600px) {
  .workreport-index-wrap {
    padding: 2px;
  }
  .workreport-card {
    padding: 2px 0 2px 0;
    margin: 0;
  }
  .search-card {
    padding: 2px 0 2px 0;
  }
  .table-card {
    padding: 2px 0 0 0;
  }
  .custom-table {
    font-size: 13px;
    :deep(.el-table__header th) {
      font-size: 14px;
    }
  }
}
</style>
