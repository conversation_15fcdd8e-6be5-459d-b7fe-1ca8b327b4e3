<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="类型 日报、周报、月报" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型 日报、周报、月报">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="报告时间" prop="reportTime">
        <el-date-picker
          v-model="formData.reportTime"
          type="date"
          value-format="x"
          placeholder="选择报告时间"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-input v-model="formData.result" placeholder="请输入审核状态" />
      </el-form-item>
      <el-form-item label="申请人的用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入申请人的用户编号" />
      </el-form-item>
      <el-form-item label="流程实例的编号" prop="processInstanceId">
        <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号" />
      </el-form-item>
      <el-form-item label="总结" prop="summary">
        <el-input v-model="formData.summary" placeholder="请输入总结" />
      </el-form-item>
      <el-form-item label="计划" prop="plan">
        <el-input v-model="formData.plan" placeholder="请输入计划" />
      </el-form-item>
      <el-form-item label="图片路径" prop="image">
        <UploadImg v-model="formData.image" />
      </el-form-item>
      <el-form-item label="附件路径" prop="attachment">
        <el-input v-model="formData.attachment" placeholder="请输入附件路径" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { WorkReportApi, WorkReportVO } from '@/api/crm/workReport'

/** 工作报告 表单 */
defineOptions({ name: 'WorkReportForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  type: undefined,
  reportTime: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  summary: undefined,
  plan: undefined,
  image: undefined,
  attachment: undefined
})
const formRules = reactive({
  type: [{ required: true, message: '类型 日报、周报、月报不能为空', trigger: 'change' }],
  reportTime: [{ required: true, message: '报告时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WorkReportApi.getWorkReport(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WorkReportVO
    if (formType.value === 'create') {
      await WorkReportApi.createWorkReport(data)
      message.success(t('common.createSuccess'))
    } else {
      await WorkReportApi.updateWorkReport(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    type: undefined,
    reportTime: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
    summary: undefined,
    plan: undefined,
    image: undefined,
    attachment: undefined
  }
  formRef.value?.resetFields()
}
</script>
