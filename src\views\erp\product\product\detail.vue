<!--
 * @Description: 产品详情页面
 * @Author: AI Assistant
 * @Date: 2024-12-20
-->
<template>
  <div class="product-detail-container">
    <div class="page-header">
      <el-button v-if="showBack" type="primary" class="back-button" @click="goBack">
        <el-icon><Back /></el-icon>
        返回
      </el-button>
      <h2 class="page-title">产品详情</h2>
      <p class="page-subtitle">Product Details</p>
    </div>

    <div class="content-wrapper" v-loading="detailLoading">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-goods"></i>
          <span>基本信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="产品名称">
              {{ detailData.name }}
            </el-descriptions-item>
            <el-descriptions-item label="产品条码">
              {{ detailData.barCode }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              {{ detailData.categoryName }}
            </el-descriptions-item>
            <el-descriptions-item label="单位">
              {{ detailData.unitName }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="detailData.status" />
            </el-descriptions-item>
            <el-descriptions-item label="规格型号">
              {{ detailData.standard }}
            </el-descriptions-item>
            <el-descriptions-item label="保质期天数">
              {{ detailData.expiryDay }} 天
            </el-descriptions-item>
            <el-descriptions-item label="重量"> {{ detailData.weight }} kg </el-descriptions-item>
            <el-descriptions-item label="采购价格">
              ¥{{ detailData.purchasePrice }}
            </el-descriptions-item>
            <el-descriptions-item label="销售价格">
              ¥{{ detailData.salePrice }}
            </el-descriptions-item>
            <el-descriptions-item label="最低价格">
              ¥{{ detailData.minPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="功能详细描述" :span="2">
              <div v-html="detailData.remark"></div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 供应商报价信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-price-tag"></i>
          <span>供应商报价</span>
          <span class="section-count">{{ supplierQuotes.length }} 条记录</span>
        </div>
        <div class="section-content">
          <div v-if="supplierQuotes.length > 0" class="table-container">
            <!-- 统计信息 -->
            <div class="stats-container">
              <div class="stat-item">
                <span class="stat-label">报价总数</span>
                <span class="stat-value">{{ supplierQuotes.length }}</span>
              </div>
              <div class="stat-item" v-if="quoteStats.avgPrice">
                <span class="stat-label">平均报价</span>
                <span class="stat-value">¥{{ quoteStats.avgPrice }}</span>
              </div>
              <div class="stat-item" v-if="quoteStats.minPrice">
                <span class="stat-label">最低报价</span>
                <span class="stat-value">¥{{ quoteStats.minPrice }}</span>
              </div>
              <div class="stat-item" v-if="quoteStats.maxPrice">
                <span class="stat-label">最高报价</span>
                <span class="stat-value">¥{{ quoteStats.maxPrice }}</span>
              </div>
            </div>

            <el-table
              :data="supplierQuotes"
              stripe
              border
              :show-overflow-tooltip="true"
              class="data-table"
              size="small"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column
                label="供应商名称"
                prop="supplierName"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column label="报价数量" prop="quantity" width="100" align="right" />
              <el-table-column
                label="报价单价"
                prop="unitPrice"
                width="100"
                align="right"
                :formatter="erpPriceTableColumnFormatter"
              />
              <el-table-column
                label="报价总价"
                prop="totalPrice"
                width="100"
                align="right"
                :formatter="erpPriceTableColumnFormatter"
              />
              <el-table-column label="货币类型" prop="currency" width="80" align="center">
                <template #default="scope">
                  {{ getDictLabel(DICT_TYPE.CURRENCY, scope.row.currency) }}
                </template>
              </el-table-column>
              <el-table-column
                label="报价日期"
                prop="quotationDate"
                width="130"
                align="center"
                :formatter="dateFormatter"
              />
              <el-table-column
                label="有效期至"
                prop="validUntil"
                width="130"
                align="center"
                :formatter="dateFormatter"
              />
              <el-table-column label="附件" prop="attachment" width="80" align="center">
                <template #default="scope">
                  <FileListPreview :fileUrl="scope.row.attachment" />
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="no-data-container">
            <el-empty description="暂无供应商报价记录" :image-size="100" />
          </div>
        </div>
      </div>

      <!-- 销售订单信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-shopping-cart-full"></i>
          <span>销售订单</span>
          <span class="section-count">{{ saleOrders.length }} 条记录</span>
        </div>
        <div class="section-content">
          <div v-if="saleOrders.length > 0" class="table-container">
            <!-- 统计信息 -->
            <div class="stats-container">
              <div class="stat-item">
                <span class="stat-label">订单总数</span>
                <span class="stat-value">{{ saleOrders.length }}</span>
              </div>
              <div class="stat-item" v-if="orderStats.totalQuantity">
                <span class="stat-label">总销售量</span>
                <span class="stat-value">{{ orderStats.totalQuantity }}</span>
              </div>
              <div class="stat-item" v-if="orderStats.totalAmount">
                <span class="stat-label">总销售额</span>
                <span class="stat-value">¥{{ orderStats.totalAmount }}</span>
              </div>
              <div class="stat-item" v-if="orderStats.avgPrice">
                <span class="stat-label">平均单价</span>
                <span class="stat-value">¥{{ orderStats.avgPrice }}</span>
              </div>
            </div>

            <el-table
              :data="saleOrders"
              stripe
              border
              :show-overflow-tooltip="true"
              class="data-table"
              size="small"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column label="订单编号" prop="no" min-width="150" show-overflow-tooltip />
              <el-table-column
                label="客户名称"
                prop="customerName"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                label="产品单价"
                prop="productPrice"
                width="100"
                align="right"
                :formatter="erpPriceTableColumnFormatter"
              />
              <el-table-column label="数量" prop="count" width="80" align="right" />
              <el-table-column label="税率" prop="taxPercent" width="80" align="right">
                <template #default="scope">
                  {{ scope.row.taxPercent ? scope.row.taxPercent + '%' : '-' }}
                </template>
              </el-table-column>
              <el-table-column
                label="含税金额"
                prop="taxPrice"
                width="100"
                align="right"
                :formatter="erpPriceTableColumnFormatter"
              />
              <el-table-column label="出库数量" prop="outCount" width="80" align="right" />
              <el-table-column label="退货数量" prop="returnCount" width="80" align="right" />
              <el-table-column label="订单时间" prop="orderTime" width="100" align="center">
                <template #default="scope">
                  {{ formatDate(scope.row.orderTime, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
            </el-table>
          </div>
          <div v-else class="no-data-container">
            <el-empty description="暂无销售订单记录" :image-size="100" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate, dateFormatter } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ProductApi, ProductVO, SaleOrderVO } from '@/api/erp/product/product'
import { SupplierQuoteApi, SupplierQuoteVO } from '@/api/erp/purchase/supplierquote'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { erpPriceTableColumnFormatter } from '@/utils'

defineOptions({ name: 'ErpProductDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})

const detailLoading = ref(false) // 表单的加载中
const detailData = ref<ProductVO>({} as ProductVO) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

// 供应商报价相关数据
const supplierQuotes = ref<SupplierQuoteVO[]>([]) // 供应商报价列表
const saleOrders = ref<SaleOrderVO[]>([]) // 销售订单列表

// 供应商报价统计信息
const quoteStats = computed(() => {
  if (supplierQuotes.value.length === 0) {
    return {}
  }

  const validPrices = supplierQuotes.value
    .map((item) => parseFloat(String(item.unitPrice)))
    .filter((price) => !isNaN(price) && price > 0)

  if (validPrices.length === 0) {
    return {}
  }

  const avgPrice = (
    validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length
  ).toFixed(2)
  const minPrice = Math.min(...validPrices).toFixed(2)
  const maxPrice = Math.max(...validPrices).toFixed(2)

  return {
    avgPrice,
    minPrice,
    maxPrice
  }
})

// 销售订单统计信息
const orderStats = computed(() => {
  if (saleOrders.value.length === 0) {
    return {}
  }

  const totalQuantity = saleOrders.value
    .reduce((sum, item) => sum + (parseFloat(String(item.count)) || 0), 0)
    .toFixed(2)

  const totalAmount = saleOrders.value
    .reduce((sum, item) => {
      const price = parseFloat(String(item.productPrice)) || 0
      const count = parseFloat(String(item.count)) || 0
      return sum + price * count
    }, 0)
    .toFixed(2)

  const validPrices = saleOrders.value
    .map((item) => parseFloat(String(item.productPrice)))
    .filter((price) => !isNaN(price) && price > 0)

  const avgPrice =
    validPrices.length > 0
      ? (validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length).toFixed(2)
      : null

  return {
    totalQuantity,
    totalAmount,
    avgPrice
  }
})

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    // 获取产品详情
    detailData.value = await ProductApi.getProduct(props.id || queryId)

    // 获取供应商报价列表
    const quotesData = await SupplierQuoteApi.getSupplierQuotePage({
      pageNo: 1,
      pageSize: 1000,
      productId: props.id || queryId
    })
    supplierQuotes.value = quotesData.list || []

    // 获取销售订单列表
    const ordersData = await ProductApi.getSaleOrdersByItem(props.id || queryId)
    saleOrders.value = ordersData || []
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/erp/product/product')
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
.product-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  position: relative;

  .back-button {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: #ffffff;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-50%) translateX(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .el-icon {
      margin-right: 6px;
    }
  }

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;

  i {
    font-size: 20px;
    color: #3b82f6;
  }

  span {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;

    &.section-count {
      margin-left: auto;
      font-size: 14px;
      font-weight: 500;
      color: #64748b;
      background: #e2e8f0;
      padding: 4px 12px;
      border-radius: 12px;
    }
  }
}

.section-content {
  padding: 24px;
}

.elegant-descriptions {
  :deep(.el-descriptions__header) {
    margin-bottom: 0;
  }

  :deep(.el-descriptions__body) {
    .el-descriptions__table {
      border-radius: 8px;
      overflow: hidden;

      .el-descriptions__cell {
        padding: 16px 20px;
        font-size: 14px;
        border-color: #e2e8f0;

        &.el-descriptions__label {
          background: #f8fafc;
          font-weight: 600;
          color: #374151;
          width: 180px;
        }

        &.el-descriptions__content {
          background: #ffffff;
          color: #1f2937;
        }
      }
    }
  }
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .stats-container {
    display: flex;
    justify-content: space-around;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;

    .stat-item {
      text-align: center;

      .stat-label {
        display: block;
        font-size: 12px;
        color: #64748b;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .stat-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
  }

  .data-table {
    :deep(.el-table__header) {
      background: #f8fafc;

      th {
        background: #f8fafc !important;
        color: #374151;
        font-weight: 600;
        border-color: #e2e8f0;
      }
    }

    :deep(.el-table__body) {
      tr {
        &:hover {
          background: #f8fafc;
        }

        td {
          border-color: #e2e8f0;
        }
      }
    }

    :deep(.el-table__border) {
      border-color: #e2e8f0;
    }
  }
}

.no-data-container {
  padding: 40px 20px;
  text-align: center;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px dashed #cbd5e1;
}

// 响应式设计
@media (max-width: 768px) {
  .product-detail-container {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 20px;

    .back-button {
      position: static;
      transform: none;
      margin-bottom: 16px;

      &:hover {
        transform: none;
      }
    }

    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .content-wrapper {
    gap: 16px;
  }

  .section-header {
    padding: 16px 20px;

    span {
      font-size: 16px;

      &.section-count {
        font-size: 12px;
        padding: 3px 8px;
      }
    }
  }

  .section-content {
    padding: 20px;
  }

  .elegant-descriptions {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 12px 16px;

          &.el-descriptions__label {
            width: 120px;
          }
        }
      }
    }
  }

  .table-container {
    .stats-container {
      flex-wrap: wrap;
      gap: 8px;
      padding: 12px;

      .stat-item {
        flex: 1;
        min-width: calc(50% - 4px);

        .stat-label {
          font-size: 11px;
          margin-bottom: 2px;
        }

        .stat-value {
          font-size: 14px;
        }
      }
    }

    .data-table {
      font-size: 12px;

      :deep(.el-table__header) {
        th {
          padding: 8px 4px;
          font-size: 12px;
        }
      }

      :deep(.el-table__body) {
        tr {
          td {
            padding: 8px 4px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
