<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="采购类别" prop="purchaseType">
        <el-select v-model="formData.purchaseType" placeholder="请选择采购类别">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PROCUREMENT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-input v-model="formData.result" placeholder="请输入审核状态" />
      </el-form-item>
      <el-form-item label="申请人的用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入申请人的用户编号" />
      </el-form-item>
      <el-form-item label="流程实例的编号" prop="processInstanceId">
        <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="formData.projectName" placeholder="请输入项目名称/业务名称" />
      </el-form-item>
      <el-form-item label="交付地点" prop="deliveryLocation">
        <el-input v-model="formData.deliveryLocation" placeholder="请输入交付地点" />
      </el-form-item>
      <el-form-item label="收货人" prop="receiver">
        <el-input v-model="formData.receiver" placeholder="请输入收货人" />
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="采购申请详情" name="purchaseRequestDetail">
        <PurchaseRequestDetailForm ref="purchaseRequestDetailFormRef" :request-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PurchaseRequestApi, PurchaseRequestVO } from '@/api/erp/purchase/purchaserequest'
import PurchaseRequestDetailForm from './components/PurchaseRequestDetailForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 采购申请 表单 */
defineOptions({ name: 'PurchaseRequestForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  purchaseType: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  remark: undefined,
  projectName: undefined,
  deliveryLocation: undefined,
  receiver: undefined
})
const formRules = reactive({
  purchaseType: [
    { required: true, message: '采购类别（项目采购,业务采购,行政采购）不能为空', trigger: 'change' }
  ],
  projectName: [{ required: true, message: '项目名称/业务名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('purchaseRequestDetail')
const purchaseRequestDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PurchaseRequestApi.getPurchaseRequest(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await purchaseRequestDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'purchaseRequestDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseRequestVO
    // 拼接子表的数据
    data.purchaseRequestDetails = purchaseRequestDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await PurchaseRequestApi.createPurchaseRequest(data)
      message.success(t('common.createSuccess'))
    } else {
      await PurchaseRequestApi.updatePurchaseRequest(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    purchaseType: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
    remark: undefined,
    projectName: undefined,
    deliveryLocation: undefined,
    receiver: undefined
  }
  formRef.value?.resetFields()
}
</script>
