<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" style="z-index: 0">
      <el-table-column label="序号" type="index" width="100" />
      <!-- <el-table-column label="产品编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-input v-model="row.productId" placeholder="请输入产品编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="产品名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.productName`"
            :rules="formRules.productName"
            class="mb-0px!"
          >
            <el-select
              ref="productRef"
              v-model="row.productName"
              allow-create
              clearable
              filterable
              remote
              :remote-method="(query) => remoteProductMethod(query, row)"
              :loading="productLoading"
              placeholder="请选择或输入产品"
              @change="onChangeProduct($event, row)"
            >
              <el-option
                v-for="item in row.filteredProductList || []"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>

      <!-- <el-table-column label="单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitId`" :rules="formRules.unitId" class="mb-0px!">
            <el-input disabled v-model="row.unitId" placeholder="请输入单位" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input disabled v-model="row.unitPrice" placeholder="请输入单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="规格" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standard`" :rules="formRules.standard" class="mb-0px!">
            <el-input disabled v-model="row.standard" placeholder="请输入规格" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="申请数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input
              @input="changePrice(row)"
              v-model="row.quantity"
              placeholder="请输入申请数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="总价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.totalPrice`"
            :rules="formRules.totalPrice"
            class="mb-0px!"
          >
            <el-input disabled v-model="row.totalPrice" placeholder="请输入总价" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="需求时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.needTime`" :rules="formRules.needTime" class="mb-0px!">
            <el-date-picker
              v-model="row.needTime"
              type="date"
              value-format="x"
              placeholder="选择需求时间"
            />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <div style="white-space: pre-line">{{ row.remark }}</div>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60" v-if="!disabled">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>添加采购申请详情</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PurchaseRequestApi } from '@/api/erp/purchase/purchaserequest'
import { propTypes } from '@/utils/propTypes'
import { ProductApi, ProductVO } from '@/api/erp/product/product'

const props = defineProps({
  requestId: {
    type: Number,
    default: undefined
  }, // 采购申请编号（主表的关联字段）
  disabled: propTypes.bool.def(false)
})
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
  requestId: [{ required: true, message: '采购申请编号不能为空', trigger: 'blur' }],
  productId: [{ required: true, message: '产品编号不能为空', trigger: 'blur' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '申请数量不能为空', trigger: 'blur' }],
  unitId: [{ required: true, message: '单位不能为空', trigger: 'blur' }],
  needTime: [{ required: true, message: '需求时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const productList = ref<ProductVO[]>([]) // 产品列表
const productLoading = ref(false)

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.requestId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await PurchaseRequestApi.getPurchaseRequestDetailListByRequestId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    requestId: undefined,
    productId: undefined,
    productName: undefined,
    quantity: undefined,
    unitId: undefined,
    unitPrice: undefined,
    standard: undefined,
    totalPrice: undefined,
    needTime: undefined,
    remark: undefined,
    filteredProductList: productList.value // 新增时默认全量
  }
  row.requestId = props.requestId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

const changePrice = (row) => {
  // row.totalPrice = row.quantity * row.unitPrice
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  // 深拷贝并去除filteredProductList字段
  return formData.value.map((row) => {
    const { filteredProductList, ...rest } = row
    return rest
  })
}

/** 处理产品变更 */
const productRef = ref()
const onChangeProduct = (productName, row) => {
  const product = productList.value.find((item) => item.name === productName)
  if (product) {
    // row.unitId = product.unitName
    // row.unitPrice = product.purchasePrice
    row.productId = product.id
    // row.standard = product.standard
    row.remark = product.remark
  }
}

const remoteProductMethod = (query, row) => {
  productLoading.value = true
  if (!query) {
    row.filteredProductList = productList.value
  } else {
    row.filteredProductList = productList.value.filter((item) => item.name.includes(query))
  }
  productLoading.value = false
}

/** 初始化 */
onMounted(async () => {
  productList.value = await ProductApi.getProductSimpleList()
  // 初始化所有已有行的 filteredProductList
  formData.value.forEach((row) => {
    row.filteredProductList = productList.value
  })
})

defineExpose({ validate, getData })
</script>
