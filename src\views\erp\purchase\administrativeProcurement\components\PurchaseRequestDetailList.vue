<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="采购申请详情编号" align="center" prop="id" />
      <el-table-column label="产品编号" align="center" prop="productId" /> -->
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="申请数量" align="center" prop="quantity" />
      <el-table-column label="单位" align="center" prop="unitId" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="总价" align="center" prop="totalPrice" />
      <el-table-column
        label="需求时间"
        align="center"
        prop="needTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" v-if="item.result === 2" @click="handleEnquiry(scope.row)">
            询价
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>
<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { PurchaseRequestApi } from '@/api/erp/purchase/purchaserequest'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const router = useRouter()
const props = defineProps<{
  requestId?: number // 采购申请编号（主表的关联字段）
  item?: any
}>()

const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await PurchaseRequestApi.getPurchaseRequestDetailListByRequestId(props.requestId)
  } finally {
    loading.value = false
  }
}

const handleEnquiry = (row) => {
  router.push({
    name: 'ErpPurchaseEnquiryCreate',
    query: {
      relPurchaseReqId: props.item.id,
      relPurchaseReqDetailId: row.id,
      itemId: row.productId,
      itemName: row.productName,
      itemQuantity: row.quantity
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
