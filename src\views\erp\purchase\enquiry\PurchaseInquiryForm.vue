<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="询价单编号" prop="inquiryCode">
        <el-input v-model="formData.inquiryCode" placeholder="请输入询价单编号" />
      </el-form-item>
      <el-form-item label="申请日期" prop="applyDate">
        <el-date-picker
          v-model="formData.applyDate"
          type="date"
          value-format="x"
          placeholder="选择申请日期"
        />
      </el-form-item>
      <el-form-item label="申请人ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入申请人ID" />
      </el-form-item>
      <el-form-item label="购买商品ID" prop="itemId">
        <el-input v-model="formData.itemId" placeholder="请输入购买商品ID" />
      </el-form-item>
      <el-form-item label="商品名称" prop="itemName">
        <el-input v-model="formData.itemName" placeholder="请输入商品名称" />
      </el-form-item>
      <el-form-item label="购买商品数量" prop="itemQuantity">
        <el-input v-model="formData.itemQuantity" placeholder="请输入购买商品数量" />
      </el-form-item>
      <el-form-item label="申请原因" prop="applyReason">
        <el-input v-model="formData.applyReason" placeholder="请输入申请原因" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="关联采购申请单ID" prop="relPurchaseReqId">
        <el-input v-model="formData.relPurchaseReqId" placeholder="请输入关联采购申请单ID" />
      </el-form-item>
      <el-form-item label="关联采购申请单明细ID" prop="relPurchaseReqDetailId">
        <el-input
          v-model="formData.relPurchaseReqDetailId"
          placeholder="请输入关联采购申请单明细ID"
        />
      </el-form-item>
      <el-form-item label="候选供应商报价ID列表（逗号拼接）" prop="candidateSupplierQuoteIds">
        <el-input
          v-model="formData.candidateSupplierQuoteIds"
          placeholder="请输入候选供应商报价ID列表（逗号拼接）"
        />
      </el-form-item>
      <el-form-item label="选择供应商报价ID" prop="selectedSupplierQuoteId">
        <el-input v-model="formData.selectedSupplierQuoteId" placeholder="请输入选择供应商报价ID" />
      </el-form-item>
      <el-form-item label="选择原因" prop="selectionReason">
        <el-input v-model="formData.selectionReason" placeholder="请输入选择原因" />
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-input v-model="formData.result" placeholder="请输入审核状态" />
      </el-form-item>
      <el-form-item label="流程实例的编号" prop="processInstanceId">
        <el-input v-model="formData.processInstanceId" placeholder="请输入流程实例的编号" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'

/** 采购询价 表单 */
defineOptions({ name: 'PurchaseInquiryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  inquiryCode: undefined,
  applyDate: undefined,
  userId: undefined,
  itemId: undefined,
  itemName: undefined,
  itemQuantity: undefined,
  applyReason: undefined,
  remark: undefined,
  relPurchaseReqId: undefined,
  relPurchaseReqDetailId: undefined,
  candidateSupplierQuoteIds: undefined,
  selectedSupplierQuoteId: undefined,
  selectionReason: undefined,
  result: undefined,
  processInstanceId: undefined
})
const formRules = reactive({
  applyDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
  itemId: [{ required: true, message: '购买商品ID不能为空', trigger: 'blur' }],
  itemQuantity: [{ required: true, message: '购买商品数量不能为空', trigger: 'blur' }],
  applyReason: [{ required: true, message: '申请原因不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PurchaseInquiryApi.getPurchaseInquiry(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseInquiryVO
    if (formType.value === 'create') {
      await PurchaseInquiryApi.createPurchaseInquiry(data)
      message.success(t('common.createSuccess'))
    } else {
      await PurchaseInquiryApi.updatePurchaseInquiry(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    inquiryCode: undefined,
    applyDate: undefined,
    userId: undefined,
    itemId: undefined,
    itemName: undefined,
    itemQuantity: undefined,
    applyReason: undefined,
    remark: undefined,
    relPurchaseReqId: undefined,
    relPurchaseReqDetailId: undefined,
    candidateSupplierQuoteIds: undefined,
    selectedSupplierQuoteId: undefined,
    selectionReason: undefined,
    result: undefined,
    processInstanceId: undefined
  }
  formRef.value?.resetFields()
}
</script>
