<!--
 * @Description: 申请新增页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2024-06-26 16:10:20
 * @LastEditTime: 2025-07-08 13:30:40
-->

<template>
  <div class="enquiry-create-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">询价申请</h2>
      <p class="page-subtitle">Purchase Inquiry Application</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="elegant-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-document"></i>
              <span>基本信息</span>
            </div>
          </div>
          <div class="card-content">
            <div class="form-grid">
              <div class="form-item">
                <el-form-item label="申请日期" prop="applyDate">
                  <el-date-picker
                    :disabled="reviewFlag"
                    v-model="formData.applyDate"
                    type="date"
                    value-format="x"
                    placeholder="选择申请日期"
                    class="elegant-input"
                  />
                </el-form-item>
              </div>
              <div class="form-item">
                <el-form-item label="产品" prop="itemName">
                  <el-input
                    disabled
                    v-model="formData.itemName"
                    placeholder="请输入产品"
                    class="elegant-input"
                  />
                </el-form-item>
              </div>
              <div class="form-item">
                <el-form-item label="购买产品数量" prop="itemQuantity">
                  <el-input
                    disabled
                    v-model="formData.itemQuantity"
                    placeholder="请输入购买产品数量"
                    class="elegant-input"
                  />
                </el-form-item>
              </div>
              <div class="form-item">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    :rows="4"
                    :disabled="reviewFlag"
                    v-model="formData.remark"
                    placeholder="请输入备注"
                    class="elegant-input"
                  />
                </el-form-item>
              </div>
              <div class="form-item" v-if="!reviewFlag">
                <el-form-item label="候选供应商报价列表" prop="candidateSupplierQuoteIds">
                  <el-button type="primary" @click="openDialog" class="elegant-button primary">
                    <i class="el-icon-plus"></i>
                    选择供应商
                  </el-button>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 供应商报价列表卡片 -->
        <div class="form-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-shopping-cart-2"></i>
              <span>供应商报价列表</span>
            </div>
          </div>
          <div class="card-content">
            <div class="table-container">
              <el-table
                ref="enquiryTableRef"
                :highlight-current-row="reviewFlag"
                :data="tableData"
                :stripe="true"
                row-key="id"
                :show-overflow-tooltip="false"
                @current-change="handleCurrentChange"
                @selection-change="handleSelectionChange"
                class="elegant-table"
              >
                <el-table-column type="selection" width="55" :selectable="() => true" />
                <el-table-column label="供应商名称" align="center" prop="supplierName" />
                <el-table-column label="产品名称" align="center" prop="productName" min-width="400">
                  <template #default="scope">
                    <div class="product-info-cell">
                      <span class="product-name">{{ scope.row.productName }}</span>
                      <div
                        v-if="
                          productDetailMap.has(
                            scope.row.productId || scope.row.id || scope.row.itemId
                          )
                        "
                        class="product-detail-preview"
                      >
                        <div class="preview-item">
                          <span class="label">规格：</span>
                          <span class="value">{{
                            productDetailMap.get(
                              scope.row.productId || scope.row.id || scope.row.itemId
                            )?.standard || '-'
                          }}</span>
                        </div>
                        <div
                          class="preview-item description"
                          v-if="
                            productDetailMap.get(
                              scope.row.productId || scope.row.id || scope.row.itemId
                            )?.remark
                          "
                        >
                          <span class="label">描述：</span>
                          <span class="value">{{
                            productDetailMap.get(
                              scope.row.productId || scope.row.id || scope.row.itemId
                            )?.remark
                          }}</span>
                        </div>
                      </div>
                      <div v-else-if="productDetailLoading" class="loading-preview">
                        <i class="el-icon-loading"></i>
                        加载中...
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="报价数量" align="center" prop="quantity" />
                <el-table-column label="报价单价" align="center" prop="unitPrice" />
                <el-table-column label="报价总价" align="center" prop="totalPrice" />
                <el-table-column label="供货周期" align="center" prop="leadTime" />
                <el-table-column
                  label="报价详细参数"
                  align="center"
                  prop="attachment"
                  width="120px"
                >
                  <template #default="scope">
                    <div class="attachment-cell">
                      <FileListPreview
                        v-if="scope.row.attachment"
                        :fileUrl="scope.row.attachment"
                      />
                      <span v-else class="no-attachment">无附件</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="报价日期"
                  align="center"
                  prop="quotationDate"
                  :formatter="dateFormatter2"
                  width="120px"
                />
                <el-table-column
                  label="有效期至"
                  align="center"
                  prop="validUntil"
                  :formatter="dateFormatter2"
                  width="120px"
                />
                <el-table-column label="操作" align="center" v-if="!reviewFlag" width="120px">
                  <template #default="scope">
                    <el-button
                      link
                      type="danger"
                      @click="handleDelete(scope.row.id)"
                      class="delete-button"
                    >
                      <i class="el-icon-delete"></i>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 产品参数对比卡片 -->
        <div class="form-card" v-if="selectedQuotes.length > 0">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-data-analysis"></i>
              <span>产品参数对比</span>
              <span class="compare-count">(已选择 {{ selectedQuotes.length }} 个产品)</span>
            </div>
            <div class="card-actions" v-if="!reviewFlag">
              <el-button
                type="primary"
                size="small"
                @click="clearSelection"
                class="elegant-button small"
              >
                <i class="el-icon-refresh"></i>
                清空选择
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="printTableData"
                class="elegant-button small"
              >
                <i class="el-icon-info"></i>
                调试数据
              </el-button>
            </div>
          </div>
          <div class="card-content">
            <div class="comparison-container">
              <!-- 对比表格 -->
              <div class="comparison-table-wrapper">
                <table class="comparison-table">
                  <thead>
                    <tr>
                      <th class="param-header">对比项目</th>
                      <th
                        v-for="(quote, index) in selectedQuotes"
                        :key="quote.id"
                        class="supplier-header"
                      >
                        <div class="supplier-info">
                          <div class="supplier-name">{{ quote.supplierName }}</div>
                          <div class="product-name">{{ quote.productName }}</div>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="param-label">供应商名称</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        {{ quote.supplierName }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">产品名称</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        {{ quote.productName }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">报价数量</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        {{ quote.quantity }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">报价单价</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value price">
                        ¥{{ quote.unitPrice }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">报价总价</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value price">
                        ¥{{ quote.totalPrice }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">报价日期</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        {{ formatDate(quote.quotationDate, 'YYYY-MM-DD') }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">有效期至</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        {{ formatDate(quote.validUntil, 'YYYY-MM-DD') }}
                      </td>
                    </tr>
                    <tr>
                      <td class="param-label">性价比评分</td>
                      <td v-for="quote in selectedQuotes" :key="quote.id" class="param-value">
                        <div class="score-container">
                          <el-rate
                            v-model="quote.score"
                            :max="5"
                            disabled
                            show-score
                            text-color="#ff9900"
                            class="score-rate"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 对比分析 -->
              <div class="analysis-section">
                <h4 class="analysis-title">对比分析</h4>
                <div class="analysis-content">
                  <div class="analysis-item">
                    <span class="analysis-label">最低价格：</span>
                    <span class="analysis-value highlight">
                      ¥{{ getMinPrice() }} ({{ getMinPriceSupplier() }})
                    </span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">最高价格：</span>
                    <span class="analysis-value">
                      ¥{{ getMaxPrice() }} ({{ getMaxPriceSupplier() }})
                    </span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">价格差异：</span>
                    <span class="analysis-value"> ¥{{ getPriceDifference() }} </span>
                  </div>
                  <div class="analysis-item">
                    <span class="analysis-label">推荐供应商：</span>
                    <span class="analysis-value recommend">
                      {{ getRecommendedSupplier() }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 选择供应商卡片 -->
        <div class="form-card" v-if="reviewFlag">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-check"></i>
              <span>选择供应商</span>
            </div>
          </div>
          <div class="card-content">
            <div class="form-grid">
              <div class="form-item">
                <el-form-item label="供应商名称" prop="selQuoteName">
                  <el-input
                    disabled
                    v-model="formData.selQuoteName"
                    placeholder="请从上方候选供应商报价列表中选择"
                    class="elegant-input"
                  />
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 采购说明卡片 -->
        <div class="form-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-edit-outline"></i>
              <span>采购说明</span>
            </div>
          </div>
          <div class="card-content">
            <el-form-item label="采购说明" prop="applyReason">
              <Editor
                :readonly="reviewFlag"
                placeholder="请输入采购说明"
                v-model="formData.applyReason"
                height="300px"
                class="elegant-editor"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar" v-if="!reviewFlag">
          <el-button
            @click="submitForm"
            type="primary"
            :disabled="formLoading"
            class="elegant-button primary"
          >
            <i class="el-icon-check"></i>
            确定提交
          </el-button>
          <el-button
            @click="handleClose"
            type="warning"
            :disabled="formLoading"
            class="elegant-button warning"
          >
            <i class="el-icon-close"></i>
            关闭
          </el-button>
        </div>
      </el-form>
    </div>
  </div>

  <SupplierQuoteDialog ref="supplierQuoteDialogRef" @fetch-data="chooseSupplierQuoteDone" />
</template>

<script lang="ts" setup>
import { dateFormatter2, formatDate } from '@/utils/formatTime'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep, uniqBy } from 'lodash-es'
import SupplierQuoteDialog from './supplierQuoteDialog.vue'
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import FileListPreview from '@/components/FileListPreview/index.vue'

defineOptions({ name: 'PurchaseEnjuryCreate' })

const props = defineProps({
  reviewFlag: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number
  },
  tasks: {
    type: Array
  }
})

let tableData = ref<any>([])
const supplierQuoteDialogRef = ref()
const enquiryTableRef = ref()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const selectedQuotes = ref<any>([]) // 选中的报价列表
const productDetailVisible = ref(false) // 产品详情弹窗显示状态
const productDetailLoading = ref(false) // 产品详情加载状态
const productDetail = ref<any>(null) // 产品详情数据
const selectedProductId = ref<number | null>(null) // 当前选中的产品ID
const productDetailMap = ref<Map<any, any>>(new Map()) // 存储所有产品详情的Map
const formData = ref<any>({
  id: undefined,
  inquiryCode: undefined,
  applyDate: undefined,
  userId: undefined,
  itemId: undefined,
  itemName: undefined,
  leadTime: undefined,
  itemQuantity: undefined,
  applyReason: undefined,
  remark: undefined,
  relPurchaseReqId: undefined,
  relPurchaseReqDetailId: undefined,
  candidateSupplierQuoteIds: undefined,
  selectedSupplierQuoteId: undefined,
  selectionReason: undefined,
  result: undefined,
  processInstanceId: undefined
})
const formRules = reactive({
  applyDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
  itemId: [{ required: true, message: '购买商品ID不能为空', trigger: 'blur' }],
  itemName: [{ required: true, message: '购买商品ID不能为空', trigger: 'blur' }],
  itemQuantity: [{ required: true, message: '购买商品数量不能为空', trigger: 'blur' }],
  applyReason: [{ required: true, message: '申请原因不能为空', trigger: 'blur' }],
  selQuoteName: [{ required: true, message: '供应商名称不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseInquiryVO as any

    if (props.reviewFlag) {
      await PurchaseInquiryApi.updatePurchaseInquiry(data)
    } else {
      delete data.id
      await PurchaseInquiryApi.createPurchaseInquiry(data)
      message.success(t('common.createSuccess'))
      // 发送操作成功的事件
      handleClose()
    }
  } finally {
    formLoading.value = false
  }
}

const handleCurrentChange = (val) => {
  if (props.reviewFlag) {
    formData.value.selectedSupplierQuoteId = val.id
    formData.value.selQuoteName = val.supplierName
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

const chooseSupplierQuoteDone = (data) => {
  tableData.value = uniqBy([...tableData.value, ...data], 'id')
  formData.value.candidateSupplierQuoteIds = data.map((item) => item.id).join()

  // 自动加载所有产品的详情
  loadAllProductDetails()
}

// 加载所有产品的详情
const loadAllProductDetails = async () => {
  if (tableData.value.length === 0) return

  productDetailLoading.value = true

  try {
    // 获取所有不重复的产品ID
    const productIds = [
      ...new Set(
        tableData.value.map((item) => item.productId || item.id || item.itemId).filter((id) => id)
      )
    ]

    console.log('需要加载的产品ID列表:', productIds)

    // 并行加载所有产品详情
    const productDetails = await Promise.all(
      productIds.map(async (productId) => {
        try {
          const result = await ProductApi.getProduct(productId)
          return { productId, data: result }
        } catch (error) {
          console.error(`获取产品 ${productId} 详情失败:`, error)
          return { productId, data: null, error: error.message }
        }
      })
    )

    // 将产品详情存储到Map中，方便查找
    productDetailMap.value = new Map(productDetails.map((item) => [item.productId, item.data]))

    console.log('所有产品详情加载完成:', productDetailMap.value)
  } catch (error) {
    console.error('加载产品详情失败:', error)
    message.error('加载产品详情失败')
  } finally {
    productDetailLoading.value = false
  }
}

const openDialog = () => {
  supplierQuoteDialogRef.value.openDialog(formData.value.itemId)
}

const handleDelete = (id) => {
  const array = formData.value.candidateSupplierQuoteIds.split(',')
  const filteredArray = array.filter((item) => item !== id)
  const index = tableData.value.findIndex((item) => item.id === id)
  if (index !== -1) {
    tableData.value.splice(index, 1)
    formData.value.candidateSupplierQuoteIds = filteredArray.join()
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedQuotes.value = selection.map((item) => ({
    ...item,
    score: Math.floor(Math.random() * 3) + 3 // 随机生成3-5分的评分
  }))
}

// 清空选择
const clearSelection = () => {
  selectedQuotes.value = []
  // 清空表格选择
  const tableRef = document.querySelector('.elegant-table')
  if (tableRef) {
    const checkboxes = tableRef.querySelectorAll('.el-checkbox__input')
    checkboxes.forEach((checkbox) => {
      if (checkbox.classList.contains('is-checked')) {
        checkbox.click()
      }
    })
  }
}

// 获取最低价格
const getMinPrice = () => {
  if (selectedQuotes.value.length === 0) return '0'
  const minPrice = Math.min(
    ...selectedQuotes.value.map((quote) => parseFloat(quote.unitPrice) || 0)
  )
  return minPrice.toFixed(2)
}

// 获取最高价格
const getMaxPrice = () => {
  if (selectedQuotes.value.length === 0) return '0'
  const maxPrice = Math.max(
    ...selectedQuotes.value.map((quote) => parseFloat(quote.unitPrice) || 0)
  )
  return maxPrice.toFixed(2)
}

// 获取最低价格供应商
const getMinPriceSupplier = () => {
  if (selectedQuotes.value.length === 0) return ''
  const minQuote = selectedQuotes.value.reduce((min, quote) =>
    parseFloat(quote.unitPrice) < parseFloat(min.unitPrice) ? quote : min
  )
  return minQuote.supplierName
}

// 获取最高价格供应商
const getMaxPriceSupplier = () => {
  if (selectedQuotes.value.length === 0) return ''
  const maxQuote = selectedQuotes.value.reduce((max, quote) =>
    parseFloat(quote.unitPrice) > parseFloat(max.unitPrice) ? quote : max
  )
  return maxQuote.supplierName
}

// 获取价格差异
const getPriceDifference = () => {
  if (selectedQuotes.value.length === 0) return '0'
  const minPrice = Math.min(
    ...selectedQuotes.value.map((quote) => parseFloat(quote.unitPrice) || 0)
  )
  const maxPrice = Math.max(
    ...selectedQuotes.value.map((quote) => parseFloat(quote.unitPrice) || 0)
  )
  return (maxPrice - minPrice).toFixed(2)
}

// 获取推荐供应商
const getRecommendedSupplier = () => {
  if (selectedQuotes.value.length === 0) return '暂无推荐'

  // 根据价格和评分综合推荐
  const recommendations = selectedQuotes.value.map((quote) => ({
    ...quote,
    recommendationScore:
      parseFloat(quote.score) * 0.6 + (1 / parseFloat(quote.unitPrice)) * 1000 * 0.4
  }))

  const bestQuote = recommendations.reduce((best, quote) =>
    quote.recommendationScore > best.recommendationScore ? quote : best
  )

  return bestQuote.supplierName
}

const printTableData = () => {
  console.log('表格数据:', tableData.value)
  message.success('已打印表格数据到控制台')
}

// 监听 tableData 变化，当 reviewFlag 为 true 时自动全选
watch(
  () => tableData.value,
  (val) => {
    if (props.reviewFlag && enquiryTableRef.value && val.length > 0) {
      // 延迟执行，确保表格已渲染
      nextTick(() => {
        enquiryTableRef.value.clearSelection()
        val.forEach((row) => {
          enquiryTableRef.value.toggleRowSelection(row, true)
        })
      })
    }
  },
  { immediate: true }
)

onMounted(async () => {
  if (props.reviewFlag) {
    formData.value = await PurchaseInquiryApi.getPurchaseInquiry(props.id as any)
    tableData.value = formData.value.supplierQuoteList
    loadAllProductDetails()

    // 确保在数据加载后全选
    nextTick(() => {
      if (enquiryTableRef.value && tableData.value.length > 0) {
        enquiryTableRef.value.clearSelection()
        tableData.value.forEach((row) => {
          enquiryTableRef.value.toggleRowSelection(row, true)
        })
      }
    })
  } else {
    if (route.query.id) {
      formData.value = await PurchaseInquiryApi.getPurchaseInquiry(route.query.id as any)
    } else {
      if (route.query.itemId) {
        formData.value.itemId = route.query.itemId
        formData.value.itemName = route.query.itemName
        formData.value.leadTime = route.query.leadTime
        formData.value.itemQuantity = route.query.itemQuantity
        formData.value.relPurchaseReqId = route.query.relPurchaseReqId
        formData.value.relPurchaseReqDetailId = route.query.relPurchaseReqDetailId
      }
    }
  }
})

defineExpose({ submitForm, formRef })
</script>

<style lang="scss" scoped>
.enquiry-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.elegant-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
  }
}

.form-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;

  .card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;

    i {
      font-size: 20px;
      color: #3b82f6;
    }
  }
}

.card-content {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.form-item {
  .el-form-item {
    margin-bottom: 0;
  }
}

.elegant-input {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .el-input__inner {
    font-size: 14px;
    color: #374151;
  }
}

.elegant-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #ffffff;

    &:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }
  }
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.elegant-table {
  .el-table__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    th {
      background: transparent;
      color: #374151;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 1px solid #e2e8f0;
    }
  }

  .el-table__body {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8fafc;
      }
    }

    td {
      border-bottom: 1px solid #f1f5f9;
      color: #374151;
      font-size: 14px;
      word-wrap: break-word;
      white-space: normal;
      vertical-align: top;
    }
  }
}

.delete-button {
  color: #ef4444;
  font-weight: 500;

  &:hover {
    color: #dc2626;
    background-color: #fef2f2;
  }
}

.action-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.elegant-editor {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  overflow: hidden;
}

// 产品参数对比样式
.compare-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
  margin-left: 8px;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.elegant-button.small {
  padding: 8px 16px;
  font-size: 12px;
}

.comparison-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comparison-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;

  th,
  td {
    padding: 12px 16px;
    text-align: center;
    border: 1px solid #e2e8f0;
    font-size: 14px;
  }

  .param-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 600;
    color: #1e293b;
    min-width: 120px;
  }

  .supplier-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    font-weight: 600;
    min-width: 200px;

    .supplier-info {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .supplier-name {
        font-size: 14px;
        font-weight: 600;
      }

      .product-name {
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }

  .param-label {
    background: #f8fafc;
    font-weight: 500;
    color: #374151;
    text-align: left;
  }

  .param-value {
    color: #1e293b;
    font-weight: 500;

    &.price {
      color: #059669;
      font-weight: 600;
    }
  }

  .score-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .score-rate {
      .el-rate__icon {
        font-size: 16px;
      }
    }
  }
}

.analysis-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #bae6fd;

  .analysis-title {
    font-size: 16px;
    font-weight: 600;
    color: #0c4a6e;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 2px;
    }
  }

  .analysis-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .analysis-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .analysis-label {
      font-size: 13px;
      color: #64748b;
      font-weight: 500;
    }

    .analysis-value {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;

      &.highlight {
        color: #059669;
      }

      &.recommend {
        color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
        display: inline-block;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enquiry-create-container {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .content-wrapper {
    gap: 16px;
  }

  .card-header {
    padding: 16px 20px;

    .card-title {
      font-size: 16px;
    }
  }

  .card-content {
    padding: 20px;
  }

  .form-grid {
    gap: 16px;
  }

  .action-bar {
    flex-direction: column;
    padding: 20px;

    .elegant-button {
      width: 100%;
      justify-content: center;
    }
  }

  .comparison-table {
    th,
    td {
      padding: 8px 12px;
      font-size: 12px;
    }

    .supplier-header {
      min-width: 150px;

      .supplier-info {
        .supplier-name {
          font-size: 12px;
        }

        .product-name {
          font-size: 10px;
        }
      }
    }
  }

  .analysis-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

// 产品详情弹窗样式
.product-detail-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }
  }

  .el-dialog__body {
    padding: 0;
  }
}

.product-detail-content {
  padding: 24px;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 2px;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    min-width: 80px;
  }

  .value {
    font-size: 14px;
    color: #1e293b;
    font-weight: 500;

    &.price {
      color: #059669;
      font-weight: 600;
    }
  }
}

.description-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
}

.product-link {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;

  &:hover {
    color: #3b82f6;
    text-decoration: underline;
  }

  &.active {
    color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
  }

  i {
    font-size: 14px;
  }
}

.debug-info {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;

  p {
    color: #92400e;
    font-weight: 500;
    margin: 0 0 12px 0;
  }

  pre {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 12px;
    font-size: 12px;
    color: #374151;
    overflow-x: auto;
    margin: 0;
  }
}

// 内嵌产品详情样式
.product-detail-section {
  margin-top: 24px;
  border-top: 2px solid #e2e8f0;
  padding-top: 20px;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .detail-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #3b82f6;
        font-size: 20px;
      }
    }

    .close-detail-btn {
      color: #64748b;

      &:hover {
        color: #ef4444;
      }
    }
  }

  .product-detail-content {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
  }
}

.product-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: left;
  white-space: normal;
  word-wrap: break-word;
  min-height: 40px;
}

.product-name {
  font-weight: 500;
  color: #1e293b;
  word-break: break-word;
  line-height: 1.4;
  white-space: normal;
}

.product-detail-preview {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  white-space: normal;
  word-wrap: break-word;
  min-width: 0;
}

.preview-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
  line-height: 1.4;

  .label {
    min-width: 40px;
    color: #64748b;
    font-weight: 500;
    flex-shrink: 0;
  }

  .value {
    color: #1e293b;
    font-weight: 400;
    flex: 1;
    word-break: break-word;
    white-space: normal;
  }
}

.preview-item.description {
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;

  .label {
    min-width: auto;
  }

  .value {
    line-height: 1.4;
    word-break: break-word;
    max-width: 100%;
    white-space: normal;
  }
}

.loading-preview {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

// 附件列样式
:deep(.el-table) {
  .attachment-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 40px;

    .file-preview {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      background: #f8fafc;
      border-radius: 6px;
      border: 1px solid #e2e8f0;
      font-size: 12px;
      color: #64748b;
      transition: all 0.2s ease;

      &:hover {
        background: #e2e8f0;
        color: #374151;
      }

      i {
        font-size: 14px;
        color: #3b82f6;
      }
    }

    .no-attachment {
      color: #9ca3af;
      font-size: 12px;
      font-style: italic;
    }
  }
}
</style>
