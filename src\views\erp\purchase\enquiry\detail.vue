<!--
 * @Description: 详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-20 16:49:24
 * @LastEditTime: 2024-07-10 15:37:22
-->
<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="询价单编号">
        {{ detailData.inquiryCode }}
      </el-descriptions-item>
      <el-descriptions-item label="申请日期">
        {{ formatDate(detailData.applyDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="申请人">
        {{ userList.find((item) => item.id === detailData.userId)?.nickname }}
      </el-descriptions-item>
      <el-descriptions-item label="商品ID">
        {{ detailData.itemId }}
      </el-descriptions-item>
      <el-descriptions-item label="商品名称">
        {{ detailData.itemName }}
      </el-descriptions-item>
      <el-descriptions-item label="购买商品数量">
        {{ detailData.itemQuantity }}
      </el-descriptions-item>
      <el-descriptions-item label="申请原因">
        <div v-html="detailData.applyReason"></div>
      </el-descriptions-item>
      <el-descriptions-item label="交付日期">
        {{ formatDate(detailData.deliveryDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="选择的供应商名称">
        {{ detailData.selQuoteName }}
      </el-descriptions-item>
      <el-descriptions-item label="选择原因">
        {{ detailData.selectionReason }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
  <ContentWrap>
    <h1>候选供应商报价列表(绿色为已选择的供应商)</h1>
    <el-table
      :row-class-name="tableRowClassName"
      style="margin-bottom: 20px"
      :data="detailData.supplierQuoteList"
      row-key="id"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="供应商编号" align="center" prop="supplierId" />
      <el-table-column label="供应商名称" align="center" prop="supplierName" />
      <!-- <el-table-column label="产品编号" align="center" prop="productId" /> -->
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="报价数量" align="center" prop="quantity" />
      <el-table-column label="报价单价" align="center" prop="unitPrice" />
      <el-table-column label="报价总价" align="center" prop="totalPrice" />
      <el-table-column label="货币类型" align="center" prop="currency">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.CURRENCY, scope.row.currency) }}
        </template>
      </el-table-column>
      <el-table-column
        label="报价日期"
        align="center"
        prop="quotationDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效期至"
        align="center"
        prop="validUntil"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="购买期限" align="center" prop="duration" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'
import { dateFormatter } from '@/utils/formatTime'

import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'ErpPurchaseEnquiryDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await PurchaseInquiryApi.getPurchaseInquiry(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const tableRowClassName = ({ row, rowIndex }) => {
  if (row.id == detailData.value.selectedSupplierQuoteId) {
    return 'success-row'
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(async () => {
  getInfo()
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style>
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
