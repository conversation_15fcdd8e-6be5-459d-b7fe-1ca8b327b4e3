<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="询价单编号" prop="inquiryCode">
        <el-input
          v-model="queryParams.inquiryCode"
          placeholder="请输入询价单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="userId">
        <el-select
          v-model="queryParams.userId"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button type="primary" plain @click="handleCreate">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="询价单编号" align="center" prop="inquiryCode" />
      <el-table-column
        label="申请日期"
        align="center"
        prop="applyDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="申请人" align="center" prop="userId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="商品ID" align="center" prop="itemId" /> -->
      <el-table-column label="商品名称" align="center" prop="itemName" />
      <el-table-column label="购买商品数量" align="center" prop="itemQuantity" />
      <!-- <el-table-column label="申请原因" align="center" prop="applyReason" width="400px">
        <template #default="scope">
          <div v-html="scope.row.applyReason"></div>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="交付日期"
        align="center"
        prop="deliveryDate"
        :formatter="dateFormatter2"
        width="180px"
      /> -->
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <!-- <el-table-column label="关联采购申请单ID" align="center" prop="relPurchaseReqId" />
      <el-table-column label="关联采购申请单明细ID" align="center" prop="relPurchaseReqDetailId" />
      <el-table-column
        label="候选供应商报价ID列表"
        align="center"
        prop="candidateSupplierQuoteIds"
      />
      <el-table-column label="选择供应商报价ID" align="center" prop="selectedSupplierQuoteId" /> -->
      <!-- <el-table-column label="选择原因" align="center" prop="selectionReason" /> -->
      <el-table-column label="审核状态" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="流程实例的编号" align="center" prop="processInstanceId" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200px">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            v-if="scope.row.result === 2"
            link
            type="primary"
            @click="handleCreateOrder(scope.row)"
          >
            去采购
          </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseInquiryForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'
import PurchaseInquiryForm from './PurchaseInquiryForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'

/** 采购询价 列表 */
defineOptions({ name: 'PurchaseInquiry' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const router = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<PurchaseInquiryVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  inquiryCode: undefined,
  applyDate: [],
  userId: undefined,
  itemId: undefined,
  itemName: undefined,
  itemQuantity: undefined,
  applyReason: undefined,
  remark: undefined,
  relPurchaseReqId: undefined,
  relPurchaseReqDetailId: undefined,
  candidateSupplierQuoteIds: undefined,
  selectedSupplierQuoteId: undefined,
  selectionReason: undefined,
  result: undefined,
  processInstanceId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseInquiryApi.getPurchaseInquiryPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const handleCreate = () => {
  router.push({
    name: 'ErpPurchaseEnquiryCreate'
  })
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const reissue = (row) => {
  router.push({
    name: 'ErpPurchaseEnquiryCreate',
    query: {
      id: row.id
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ErpPurchaseEnquiryDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId
    }
  })
}

const handleCreateOrder = (row) => {
  router.push({
    path: '/erp/purchase/order',
    query: {
      id: row.id
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseInquiryApi.deletePurchaseInquiry(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseInquiryApi.exportPurchaseInquiry(queryParams)
    download.excel(data, '采购询价.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
