<template>
  <el-dialog
    title="供应商报价选择"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
    center
  >
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      row-key="id"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" reserve-selection />
      <!-- <el-table-column label="供应商编号" align="center" prop="supplierId" /> -->
      <el-table-column label="供应商名称" align="center" prop="supplierName" min-width="200"/>
      <!-- <el-table-column label="产品编号" align="center" prop="productId" /> -->
      <el-table-column label="产品名称" align="center" prop="productName" min-width="200"/>
      <el-table-column label="报价数量" align="center" prop="quantity" />
      <el-table-column label="报价单价" align="center" prop="unitPrice" />
      <el-table-column label="报价总价" align="center" prop="totalPrice" />
      <el-table-column label="货币类型" align="center" prop="currency">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.CURRENCY, scope.row.currency) }}
        </template>
      </el-table-column>
      <el-table-column label="报价详情" align="center" prop="attachment" min-width="200">
        <template #default="scope">
          <div class="quote-detail-cell">
            <FileListPreview :fileUrl="scope.row.attachment" />
            <div v-if="scope.row.remark" class="quote-remark">
              <span class="remark-label">备注：</span>
              <span class="remark-content">{{ scope.row.remark }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="报价日期"
        align="center"
        prop="quotationDate"
        :formatter="dateFormatter2"
        width="120px"
      />
      <el-table-column
        label="有效期至"
        align="center"
        prop="validUntil"
        :formatter="dateFormatter2"
        width="120px"
      />
      <el-table-column label="购买期限" align="center" prop="duration" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter2"
        width="120px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="chooseQuote">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({ name: 'SupplierQuoteDialog' })

import { dateFormatter2 } from '@/utils/formatTime'
import { SupplierQuoteApi, SupplierQuoteVO } from '@/api/erp/purchase/supplierquote'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'

const emit = defineEmits(['fetch-data'])

let dialogVisible = ref(false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const queryFormRef = ref() // 搜索的表单

let selectedRows = ref([])
const loading = ref(true) // 列表的加载中
const list = ref<SupplierQuoteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  supplierName: undefined,
  productName: undefined,
  productId: undefined
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SupplierQuoteApi.getSupplierQuotePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const openDialog = async (id) => {
  dialogVisible.value = true
  queryParams.productId = id
  await getList()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const chooseQuote = () => {
  dialogVisible.value = false
  emit('fetch-data', selectedRows.value)
  message.success('选择成功')
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.quote-detail-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: left;
  padding: 8px;
  
  .quote-remark {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    font-size: 12px;
    line-height: 1.4;
    
    .remark-label {
      color: #64748b;
      font-weight: 500;
      flex-shrink: 0;
    }
    
    .remark-content {
      color: #1e293b;
      font-weight: 400;
      flex: 1;
      word-break: break-word;
      white-space: normal;
    }
  }
}

:deep(.el-table) {
  .el-table__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    
    th {
      background: transparent;
      color: #374151;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 1px solid #e2e8f0;
    }
  }
  
  .el-table__body {
    tr {
      transition: all 0.2s ease;
      
      &:hover {
        background-color: #f8fafc;
      }
    }
    
    td {
      border-bottom: 1px solid #f1f5f9;
      color: #374151;
      font-size: 14px;
      word-wrap: break-word;
      white-space: normal;
      vertical-align: top;
    }
  }
}

:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
  }
}

:deep(.el-form) {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
  
  .el-form-item {
    margin-bottom: 16px;
    
    .el-form-item__label {
      color: #374151;
      font-weight: 500;
    }
    
    .el-input {
      .el-input__wrapper {
        border-radius: 6px;
        border: 1px solid #d1d5db;
        
        &:hover {
          border-color: #3b82f6;
        }
        
        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
</style>
