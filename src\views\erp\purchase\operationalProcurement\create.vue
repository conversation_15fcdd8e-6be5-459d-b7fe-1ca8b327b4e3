<template>
  <div class="operational-procurement-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">业务采购申请</h2>
      <p class="page-subtitle">创建新的业务采购申请单</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 申请表单区域 -->
      <div class="content-section form-section">
        <div class="section-header">
          <div class="section-icon">
            <Icon icon="ep:edit" />
          </div>
          <h3 class="section-title">申请信息</h3>
        </div>
        <div class="section-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="formLoading"
            class="request-form"
          >
            <div class="form-row">
              <el-form-item label="交付地点" prop="deliveryLocation" class="form-item">
                <el-input
                  v-model="formData.deliveryLocation"
                  placeholder="请输入交付地点"
                  class="form-input"
                />
              </el-form-item>
              <el-form-item label="收货人" prop="receiver" class="form-item">
                <el-input
                  v-model="formData.receiver"
                  placeholder="请输入收货人"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="交付日期" prop="deliveryDate" class="form-item">
                <el-date-picker
                  v-model="formData.deliveryDate"
                  type="date"
                  value-format="x"
                  placeholder="选择交付日期"
                  class="form-date-picker"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark" class="form-item">
                <el-input v-model="formData.remark" placeholder="请输入备注" class="form-input" />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 详情表格区域 -->
      <div class="content-section detail-section">
        <div class="section-header">
          <div class="section-icon">
            <Icon icon="ep:list" />
          </div>
          <h3 class="section-title">采购详情</h3>
        </div>
        <div class="section-content">
          <el-tabs v-model="subTabsName" class="detail-tabs">
            <el-tab-pane label="采购申请详情" name="purchaseRequestDetail">
              <PurchaseRequestDetailForm
                ref="purchaseRequestDetailFormRef"
                :request-id="formData.id"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button
        @click="submitForm"
        type="primary"
        :disabled="formLoading"
        class="submit-btn"
        :icon="'ep:check'"
      >
        提交申请
      </el-button>
      <el-button
        @click="handleClose"
        type="default"
        :disabled="formLoading"
        class="cancel-btn"
        :icon="'ep:close'"
      >
        取消
      </el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PurchaseRequestApi, PurchaseRequestVO } from '@/api/erp/purchase/purchaserequest'
import PurchaseRequestDetailForm from './components/PurchaseRequestDetailForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'OperationalProcurementCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作

const { back, currentRoute } = useRouter()
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  purchaseType: 2,
  contractCode: undefined,
  contractName: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  remark: undefined,
  deliveryDate: undefined,
  projectName: undefined,
  deliveryLocation: undefined,
  receiver: undefined
})
const formRules = reactive({
  deliveryLocation: [{ required: true, message: '交付地点不能为空', trigger: 'blur' }],
  deliveryDate: [{ required: true, message: '交付日期不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('purchaseRequestDetail')
const purchaseRequestDetailFormRef = ref()

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseRequestVO as any
    data.purchaseRequestDetails =
      purchaseRequestDetailFormRef.value.getData().length > 0
        ? purchaseRequestDetailFormRef.value.getData().map((item) => {
            const { id, ...rest } = item
            return rest
          })
        : []

    delete data.id
    await PurchaseRequestApi.createPurchaseRequest(data)
    message.success(t('common.updateSuccess'))
    // 发送操作成功的事件
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  if (route.query.id) {
    formData.value = await PurchaseRequestApi.getPurchaseRequest(route.query.id as any)
  }
})
</script>

<style lang="scss" scoped>
.operational-procurement-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;

  .page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 内容区块 */
.content-section {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }
  }

  .section-content {
    padding: 24px;
  }
}

/* 表单区域 */
.form-section {
  .request-form {
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
      }

      :deep(.el-form-item__content) {
        .form-input,
        .form-select,
        .form-date-picker {
          .el-input__wrapper {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            box-shadow: none;
            transition: all 0.2s ease;

            &:hover {
              border-color: #4f46e5;
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

/* 详情表格区域 */
.detail-section {
  .detail-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        font-weight: 600;
        color: #6b7280;
        border-radius: 8px 8px 0 0;
        transition: all 0.2s ease;

        &.is-active {
          color: #4f46e5;
          background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);
        }

        &:hover {
          color: #4f46e5;
        }
      }

      .el-tabs__active-bar {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        height: 3px;
        border-radius: 2px;
      }
    }
  }
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 32px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }

  .cancel-btn {
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px 32px;
    font-weight: 600;
    font-size: 16px;
    color: #6b7280;
    transition: all 0.3s ease;

    &:hover {
      border-color: #9ca3af;
      color: #374151;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      background: #f3f4f6;
      color: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .operational-procurement-container {
    padding: 16px;
  }

  .main-content {
    gap: 16px;
  }

  .content-section {
    .section-content {
      padding: 20px;
    }
  }

  .form-section .request-form .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .content-section {
    .section-header {
      padding: 16px 20px;

      .section-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
        margin-right: 12px;
      }

      .section-title {
        font-size: 18px;
      }
    }

    .section-content {
      padding: 16px;
    }
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;

    .submit-btn,
    .cancel-btn {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .operational-procurement-container {
    padding: 12px;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .content-section {
    border-radius: 12px;

    .section-header {
      padding: 12px 16px;
    }

    .section-content {
      padding: 12px;
    }
  }
}
</style>
