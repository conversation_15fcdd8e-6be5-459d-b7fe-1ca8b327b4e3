<!--
 * @Description: 详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-20 16:49:24
 * @LastEditTime: 2024-07-10 15:37:22
-->
<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="申请人">
        {{ userList.find((item) => item.id === detailData.userId)?.nickname }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="交付地点">
        {{ detailData.deliveryLocation }}
      </el-descriptions-item>
      <el-descriptions-item label="交付日期">
        {{ formatDate(detailData.deliveryDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="收货人">
        {{ detailData.receiver }}
      </el-descriptions-item>
    </el-descriptions>
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="采购申请详情" name="purchaseRequestDetail">
        <PurchaseRequestDetailForm
          :disabled="true"
          ref="purchaseRequestDetailFormRef"
          :request-id="detailData.id"
        />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { PurchaseRequestApi, PurchaseRequestVO } from '@/api/erp/purchase/purchaserequest'
import PurchaseRequestDetailForm from './components/PurchaseRequestDetailForm.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'OAOvertimeApplicationDetail' })
const subTabsName = ref('purchaseRequestDetail')
const purchaseRequestDetailFormRef = ref()

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await PurchaseRequestApi.getPurchaseRequest(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(async () => {
  getInfo()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
