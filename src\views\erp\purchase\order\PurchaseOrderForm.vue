<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      :disabled="disabled"
    >
      <el-row :gutter="20">
        <!-- <el-col :span="8">
          <el-form-item label="订单单号" prop="no">
            <el-input disabled v-model="formData.no" placeholder="保存时自动生成" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="订单时间" prop="orderTime">
            <el-date-picker
              v-model="formData.orderTime"
              type="date"
              value-format="x"
              placeholder="选择订单时间"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择的供应商" prop="supplierId">
            <el-select
              @change="changeSelect"
              v-model="formData.supplierId"
              clearable
              filterable
              :disabled="formType === 'autocreate' || formType === 'update'"
              placeholder="请选择供应商"
              class="!w-1/1"
            >
              <el-option
                v-for="item in supplierList"
                :key="item.id"
                :label="item.selQuoteName"
                :value="`${item.id}-${item.selSupplierQuote.supplierId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="关联合同" prop="contractName">
            <div
              ><el-input
                style="margin-right: 50px"
                v-model="formData.customerName"
                disabled
                placeholder="请输入关联合同"
              ></el-input>
              <el-button type="primary" @click="chooseDialog('contract')">关联合同</el-button></div
            >
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formData.remark"
              :rows="1"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="附件" prop="fileUrl">
            <UploadFile :is-show-tip="false" v-model="formData.fileUrl" :limit="1" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 子表的表单 -->
      <ContentWrap>
        <el-tabs v-model="subTabsName" class="-mt-15px -mb-10px">
          <el-tab-pane label="订单产品清单" name="item">
            <PurchaseOrderItemForm
              ref="itemFormRef"
              :items="formData.items"
              :disabled="disabled"
              :data="enquiryData"
              :quantity="formData.itemQuantity"
            />
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>
      <!-- <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="优惠率（%）" prop="discountPercent">
            <el-input-number
              v-model="formData.discountPercent"
              controls-position="right"
              :min="0"
              :precision="2"
              placeholder="请输入优惠率"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款优惠" prop="discountPrice">
            <el-input
              disabled
              v-model="formData.discountPrice"
              :formatter="erpPriceInputFormatter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="优惠后金额">
            <el-input disabled v-model="formData.totalPrice" :formatter="erpPriceInputFormatter" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结算账户" prop="accountId">
            <el-select
              v-model="formData.accountId"
              clearable
              filterable
              placeholder="请选择结算账户"
              class="!w-1/1"
            >
              <el-option
                v-for="item in accountList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付订金" prop="depositPrice">
            <el-input-number
              v-model="formData.depositPrice"
              controls-position="right"
              :min="0"
              :precision="2"
              placeholder="请输入支付订金"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading" v-if="!disabled">
        确 定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <contractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
</template>
<script setup lang="ts">
import { PurchaseOrderApi, PurchaseOrderVO } from '@/api/erp/purchase/order'
import PurchaseOrderItemForm from './components/PurchaseOrderItemForm.vue'
import { SupplierApi, SupplierVO } from '@/api/erp/purchase/supplier'
import { erpPriceInputFormatter, erpPriceMultiply } from '@/utils'
import * as UserApi from '@/api/system/user'
import { AccountApi, AccountVO } from '@/api/erp/finance/account'
import contractDialog from '@/components/ContractDialog/index.vue'
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'

const contractDialogRef = ref()
const chooseDialog = (type) => {
  contractDialogRef.value.openDialog()
}
/** ERP 销售订单表单 */
defineOptions({ name: 'PurchaseOrderForm' })

let enquiryData = ref({})
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const route = useRoute()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改；detail - 详情
const formData = ref<any>({
  id: undefined,
  contractId: undefined,
  contractCode: undefined,
  projectName: undefined,
  customerName: undefined,
  supplierId: undefined,
  accountId: undefined,
  orderTime: undefined,
  remark: undefined,
  fileUrl: undefined,
  discountPercent: 0,
  discountPrice: 0,
  totalPrice: 0,
  depositPrice: 0,
  items: [],
  no: undefined // 订单单号，后端返回
})

const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractCode = item.contractCode
  formData.value.projectName = item.contractName
  formData.value.customerName = item.customerName
}
const formRules = reactive({
  supplierId: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
  orderTime: [{ required: true, message: '订单时间不能为空', trigger: 'blur' }]
})
const disabled = computed(() => formType.value === 'detail')
const formRef = ref() // 表单 Ref
const supplierList = ref<any>([]) // 供应商列表
const accountList = ref<AccountVO[]>([]) // 账户列表
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 子表的表单 */
const subTabsName = ref('item')
const itemFormRef = ref()

/** 计算 discountPrice、totalPrice 价格 */
watch(
  () => formData.value,
  (val) => {
    if (!val) {
      return
    }
    const totalPrice = val.items.reduce((prev, curr) => prev + curr.totalPrice, 0)
    const discountPrice =
      val.discountPercent != null ? erpPriceMultiply(totalPrice, val.discountPercent / 100.0) : 0
    formData.value.discountPrice = discountPrice
    formData.value.totalPrice = totalPrice - discountPrice
  },
  { deep: true }
)

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  if (type === 'autocreate') {
    let data = await PurchaseInquiryApi.getPurchaseInquiry(route.query.id as any)
    enquiryData.value = data.selSupplierQuote
    nextTick(() => {
      formData.value.itemQuantity = data.itemQuantity
      formData.value.supplierId = data.id + '-' + data.selSupplierQuote.supplierId
      formData.value.contractCode = data.contractCode
      formData.value.projectName = data.projectName
    })
  }
  dialogVisible.value = true
  dialogTitle.value =
    type === 'create'
      ? t('action.create')
      : type === 'update'
        ? t('action.update')
        : t('action.create')
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PurchaseOrderApi.getPurchaseOrder(id)
      console.log('formData.value', formData.value)

      formData.value.supplierId = formData.value.id + '-' + formData.value.supplierId
    } finally {
      formLoading.value = false
    }
  }
  // 加载供应商列表
  let data = await PurchaseInquiryApi.getPurchaseInquiryPage({
    pageNo: 1,
    pageSize: -1,
    result: 2
  })
  supplierList.value = data.list

  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
  // 加载账户列表
  accountList.value = await AccountApi.getAccountSimpleList()
  const defaultAccount = accountList.value.find((item) => item.defaultStatus)
  if (defaultAccount) {
    formData.value.accountId = defaultAccount.id
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  await itemFormRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseOrderVO as any
    let res = data.supplierId
    data.refInquiryId = Number(data.supplierId.split('-')[0])
    data.supplierId = Number(res.split('-')[1])
    if (formType.value === 'create' || formType.value === 'autocreate') {
      await PurchaseOrderApi.createPurchaseOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await PurchaseOrderApi.updatePurchaseOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeSelect = (id) => {
  let data = supplierList.value.find((item) => {
    return item.selSupplierQuote.supplierId === Number(id?.split('-')[1])
  })
  formData.value.contractCode = data.contractCode
  formData.value.projectName = data.projectName
  enquiryData.value = data.selSupplierQuote
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    supplierId: undefined,
    accountId: undefined,
    orderTime: undefined,
    remark: undefined,
    fileUrl: undefined,
    discountPercent: 0,
    discountPrice: 0,
    totalPrice: 0,
    depositPrice: 0,
    items: []
  }
  formRef.value?.resetFields()
}
</script>
