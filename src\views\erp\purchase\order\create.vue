<template>
  <div class="purchase-order-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">采购订单</h2>
      <p class="page-subtitle">创建新的采购订单</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 基本信息区域 -->
      <div class="content-section form-section">
        <div class="section-header">
          <div class="section-icon">
            <Icon icon="ep:edit" />
          </div>
          <h3 class="section-title">订单信息</h3>
        </div>
        <div class="section-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="formLoading"
            class="order-form"
          >
            <div class="form-row">
              <el-form-item label="订单时间" prop="orderTime" class="form-item">
                <el-date-picker
                  v-model="formData.orderTime"
                  type="date"
                  value-format="x"
                  placeholder="选择订单时间"
                  class="form-date-picker"
                />
              </el-form-item>
              <el-form-item label="选择的供应商" prop="supplierId" class="form-item">
                <el-select
                  @change="changeSelect"
                  v-model="formData.supplierId"
                  clearable
                  filterable
                  :disabled="formType === 'autocreate' || formType === 'update'"
                  placeholder="请选择供应商"
                  class="form-select"
                >
                  <el-option
                    v-for="item in supplierList"
                    :key="item.id"
                    :label="item.selQuoteName"
                    :value="`${item.id}-${item.selSupplierQuote.supplierId}`"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="备注" prop="remark" class="form-item">
                <el-input
                  type="textarea"
                  v-model="formData.remark"
                  :rows="2"
                  placeholder="请输入备注"
                  class="form-input"
                />
              </el-form-item>
              <el-form-item label="附件" prop="fileUrl" class="form-item">
                <UploadFile :is-show-tip="false" v-model="formData.fileUrl" :limit="1" />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 详情表格区域 -->
      <div class="content-section detail-section">
        <div class="section-header">
          <div class="section-icon">
            <Icon icon="ep:list" />
          </div>
          <h3 class="section-title">订单详情</h3>
        </div>
        <div class="section-content">
          <el-tabs v-model="subTabsName" class="detail-tabs">
            <el-tab-pane label="订单产品清单" name="item">
              <PurchaseOrderItemForm
                ref="itemFormRef"
                :items="formData.items"
                :disabled="false"
                :data="enquiryData"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button @click="submitForm" type="primary" :disabled="formLoading" class="submit-btn">
        提交订单
      </el-button>
      <el-button @click="handleClose" type="default" :disabled="formLoading" class="cancel-btn">
        取消
      </el-button>
    </div>
  </div>

  <contractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
</template>

<script lang="ts" setup>
import { PurchaseOrderApi, PurchaseOrderVO } from '@/api/erp/purchase/order'
import PurchaseOrderItemForm from './components/PurchaseOrderItemForm.vue'
import { SupplierApi, SupplierVO } from '@/api/erp/purchase/supplier'
import { erpPriceInputFormatter, erpPriceMultiply } from '@/utils'
import * as UserApi from '@/api/system/user'
import { AccountApi, AccountVO } from '@/api/erp/finance/account'
import contractDialog from '@/components/ContractDialog/index.vue'
import { PurchaseInquiryApi, PurchaseInquiryVO } from '@/api/erp/enquiry'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'PurchaseOrderCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作

const contractDialogRef = ref()
const { back, currentRoute } = useRouter()
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('create') // 表单的类型：create - 新增；update - 修改；detail - 详情
const formData = ref<any>({
  id: undefined,
  contractId: undefined,
  contractCode: undefined,
  projectName: undefined,
  customerName: undefined,
  supplierId: undefined,
  accountId: undefined,
  orderTime: undefined,
  remark: undefined,
  fileUrl: undefined,
  discountPercent: 0,
  discountPrice: 0,
  totalPrice: 0,
  depositPrice: 0,
  items: [],
  no: undefined // 订单单号，后端返回
})

let enquiryData = ref({})

const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractCode = item.contractCode
  formData.value.projectName = item.contractName
  formData.value.customerName = item.customerName
}

const formRules = reactive({
  supplierId: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
  orderTime: [{ required: true, message: '订单时间不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref
const supplierList = ref<any>([]) // 供应商列表
const accountList = ref<AccountVO[]>([]) // 账户列表
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 子表的表单 */
const subTabsName = ref('item')
const itemFormRef = ref()

/** 计算 discountPrice、totalPrice 价格 */
watch(
  () => formData.value,
  (val) => {
    if (!val) {
      return
    }
    const totalPrice = val.items.reduce((prev, curr) => prev + curr.totalPrice, 0)
    const discountPrice =
      val.discountPercent != null ? erpPriceMultiply(totalPrice, val.discountPercent / 100.0) : 0
    formData.value.discountPrice = discountPrice
    formData.value.totalPrice = totalPrice - discountPrice
  },
  { deep: true }
)

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  await itemFormRef.value.validate()

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseOrderVO as any
    data.supplierId = Number(data.supplierId.split('-')[1])

    if (formType.value === 'create' || formType.value === 'autocreate') {
      await PurchaseOrderApi.createPurchaseOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await PurchaseOrderApi.updatePurchaseOrder(data)
      message.success(t('common.updateSuccess'))
    }

    handleClose()
  } finally {
    formLoading.value = false
  }
}

const changeSelect = (id) => {
  let data = supplierList.value.find((item) => {
    return item.selSupplierQuote.supplierId === Number(id?.split('-')[1])
  })
  formData.value.contractCode = data.contractCode
  formData.value.projectName = data.projectName
  enquiryData.value = data.selSupplierQuote
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  if (route.query.id) {
    let data = await PurchaseInquiryApi.getPurchaseInquiry(route.query.id as any)
    enquiryData.value = data.selSupplierQuote
    nextTick(() => {
      formData.value.supplierId = data.id + '-' + data.selSupplierQuote.supplierId
      formData.value.contractCode = data.contractCode
      formData.value.projectName = data.projectName
    })
    formType.value = 'autocreate'
  }

  // 加载供应商列表
  let data = await PurchaseInquiryApi.getPurchaseInquiryPage({
    pageNo: 1,
    pageSize: -1,
    result: 2
  })
  supplierList.value = data.list

  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
  // 加载账户列表
  accountList.value = await AccountApi.getAccountSimpleList()
  const defaultAccount = accountList.value.find((item) => item.defaultStatus)
  if (defaultAccount) {
    formData.value.accountId = defaultAccount.id
  }
})
</script>

<style lang="scss" scoped>
.purchase-order-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;

  .page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 内容区块 */
.content-section {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }
  }

  .section-content {
    padding: 24px;
  }
}

/* 表单区域 */
.form-section {
  .order-form {
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
      }

      :deep(.el-form-item__content) {
        .form-input,
        .form-select,
        .form-date-picker {
          .el-input__wrapper {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            box-shadow: none;
            transition: all 0.2s ease;

            &:hover {
              border-color: #4f46e5;
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

/* 详情表格区域 */
.detail-section {
  .detail-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        font-weight: 600;
        color: #6b7280;
        border-radius: 8px 8px 0 0;
        transition: all 0.2s ease;

        &.is-active {
          color: #4f46e5;
          background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);
        }

        &:hover {
          color: #4f46e5;
        }
      }

      .el-tabs__active-bar {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        height: 3px;
        border-radius: 2px;
      }
    }
  }
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 32px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }

  .cancel-btn {
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px 32px;
    font-weight: 600;
    font-size: 16px;
    color: #6b7280;
    transition: all 0.3s ease;

    &:hover {
      border-color: #9ca3af;
      color: #374151;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      background: #f3f4f6;
      color: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .purchase-order-container {
    padding: 16px;
  }

  .main-content {
    gap: 16px;
  }

  .content-section {
    .section-content {
      padding: 20px;
    }
  }

  .form-section .order-form .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .content-section {
    .section-header {
      padding: 16px 20px;

      .section-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
        margin-right: 12px;
      }

      .section-title {
        font-size: 18px;
      }
    }

    .section-content {
      padding: 16px;
    }
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;

    .submit-btn,
    .cancel-btn {
      width: 100%;
    }
  }
}
</style>
