<template>
  <div class="purchase-order-detail-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">采购订单详情</h2>
      <p class="page-subtitle">Purchase Order Details</p>
    </div>

    <!-- 返回按钮 -->
    <div class="action-bar" v-if="showBack">
      <el-button @click="goBack" type="primary" class="elegant-button primary">
        <el-icon><Back /></el-icon>
        返回列表
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <!-- 基本信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
          </div>
        </div>
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">订单单号</div>
              <div class="info-value">{{ detailData.no }}</div>
            </div>
            <div class="info-item" v-if="detailData.contractCode">
              <div class="info-label">合同编号</div>
              <div class="info-value">{{ detailData.contractCode }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">供应商</div>
              <div class="info-value">{{ detailData.supplierName }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">订单时间</div>
              <div class="info-value">{{ formatDate(detailData.orderTime, 'YYYY-MM-DD') }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">创建人</div>
              <div class="info-value">
                <el-avatar :size="24" class="user-avatar">
                  {{ detailData.creatorName?.charAt(0) }}
                </el-avatar>
                <span class="user-name">{{ detailData.creatorName }}</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">创建时间</div>
              <div class="info-value">{{ formatDate(detailData.createTime, 'YYYY-MM-DD') }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">审核状态</div>
              <div class="info-value">
                <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="detailData.result" />
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">总数量</div>
              <div class="info-value">{{ erpCountInputFormatter(detailData.totalCount) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">入库数量</div>
              <div class="info-value">{{ erpCountInputFormatter(detailData.inCount) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">退货数量</div>
              <div class="info-value">{{ erpCountInputFormatter(detailData.returnCount) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">金额合计</div>
              <div class="info-value">{{ erpPriceInputFormatter(detailData.totalProductPrice) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">含税金额</div>
              <div class="info-value">{{ erpPriceInputFormatter(detailData.totalPrice) }}</div>
            </div>
            <div class="info-item full-width" v-if="detailData.remark">
              <div class="info-label">备注</div>
              <div class="info-value remark-text">{{ detailData.remark }}</div>
            </div>
            <div class="info-item full-width" v-if="detailData.fileUrl">
              <div class="info-label">采购合同</div>
              <div class="info-value">
                <FileListPreview :fileUrl="detailData.fileUrl" />
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- 订单详情卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-shopping-cart-2"></i>
            <span>订单产品清单</span>
          </div>
        </div>
        <div class="card-content">
          <PurchaseOrderItemForm
            :disabled="false"
            ref="itemFormRef"
            :items="detailData.items || []"
            :data="undefined"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { PurchaseOrderApi, PurchaseOrderVO } from '@/api/erp/purchase/order'
import PurchaseOrderItemForm from './components/PurchaseOrderItemForm.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { erpCountTableColumnFormatter, erpPriceTableColumnFormatter } from '@/utils'
import FileListPreview from '@/components/FileListPreview/index.vue'
import { erpCountInputFormatter, erpPriceInputFormatter } from '@/utils'

defineOptions({ name: 'PurchaseOrderDetail' })

const itemFormRef = ref()
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})

const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await PurchaseOrderApi.getPurchaseOrder(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}



defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(async () => {
  getInfo()
})
</script>

<style lang="scss" scoped>
.purchase-order-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

.action-bar {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.elegant-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  
  .card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    
    i {
      font-size: 20px;
      color: #3b82f6;
    }
  }
}

.card-content {
  padding: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  &.full-width {
    grid-column: 1 / -1;
  }
  
  .info-label {
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .info-value {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.5;
    
    &.remark-text {
      background: #f8fafc;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      font-weight: 400;
      line-height: 1.6;
    }
  }
}

.user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  font-weight: 600;
  margin-right: 8px;
}

.user-name {
  font-weight: 500;
  color: #1e293b;
}

// 合同预览样式
.contract-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .contract-text {
    font-size: 14px;
    color: #64748b;
  }
  
  .preview-btn {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .purchase-order-detail-container {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
    }
    
    .page-subtitle {
      font-size: 14px;
    }
  }
  
  .content-wrapper {
    gap: 16px;
  }
  
  .card-header {
    padding: 16px 20px;
    
    .card-title {
      font-size: 16px;
    }
  }
  
  .card-content {
    padding: 20px;
  }
  
  .info-grid {
    gap: 16px;
  }
  
  .info-item {
    .info-label {
      font-size: 13px;
    }
    
    .info-value {
      font-size: 15px;
    }
  }
}
</style> 
