<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="订单单号" prop="no">
        <el-input
          v-model="queryParams.no"
          placeholder="请输入订单单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="产品" prop="productId">
        <el-select
          v-model="queryParams.productId"
          clearable
          filterable
          placeholder="请选择产品"
          class="!w-240px"
        >
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单时间" prop="orderTime">
        <el-date-picker
          v-model="queryParams.orderTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <el-select
          v-model="queryParams.supplierId"
          clearable
          filterable
          placeholder="请选择供供应商"
          class="!w-240px"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-select
          v-model="queryParams.result"
          placeholder="请选择审核状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb-2">
      <!-- <el-button type="primary" plain @click="handleCreate">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button> -->
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['erp:purchase-order:export']"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column min-width="180" label="订单单号" align="center" prop="no" /> -->
      <el-table-column min-width="180" label="合同编号" align="center" prop="contractCode">
        <template #default="scope">
          <el-link
            v-if="scope.row.contractCode"
            type="primary"
            :underline="false"
            @click="handleContractClick(scope.row.contractCode)"
          >
            {{ scope.row.contractCode }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column min-width="180" label="客户名称" align="center" prop="customerName" /> -->

      <el-table-column label="产品信息" align="center" prop="productNames" min-width="200">
        <template #default="scope">
          <el-link
            v-if="scope.row.productNames"
            type="primary"
            :underline="false"
            @click="handleProductClick(scope.row.productNames)"
          >
            {{ scope.row.productNames }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName" min-width="300">
        <template #default="scope">
          <el-link
            v-if="scope.row.supplierName"
            type="primary"
            :underline="false"
            @click="handleSupplierClick(scope.row)"
          >
            {{ scope.row.supplierName }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单时间"
        align="center"
        prop="orderTime"
        :formatter="dateFormatter2"
        width="120px"
      />
      <el-table-column label="创建人" align="center" prop="creatorName" />
      <el-table-column
        label="总数量"
        align="center"
        prop="totalCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="入库数量"
        align="center"
        prop="inCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="退货数量"
        align="center"
        prop="returnCount"
        :formatter="erpCountTableColumnFormatter"
      />
      <el-table-column
        label="金额合计"
        align="center"
        prop="totalProductPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column
        label="含税金额"
        align="center"
        prop="totalPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column
        label="支付订金"
        align="center"
        prop="depositPrice"
        :formatter="erpPriceTableColumnFormatter"
      />
      <el-table-column label="审核状态" align="center" prop="result" fixed="right" min-width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="center" fixed="right" width="90" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ERP_AUDIT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" fixed="right" width="260">
        <template #default="scope">
          <!-- 草稿状态的操作按钮 -->
          <template v-if="scope.row.result === -1">
            <el-button
              link
              type="primary"
              @click="handleEdit(scope.row)"
              v-hasPermi="['erp:purchase-order:update']"
            >
              修改
            </el-button>
            <el-button
              link
              type="success"
              @click="handleSubmit(scope.row)"
              v-hasPermi="['erp:purchase-order:update']"
            >
              提交
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['erp:purchase-order:delete']"
            >
              删除
            </el-button>
          </template>

          <!-- 非草稿状态的操作按钮 -->
          <template v-else>
            <el-button
              link
              type="warning"
              @click="reissue(scope.row)"
              v-if="scope.row.result === 3 || scope.row.result === 4"
            >
              重新发起
            </el-button>
            <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
            <el-button link type="primary" @click="handleProcessDetail(scope.row)">
              进度
            </el-button>
            <!-- <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['erp:purchase-order:update']"
              :disabled="scope.row.status === 20"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              @click="handleUpdateStatus(scope.row.id, 20)"
              v-hasPermi="['erp:purchase-order:update-status']"
              v-if="scope.row.status === 10"
            >
              审批
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleUpdateStatus(scope.row.id, 10)"
              v-hasPermi="['erp:purchase-order:update-status']"
              v-else-if="scope.row.status === 20"
            >
              反审批
            </el-button> -->
            <el-button
              link
              type="danger"
              @click="cancelOrder(scope.row)"
              v-if="scope.row.result === 1"
            >
              取消
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              v-if="scope.row.result === 3 || scope.row.result === 4"
            >
              删除
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseOrderForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseOrderApi, PurchaseOrderVO } from '@/api/erp/purchase/order'
import PurchaseOrderForm from './PurchaseOrderForm.vue'
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { UserVO } from '@/api/system/user'
import * as UserApi from '@/api/system/user'
import { erpCountTableColumnFormatter, erpPriceTableColumnFormatter } from '@/utils'
import { SupplierApi, SupplierVO } from '@/api/erp/purchase/supplier'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'

/** ERP 销售订单列表 */
defineOptions({ name: 'ErpPurchaseOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<PurchaseOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  no: undefined,
  supplierId: undefined,
  productId: undefined,
  orderTime: [],
  status: undefined,
  remark: undefined,
  creator: undefined,
  inStatus: undefined,
  returnStatus: undefined,
  result: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const productList = ref<ProductVO[]>([]) // 产品列表
const supplierList = ref<SupplierVO[]>([]) // 供应商列表
const userList = ref<UserVO[]>([]) // 用户列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseOrderApi.getPurchaseOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'PurchaseOrderCreate' })
}

/** 重新发起 */
const reissue = (row) => {
  router.push({
    name: 'PurchaseOrderCreate',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'PurchaseOrderDetail',
    query: {
      id: row.id
    }
  })
}

/** 点击合同编号跳转到合同页面 */
const handleContractClick = (contractCode: string) => {
  router.push({
    name: 'Contract',
    query: {
      contractCode: contractCode
    }
  })
}

/** 点击产品信息跳转到产品详情页面 */
const handleProductClick = (productNames: string) => {
  // 由于productNames可能包含多个产品名称，这里跳转到产品列表页面并搜索第一个产品
  const firstProductName = productNames.split(',').shift()?.trim()
  if (firstProductName) {
    router.push({
      name: 'ErpProduct',
      query: {
        name: firstProductName
      }
    })
  }
}

/** 点击供应商跳转到供应商详情页面 */
const handleSupplierClick = (row: any) => {
  console.log('handleSupplierClick called with row:', row)
  if (row.supplierId) {
    router.push({
      name: 'SupplierDetail',
      query: {
        id: row.supplierId
      }
    })
  } else {
    console.warn('No supplierId found in row:', row)
    message.warning('供应商ID不存在')
  }
}

/** 取消订单操作 */
const cancelOrder = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 草稿状态 - 修改操作 */
const handleEdit = (row) => {
  router.push({
    name: 'PurchaseOrderCreate',
    query: {
      id: row.id,
      type: 'edit'
    }
  })
}

/** 草稿状态 - 提交操作 */
const handleSubmit = async (row) => {
  try {
    // 提交的二次确认
    await message.confirm('确定提交该采购订单吗？')
    // 发起提交流程（这里需要根据实际的提交流程接口调整）
    await PurchaseOrderApi.submitPurchaseOrder(row.id)
    message.success('提交成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 草稿状态 - 删除操作 */
const handleDelete = async (row) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseOrderApi.deletePurchaseOrder([row.id])
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 审批/反审批操作 */
const handleUpdateStatus = async (id: number, status: number) => {
  try {
    // 审批的二次确认
    await message.confirm(`确定${status === 20 ? '审批' : '反审批'}该订单吗？`)
    // 发起审批
    await PurchaseOrderApi.updatePurchaseOrderStatus(id, status)
    message.success(`${status === 20 ? '审批' : '反审批'}成功`)
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseOrderApi.exportPurchaseOrder(queryParams)
    download.excel(data, '销售订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 加载产品、仓库列表、供应商
  productList.value = await ProductApi.getProductSimpleList()
  supplierList.value = await SupplierApi.getSupplierSimpleList()
  userList.value = await UserApi.getSimpleUserList()
})

onActivated(() => {
  // 刷新列表数据
  getList()

  if (route.query.id) {
    openForm('autocreate')
  }
})
</script>
