<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="userId">
        <el-select
          v-model="queryParams.userId"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="handleCreate">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      row-key="id"
      :expand-row-keys="expandedRows"
    >
      <!-- 展开列 -->
      <el-table-column label="展开" width="80" align="center">
        <template #default="scope">
          <el-button type="text" @click="toggleExpand(scope.row)">
            {{ expandedRows.includes(scope.row.id) ? '收起' : '展开' }}
          </el-button>
        </template>
      </el-table-column>
      <!-- 子表的列表 -->
      <el-table-column type="expand" width="0">
        <template #default="scope">
          <el-tabs model-value="purchaseRequestDetail" style="padding: 0 20px">
            <el-tab-pane label="采购申请详情" name="purchaseRequestDetail">
              <PurchaseRequestDetailList :request-id="scope.row.id" :item="scope.row" />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center" prop="contractCode" />
      <el-table-column label="合同名称" align="center" prop="contractName" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column
        label="交付日期"
        align="center"
        prop="deliveryDate"
        :formatter="dateFormatter2"
      />
      <el-table-column label="申请人" align="center" prop="userId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="收货人" align="center" prop="receiver" />
      <el-table-column label="操作" align="center" min-width="200px">
        <template #default="scope">
          <!-- 草稿状态的操作按钮 -->
          <template v-if="scope.row.result === -1">
            <el-button
              link
              type="primary"
              @click="handleEdit(scope.row)"
              v-hasPermi="['erp:purchase-request:update']"
            >
              修改
            </el-button>
            <el-button
              link
              type="success"
              @click="handleSubmit(scope.row)"
              v-hasPermi="['erp:purchase-request:update']"
            >
              提交
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['erp:purchase-request:delete']"
            >
              删除
            </el-button>
          </template>
          
          <!-- 非草稿状态的操作按钮 -->
          <template v-else>
            <el-button
              link
              type="warning"
              @click="reissue(scope.row)"
              v-if="scope.row.result === 3 || scope.row.result === 4"
            >
              重新发起
            </el-button>
            <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
            <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
            <el-button
              link
              type="danger"
              @click="cancelLeave(scope.row)"
              v-if="scope.row.result === 1"
            >
              取消
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseRequestForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseRequestApi, PurchaseRequestVO } from '@/api/erp/purchase/purchaserequest'
import PurchaseRequestForm from './PurchaseRequestForm.vue'
import PurchaseRequestDetailList from './components/PurchaseRequestDetailList.vue'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as UserApi from '@/api/system/user'

/** 采购申请 列表 */
defineOptions({ name: 'PurchaseRequest' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<PurchaseRequestVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  purchaseType: 1,
  result: undefined,
  userId: undefined,
  contractCode: undefined,
  contractName: undefined,
  processInstanceId: undefined,
  remark: undefined,
  projectName: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const expandedRows = ref<number[]>([]) // 展开的行

const reissue = (row) => {
  router.push({
    name: 'ErpPurchaseRequestCreate',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ErpPurchaseRequestDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 草稿状态 - 修改操作 */
const handleEdit = (row) => {
  router.push({
    name: 'ErpPurchaseRequestCreate',
    query: {
      id: row.id,
      type: 'edit'
    }
  })
}

/** 草稿状态 - 提交操作 */
const handleSubmit = async (row) => {
  try {
    // 提交的二次确认
    await message.confirm('确定提交该采购申请吗？')
    // 发起提交流程（这里需要根据实际的提交流程接口调整）
    await PurchaseRequestApi.submitPurchaseRequest(row.id)
    message.success('提交成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 草稿状态 - 删除操作 */
const handleDelete = async (row) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseRequestApi.deletePurchaseRequest(row.id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'ErpPurchaseRequestCreate' })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseRequestApi.getPurchaseRequestPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}



/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseRequestApi.exportPurchaseRequest(queryParams)
    download.excel(data, '采购申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 切换展开/收起状态 */
const toggleExpand = (row) => {
  const index = expandedRows.value.indexOf(row.id)
  if (index > -1) {
    expandedRows.value.splice(index, 1)
  } else {
    expandedRows.value.push(row.id)
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})

onActivated(() => {
  getList()
})
</script>
