<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1400">
    <div class="supplier-form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        class="elegant-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-office-building"></i>
            <span>基本信息</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="企业名称" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入企业名称" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序号" prop="sort">
                  <el-input-number class="elegant-input-number" v-model="formData.sort" placeholder="请输入排序号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供应商等级" prop="supplierLevel">
                  <el-input v-model="formData.supplierLevel" placeholder="请输入供应商等级" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业法人" prop="legalRepresentative">
                  <el-input v-model="formData.legalRepresentative" placeholder="请输入企业法人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业类型" prop="companyType">
                  <el-select v-model="formData.companyType" placeholder="请选择企业类型" class="elegant-select">
                    <el-option
                      v-for="dict in getStrDictOptions(DICT_TYPE.COMPANY_TYPE)"
                      :key="dict.value"
                      :value="dict.value"
                      :label="dict.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册资本" prop="registeredCapital">
                  <el-input
                    @input="(v) => (formData.registeredCapital = v.replace(/[^\d.]/g, ''))"
                    class="elegant-input"
                    v-model="formData.registeredCapital"
                    placeholder="请输入注册资本"
                  >
                    <template #append> 万元 </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册地址" prop="registeredAddress">
                  <el-input v-model="formData.registeredAddress" placeholder="请输入注册地址" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经营范围" prop="businessScope">
                  <el-input v-model="formData.businessScope" placeholder="请输入经营范围" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业编号" prop="companyCode">
                  <el-input v-model="formData.companyCode" placeholder="请输入企业编号" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="信用代码" prop="creditCode">
                  <el-input v-model="formData.creditCode" placeholder="请输入信用代码" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业规模" prop="companyScale">
                  <el-select v-model="formData.companyScale" placeholder="请选择企业规模" class="elegant-select">
                    <el-option
                      v-for="dict in getStrDictOptions(DICT_TYPE.COMPANY_SCALE)"
                      :key="dict.value"
                      :value="dict.value"
                      :label="dict.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册日期" prop="registrationDate">
                  <el-date-picker
                    class="elegant-date-picker"
                    v-model="formData.registrationDate"
                    type="date"
                    value-format="x"
                    placeholder="选择注册日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="信用等级" prop="creditRating">
                  <el-select v-model="formData.creditRating" placeholder="请选择信用等级" class="elegant-select">
                    <el-option
                      v-for="dict in getStrDictOptions(DICT_TYPE.CREDIT_RATING)"
                      :key="dict.value"
                      :value="dict.value"
                      :label="dict.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="社保员工人数" prop="employeeCount">
                  <el-input v-model="formData.employeeCount" placeholder="请输入社保员工人数" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="年营业额" prop="annualTurnover">
                  <el-input v-model="formData.annualTurnover" placeholder="请输入年营业额" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="售后内容与响应时间" prop="afterSalesService" label-width="150px">
                  <el-input v-model="formData.afterSalesService" placeholder="请输入售后内容与响应时间" class="elegant-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 文件上传 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>资质文件</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="行业资质" prop="industryQualification">
                  <UploadFile v-model="formData.industryQualification" :file-size="1024" class="elegant-upload" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="质量认证" prop="qualityCertification">
                  <UploadFile v-model="formData.qualityCertification" :file-size="1024" class="elegant-upload" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="其他认证" prop="otherCertifications">
                  <UploadFile v-model="formData.otherCertifications" :file-size="1024" class="elegant-upload" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="营业执照" prop="businessLicense">
                  <UploadFile v-model="formData.businessLicense" :file-size="1024" class="elegant-upload" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 联系人信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-user"></i>
            <span>联系人信息</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input v-model="formData.contactPerson" placeholder="请输入联系人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属部门" prop="contactDept">
                  <el-input v-model="formData.contactDept" placeholder="请输入联系人所属部门" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电子邮件" prop="email">
                  <el-input v-model="formData.email" placeholder="请输入电子邮件" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统电话" prop="telephone">
                  <el-input v-model="formData.telephone" placeholder="请输入系统电话" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系地址" prop="address">
                  <el-input v-model="formData.address" placeholder="请输入联系地址" class="elegant-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 负责人信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-s-custom"></i>
            <span>负责人信息</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="采购负责人" prop="purchaseOwner">
                  <el-input v-model="formData.purchaseOwner" placeholder="请输入采购负责人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="财务负责人" prop="financeOwner">
                  <el-input v-model="formData.financeOwner" placeholder="请输入财务负责人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售负责人" prop="saleOwner">
                  <el-input v-model="formData.saleOwner" placeholder="请输入销售负责人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="售后负责人" prop="afterAles">
                  <el-input v-model="formData.afterAles" placeholder="请输入售后负责人" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品负责人" prop="productOwner">
                  <el-input v-model="formData.productOwner" placeholder="请输入产品负责人" class="elegant-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-money"></i>
            <span>财务信息</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="纳税人识别号" prop="taxNo">
                  <el-input v-model="formData.taxNo" placeholder="请输入纳税人识别号" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="税率" prop="taxPercent">
                  <el-input v-model="formData.taxPercent" placeholder="请输入税率" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户行" prop="bankName">
                  <el-input v-model="formData.bankName" placeholder="请输入开户行" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户账号" prop="bankAccount">
                  <el-input v-model="formData.bankAccount" placeholder="请输入开户账号" class="elegant-input" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="开户地址" prop="bankAddress">
                  <el-input v-model="formData.bankAddress" placeholder="请输入开户地址" class="elegant-input" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-info"></i>
            <span>其他信息</span>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="开启状态" prop="status">
                  <el-radio-group v-model="formData.status" class="elegant-radio-group">
                    <el-radio label="1">开启</el-radio>
                    <el-radio label="2">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 5 }"
                    v-model="formData.remark"
                    placeholder="请输入备注"
                    class="elegant-textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="submitForm" type="primary" :disabled="formLoading" class="elegant-button primary">
          <i class="el-icon-check"></i>
          确认提交
        </el-button>
        <el-button @click="dialogVisible = false" class="elegant-button default">
          <i class="el-icon-close"></i>
          取消
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { SupplierApi, SupplierVO } from '@/api/erp/supplier'

/** ERP 供应商主表 表单 */
defineOptions({ name: 'SupplierForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: any = ref({
  id: undefined,
  name: undefined,
  supplierLevel: undefined,
  legalRepresentative: undefined,
  companyType: undefined,
  registeredCapital: undefined,
  registeredAddress: undefined,
  businessScope: undefined,
  sort: undefined,
  companyCode: undefined,
  creditCode: undefined,
  companyScale: undefined,
  registrationDate: undefined,
  creditRating: undefined,
  businessLicense: undefined,
  employeeCount: undefined,
  annualTurnover: undefined,
  industryQualification: undefined,
  qualityCertification: undefined,
  otherCertifications: undefined,
  afterSalesService: undefined,
  contactPerson: undefined,
  contactDept: undefined,
  email: undefined,
  telephone: undefined,
  address: undefined,
  purchaseOwner: undefined,
  financeOwner: undefined,
  saleOwner: undefined,
  afterAles: undefined,
  productOwner: undefined,
  taxNo: undefined,
  taxPercent: undefined,
  bankName: undefined,
  bankAccount: undefined,
  bankAddress: undefined,
  status: undefined,
  remark: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
  creditCode: [{ required: true, message: '信用代码不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupplierApi.getSupplier(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SupplierVO
    if (formType.value === 'create') {
      await SupplierApi.createSupplier(data)
      message.success(t('common.createSuccess'))
    } else {
      await SupplierApi.updateSupplier(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    supplierLevel: undefined,
    legalRepresentative: undefined,
    companyType: undefined,
    registeredCapital: undefined,
    registeredAddress: undefined,
    businessScope: undefined,
    sort: undefined,
    companyCode: undefined,
    creditCode: undefined,
    companyScale: undefined,
    registrationDate: undefined,
    creditRating: undefined,
    businessLicense: undefined,
    employeeCount: undefined,
    annualTurnover: undefined,
    industryQualification: undefined,
    qualityCertification: undefined,
    otherCertifications: undefined,
    afterSalesService: undefined,
    contactPerson: undefined,
    contactDept: undefined,
    email: undefined,
    telephone: undefined,
    address: undefined,
    purchaseOwner: undefined,
    financeOwner: undefined,
    saleOwner: undefined,
    afterAles: undefined,
    productOwner: undefined,
    taxNo: undefined,
    taxPercent: undefined,
    bankName: undefined,
    bankAccount: undefined,
    bankAddress: undefined,
    status: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.supplier-form-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
}

.form-section {
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  
  i {
    font-size: 18px;
    color: #3b82f6;
  }
  
  span {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }
}

.section-content {
  padding: 20px;
}

.elegant-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }
  }
}

.elegant-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  :deep(.el-input__inner) {
    font-size: 14px;
    color: #1f2937;
    
    &::placeholder {
      color: #9ca3af;
    }
  }
}

.elegant-input-number {
  width: 100%;
  
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    border-radius: 8px 0 0 8px;
    border: 1px solid #d1d5db;
    background: #f9fafb;
  }
  
  :deep(.el-input__wrapper) {
    border-radius: 0 8px 8px 0;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-select {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-date-picker {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #1f2937;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &::placeholder {
      color: #9ca3af;
    }
  }
}

.elegant-radio-group {
  :deep(.el-radio) {
    margin-right: 24px;
    
    .el-radio__label {
      font-size: 14px;
      color: #374151;
    }
    
    .el-radio__input.is-checked .el-radio__inner {
      border-color: #3b82f6;
      background: #3b82f6;
    }
  }
}

.elegant-upload {
  :deep(.el-upload) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #3b82f6;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px 0;
}

.elegant-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
  
  i {
    font-size: 16px;
  }
  
  &.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }
  
  &.default {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    
    &:hover {
      background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .supplier-form-container {
    padding: 16px;
  }
  
  .section-header {
    padding: 12px 16px;
    
    span {
      font-size: 14px;
    }
  }
  
  .section-content {
    padding: 16px;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .elegant-button {
    width: 100%;
  }
}
</style>
