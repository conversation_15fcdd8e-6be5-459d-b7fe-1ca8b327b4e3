<!--
 * @Description: 供应商新增流程
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2024-01-10 10:40:28
 * @LastEditTime: 2025-07-25 15:57:11
-->
<template>
  <div class="supplier-create-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">{{ formType === 'edit' ? '供应商编辑' : '供应商新增' }}</h2>
      <p class="page-subtitle">{{ formType === 'edit' ? 'Supplier Edit' : 'Supplier Creation' }}</p>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="warning" plain @click="handleImport" class="elegant-button warning">
        <Icon icon="ep:upload" />
        导入
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        class="elegant-button success"
      >
        <Icon icon="ep:download" class="mr-5px" />
        下载模板
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        class="elegant-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-office-building"></i>
            <span>基本信息</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="企业名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入企业名称"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="排序号" prop="sort">
                <el-input-number
                  class="elegant-input-number"
                  v-model="formData.sort"
                  placeholder="请输入排序号"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="供应商等级" prop="supplierLevel">
                <el-input
                  v-model="formData.supplierLevel"
                  placeholder="请输入供应商等级"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="企业法人" prop="legalRepresentative">
                <el-input
                  v-model="formData.legalRepresentative"
                  placeholder="请输入企业法人"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="企业类型" prop="companyType">
                <el-select
                  v-model="formData.companyType"
                  placeholder="请选择企业类型"
                  class="elegant-select"
                >
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.COMPANY_TYPE)"
                    :key="dict.value"
                    :value="dict.value"
                    :label="dict.label"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="注册资本" prop="registeredCapital">
                <el-input
                  @input="(v) => (formData.registeredCapital = v.replace(/[^\d.]/g, ''))"
                  v-model="formData.registeredCapital"
                  placeholder="请输入注册资本"
                  class="elegant-input"
                >
                  <template #append> 万元 </template>
                </el-input>
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="注册地址" prop="registeredAddress">
                <el-input
                  v-model="formData.registeredAddress"
                  placeholder="请输入注册地址"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="经营范围" prop="businessScope">
                <el-input
                  v-model="formData.businessScope"
                  placeholder="请输入经营范围"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="企业编号" prop="companyCode">
                <el-input
                  v-model="formData.companyCode"
                  placeholder="请输入企业编号"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="信用代码" prop="creditCode">
                <el-input
                  v-model="formData.creditCode"
                  placeholder="请输入信用代码"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="企业规模" prop="companyScale">
                <el-select
                  v-model="formData.companyScale"
                  placeholder="请选择企业规模"
                  class="elegant-select"
                >
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.COMPANY_SCALE)"
                    :key="dict.value"
                    :value="dict.value"
                    :label="dict.label"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="注册日期" prop="registrationDate">
                <el-date-picker
                  v-model="formData.registrationDate"
                  type="date"
                  value-format="x"
                  placeholder="选择注册日期"
                  class="elegant-date-picker"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="信用等级" prop="creditRating">
                <el-input
                  v-model="formData.creditRating"
                  placeholder="请输入信用等级"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="社保员工人数" prop="employeeCount">
                <el-input
                  v-model="formData.employeeCount"
                  placeholder="请输入社保员工人数"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="年营业额" prop="annualTurnover">
                <el-input
                  v-model="formData.annualTurnover"
                  placeholder="请输入年营业额"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="售后内容与响应时间" prop="afterSalesService">
                <el-input
                  v-model="formData.afterSalesService"
                  placeholder="请输入售后内容与响应时间"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 资质文件 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>资质文件</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="行业资质" prop="industryQualification">
                <UploadFile v-model="formData.industryQualification" :file-size="1024" />
              </el-form-item>
              <el-form-item label="质量认证" prop="qualityCertification">
                <UploadFile v-model="formData.qualityCertification" :file-size="1024" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="其他认证" prop="otherCertifications">
                <UploadFile v-model="formData.otherCertifications" :file-size="1024" />
              </el-form-item>
              <el-form-item label="营业执照" prop="businessLicense">
                <UploadFile v-model="formData.businessLicense" :file-size="1024" />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 联系人信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-user"></i>
            <span>联系人信息</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="联系人" prop="contactPerson">
                <el-input
                  v-model="formData.contactPerson"
                  placeholder="请输入联系人"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="所属部门" prop="contactDept">
                <el-input
                  v-model="formData.contactDept"
                  placeholder="请输入所属部门"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="电子邮件" prop="email">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入电子邮件"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="系统电话" prop="telephone">
                <el-input
                  v-model="formData.telephone"
                  placeholder="请输入系统电话"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="联系地址" prop="address">
                <el-input
                  v-model="formData.address"
                  placeholder="请输入联系地址"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 负责人信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-s-custom"></i>
            <span>负责人信息</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="采购负责人" prop="purchaseOwner">
                <el-input
                  v-model="formData.purchaseOwner"
                  placeholder="请输入采购负责人"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="财务负责人" prop="financeOwner">
                <el-input
                  v-model="formData.financeOwner"
                  placeholder="请输入财务负责人"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="销售负责人" prop="saleOwner">
                <el-input
                  v-model="formData.saleOwner"
                  placeholder="请输入销售负责人"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="售后负责人" prop="afterAles">
                <el-input
                  v-model="formData.afterAles"
                  placeholder="请输入售后负责人"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="产品负责人" prop="productOwner">
                <el-input
                  v-model="formData.productOwner"
                  placeholder="请输入产品负责人"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-money"></i>
            <span>财务信息</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="纳税人识别号" prop="taxNo">
                <el-input
                  v-model="formData.taxNo"
                  placeholder="请输入纳税人识别号"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="税率" prop="taxPercent">
                <el-input-number
                  class="elegant-input-number"
                  v-model="formData.taxPercent"
                  placeholder="请输入税率"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="开户行" prop="bankName">
                <el-input
                  v-model="formData.bankName"
                  placeholder="请输入开户行"
                  class="elegant-input"
                />
              </el-form-item>
              <el-form-item label="开户账号" prop="bankAccount">
                <el-input
                  v-model="formData.bankAccount"
                  placeholder="请输入开户账号"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="开户地址" prop="bankAddress">
                <el-input
                  v-model="formData.bankAddress"
                  placeholder="请输入开户地址"
                  class="elegant-input"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-goods"></i>
            <span>商品信息</span>
          </div>
          <div class="section-content">
            <!-- 历史商品 -->
            <el-form-item label="历史商品" prop="historicalGoods" class="mb-5">
              <div class="mt-2 flex flex-wrap gap-2 product-tag-container">
                <div
                  v-for="(product, index) in displayHistoricalGoods"
                  :key="product.id"
                  class="bg-blue-50 h-9 rounded-3xl flex items-center pr-2 relative border border-blue-200 product-tag"
                >
                  <div
                    class="w-7 h-7 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center m-1"
                  >
                    商
                  </div>
                  <span class="mx-2 text-sm">{{ product.name }}</span>
                  <el-icon
                    class="ml-2 cursor-pointer hover:text-red-500"
                    @click="handleRemoveHistoricalProduct(index)"
                  >
                    <Close />
                  </el-icon>
                </div>

                <!-- 展开/收起按钮 -->
                <div
                  v-if="selectedHistoricalGoods.length > 20"
                  class="bg-gray-100 h-9 rounded-3xl flex items-center px-3 border border-gray-200 cursor-pointer hover:bg-gray-200"
                  @click="toggleHistoricalExpanded"
                >
                  <span class="text-sm text-gray-600">
                    {{
                      isHistoricalExpanded
                        ? '收起'
                        : `还有${selectedHistoricalGoods.length - 20}个...`
                    }}
                  </span>
                  <el-icon class="ml-1 text-gray-600">
                    <component :is="isHistoricalExpanded ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                </div>

                <el-button type="primary" link @click="chooseHistoricalProducts">
                  <el-icon><Plus /></el-icon> 选择商品
                </el-button>
              </div>
            </el-form-item>

            <!-- 主推商品 -->
            <el-form-item label="主推商品" prop="featuredProducts" class="mb-5 mt-5">
              <div class="featured-products-editor">
                <Editor
                  v-model="formData.featuredProducts"
                  :height="300"
                  :placeholder="'请输入主推商品信息，支持富文本格式'"
                  :editorConfig="editorConfig"
                />
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-info"></i>
            <span>其他信息</span>
          </div>
          <div class="section-content">
            <div class="form-row">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                  v-model="formData.remark"
                  placeholder="请输入备注"
                  class="elegant-textarea"
                />
              </el-form-item>
              <el-form-item label="开启状态" prop="status">
                <el-radio-group v-model="formData.status" class="elegant-radio-group">
                  <el-radio :label="0">开启</el-radio>
                  <el-radio :label="1">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="submit-action-bar">
      <el-button
        @click="saveForm"
        type="primary"
        :disabled="formLoading"
        class="elegant-button primary"
      >
        <i class="el-icon-check"></i>
        {{ formType === 'edit' ? '保存' : '保存' }}
      </el-button>
      <el-button
        @click="submitForm"
        type="success"
        :disabled="formLoading"
        class="elegant-button success"
      >
        <i class="el-icon-upload"></i>
        {{ formType === 'edit' ? '更新' : '提交' }}
      </el-button>
      <el-button @click="handleClose" type="warning" class="elegant-button warning">
        <i class="el-icon-close"></i>
        关闭
      </el-button>
    </div>
  </div>
  <ImportForm ref="importFormRef" @success="getInfo" />

  <!-- 历史商品选择弹窗 -->
  <ProductSelectDialog
    ref="historicalProductDialogRef"
    v-model="selectedHistoricalGoods"
    @confirm="handleHistoricalProductsConfirm"
  />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { SupplierApi, SupplierVO } from '@/api/erp/supplier'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isArray } from '@/utils/is'
import download from '@/utils/download'
import { getAccessToken, getTenantId } from '@/utils/auth'
import ImportForm from './importForm.vue'

defineOptions({ name: 'SupplierCreate' })

let importFormRef = ref()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const exportLoading = ref(false) // 导出的加载中
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('create') // 表单类型：create-新增，edit-编辑
const formData = ref<any>({
  id: undefined,
  name: undefined,
  supplierLevel: undefined,
  legalRepresentative: undefined,
  companyType: undefined,
  registeredCapital: undefined,
  registeredAddress: undefined,
  businessScope: undefined,
  sort: undefined,
  companyCode: undefined,
  creditCode: undefined,
  companyScale: undefined,
  registrationDate: undefined,
  creditRating: undefined,
  businessLicense: undefined,
  employeeCount: undefined,
  annualTurnover: undefined,
  industryQualification: undefined,
  qualityCertification: undefined,
  otherCertifications: undefined,
  afterSalesService: undefined,
  contactPerson: undefined,
  contactDept: undefined,
  email: undefined,
  telephone: undefined,
  address: undefined,
  purchaseOwner: undefined,
  financeOwner: undefined,
  saleOwner: undefined,
  afterAles: undefined,
  productOwner: undefined,
  taxNo: undefined,
  taxPercent: undefined,
  bankName: undefined,
  bankAccount: undefined,
  bankAddress: undefined,
  status: 0, // 默认开启状态
  remark: undefined,
  historicalGoods: undefined,
  featuredProducts: undefined
})

const formRules = reactive({
  name: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
  creditCode: [{ required: true, message: '信用代码不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

// 商品选择相关（只保留历史商品）
interface ProductItem {
  id: number | string
  name: string
  [key: string]: any
}

const selectedHistoricalGoods = ref<ProductItem[]>([]) // 已选择的历史商品
const historicalProductDialogRef = ref() // 历史商品选择弹窗ref
const isHistoricalExpanded = ref(false) // 历史商品是否展开

// 显示的历史商品列表（控制显示数量）
const displayHistoricalGoods = computed(() => {
  if (isHistoricalExpanded.value || selectedHistoricalGoods.value.length <= 20) {
    return selectedHistoricalGoods.value
  }
  return selectedHistoricalGoods.value.slice(0, 20)
})

// 富文本编辑器配置
const editorConfig = {
  placeholder: '请输入主推商品信息...',
  MENU_CONF: {
    // 配置编辑器菜单
    uploadImage: {
      server: import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL + '/infra/file/upload',
      maxFileSize: 5 * 1024 * 1024, // 5M
      maxNumberOfFiles: 10,
      allowedFileTypes: ['image/*'],
      fieldName: 'file',
      meta: { updateSupport: 0 },
      metaWithUrl: true,
      headers: {
        Accept: '*',
        Authorization: 'Bearer ' + getAccessToken(),
        'tenant-id': getTenantId()
      },
      withCredentials: true,
      timeout: 5 * 1000,
      customInsert(res: any, insertFn: any) {
        insertFn(res.data, 'image', res.data)
      }
    }
  }
}

/** 选择历史商品 */
const chooseHistoricalProducts = () => {
  historicalProductDialogRef.value?.open()
}

/** 历史商品选择确认 */
const handleHistoricalProductsConfirm = (products: ProductItem[]) => {
  selectedHistoricalGoods.value = products
}

/** 移除历史商品 */
const handleRemoveHistoricalProduct = (index: number) => {
  const actualIndex = isHistoricalExpanded.value ? index : index
  selectedHistoricalGoods.value.splice(actualIndex, 1)
}

/** 切换历史商品展开状态 */
const toggleHistoricalExpanded = () => {
  isHistoricalExpanded.value = !isHistoricalExpanded.value
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 发起导出
    exportLoading.value = true
    const data = await SupplierApi.getSupplierTemplate()
    download.excel(data, '供应商模板表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleImport = async () => {
  importFormRef.value.open()
}

const getInfo = (data) => {
  formData.value = data[0]
}

/** 保存表单 */
const saveForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    // 处理文件数组
    const fileFields = [
      'industryQualification',
      'qualityCertification',
      'otherCertifications',
      'businessLicense'
    ]
    fileFields.forEach((field) => {
      if (isArray(data[field]) && data[field].length > 0) {
        data[field] = data[field].join()
      }
    })

    // 处理商品数据
    data.historicalGoods =
      selectedHistoricalGoods.value.length > 0
        ? JSON.stringify(selectedHistoricalGoods.value)
        : undefined
    // 主推商品现在是富文本内容，直接使用

    if (formType.value === 'edit') {
      // 编辑模式，调用更新API
      await SupplierApi.updateSupplier(data)
      message.success('更新成功')
    } else {
      // 新增模式，调用创建API
      delete data.id
      // 添加保存标记
      data.submitFlag = false
      await SupplierApi.createSupplier(data)
      message.success('操作成功')
    }

    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    // 处理文件数组
    const fileFields = [
      'industryQualification',
      'qualityCertification',
      'otherCertifications',
      'businessLicense'
    ]
    fileFields.forEach((field) => {
      if (isArray(data[field]) && data[field].length > 0) {
        data[field] = data[field].join()
      }
    })

    // 处理商品数据
    data.historicalGoods =
      selectedHistoricalGoods.value.length > 0
        ? JSON.stringify(selectedHistoricalGoods.value)
        : undefined
    // 主推商品现在是富文本内容，直接使用

    if (formType.value === 'edit') {
      // 编辑模式，调用更新API
      await SupplierApi.updateSupplier(data)
      message.success(t('common.updateSuccess'))
    } else {
      // 新增模式，调用创建API
      delete data.id
      await SupplierApi.createSupplier(data)
      message.success(t('common.createSuccess'))
    }

    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  // 设置表单类型
  if (route.query.type) {
    formType.value = route.query.type as string
  }

  if (route.query.id) {
    let supplierData = await SupplierApi.getSupplier(route.query.id as any)
    formData.value = supplierData
    // 传null报错
    if (!formData.value.featuredProducts) {
      formData.value.featuredProducts = undefined
    }
    // 解析历史商品数据
    if (supplierData.historicalGoods) {
      try {
        selectedHistoricalGoods.value = JSON.parse(supplierData.historicalGoods)
      } catch (e) {
        console.error('解析历史商品数据失败:', e)
        selectedHistoricalGoods.value = []
      }
    }

    // 主推商品现在是富文本内容，直接使用
  }
})
</script>

<style lang="scss" scoped>
.supplier-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

.action-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;

  i {
    font-size: 20px;
    color: #3b82f6;
  }

  span {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}

.section-content {
  padding: 24px;
}

.elegant-form {
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-form-item__label {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }
  }
}

.elegant-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  :deep(.el-input__inner) {
    font-size: 14px;
    color: #1f2937;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.elegant-input-number {
  width: 100%;

  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    border-radius: 8px 0 0 8px;
    border-color: #d1d5db;
  }

  :deep(.el-input__wrapper) {
    border-radius: 0 8px 8px 0;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-select {
  width: 100%;

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-date-picker {
  width: 100%;

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.elegant-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #1f2937;

    &:hover {
      border-color: #3b82f6;
    }

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.elegant-radio-group {
  :deep(.el-radio) {
    margin-right: 24px;

    .el-radio__label {
      font-size: 14px;
      color: #374151;
    }

    .el-radio__input.is-checked .el-radio__inner {
      border-color: #3b82f6;
      background: #3b82f6;
    }
  }
}

.submit-action-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.elegant-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;

  i {
    font-size: 16px;
  }

  &.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }

  &.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;

    &:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #ffffff;

    &:hover {
      background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }

    &:disabled {
      background: #9ca3af;
      transform: none;
      box-shadow: none;
    }
  }
}

// 商品选择样式增强
.product-tag {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.border-green-200 {
  border-color: #bbf7d0;
}

// 响应式设计
@media (max-width: 768px) {
  .supplier-create-container {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
  }

  .content-wrapper {
    gap: 16px;
  }

  .section-header {
    padding: 16px 20px;

    span {
      font-size: 16px;
    }
  }

  .section-content {
    padding: 20px;
  }

  .submit-action-bar {
    flex-direction: column;
    gap: 12px;
    margin-top: 24px;
    padding: 20px;
  }

  .elegant-button {
    width: 100%;
  }

  // 移动端商品标签样式调整
  .product-tag-container {
    .bg-blue-50,
    .bg-green-50 {
      height: auto;
      min-height: 32px;
      padding: 4px 8px;

      span {
        font-size: 12px;
      }
    }
  }
}

// 富文本编辑器样式
.featured-products-editor {
  :deep(.w-e-text-container) {
    min-height: 300px;
  }

  :deep(.w-e-text-placeholder) {
    color: #9ca3af;
  }

  :deep(.w-e-toolbar) {
    border-bottom: 1px solid #e2e8f0;
  }

  :deep(.w-e-text) {
    border: 1px solid #e2e8f0;
    border-radius: 0 0 8px 8px;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}
</style>
