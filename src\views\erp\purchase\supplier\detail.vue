<!--
 * @Description: 供应商详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-10 10:40:28
 * @LastEditTime: 2025-07-08 15:00:24
-->
<template>
  <div class="supplier-detail-container">
    <div class="page-header">
      <el-button v-if="showBack" type="primary" class="back-button" @click="goBack">
        <el-icon><Back /></el-icon>
        返回
      </el-button>
      <h2 class="page-title">供应商详情</h2>
      <p class="page-subtitle">Supplier Details</p>
    </div>

    <div class="content-wrapper">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-office-building"></i>
          <span>基本信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="企业名称">
              {{ detailData.name }}
            </el-descriptions-item>
            <el-descriptions-item label="供应商等级">
              {{ detailData.supplierLevel }}
            </el-descriptions-item>
            <el-descriptions-item label="企业法人">
              {{ detailData.legalRepresentative }}
            </el-descriptions-item>
            <el-descriptions-item label="企业类型">
              <dict-tag :type="DICT_TYPE.COMPANY_TYPE" :value="detailData.companyType" />
            </el-descriptions-item>
            <el-descriptions-item label="注册资本">
              {{ detailData.registeredCapital }} 万元
            </el-descriptions-item>
            <el-descriptions-item label="企业编号">
              {{ detailData.companyCode }}
            </el-descriptions-item>
            <el-descriptions-item label="信用代码">
              {{ detailData.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="企业规模">
              <dict-tag :type="DICT_TYPE.COMPANY_SCALE" :value="detailData.companyScale" />
            </el-descriptions-item>
            <el-descriptions-item label="注册日期">
              {{ formatDate(detailData.registrationDate, 'YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item label="信用等级">
              {{ detailData.creditRating }}
            </el-descriptions-item>
            <el-descriptions-item label="社保员工人数">
              {{ detailData.employeeCount }}
            </el-descriptions-item>
            <el-descriptions-item label="年营业额">
              {{ detailData.annualTurnover }}
            </el-descriptions-item>
            <el-descriptions-item label="售后内容与响应时间">
              {{ detailData.afterSalesService }}
            </el-descriptions-item>
            <el-descriptions-item label="注册地址" :span="2">
              {{ detailData.registeredAddress }}
            </el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="2">
              {{ detailData.businessScope }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 资质文件 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-document"></i>
          <span>资质文件</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="行业资质">
              <FileListPreview :fileUrl="detailData.industryQualification" />
            </el-descriptions-item>
            <el-descriptions-item label="质量认证">
              <FileListPreview :fileUrl="detailData.qualityCertification" />
            </el-descriptions-item>
            <el-descriptions-item label="其他认证">
              <FileListPreview :fileUrl="detailData.otherCertifications" />
            </el-descriptions-item>
            <el-descriptions-item label="营业执照">
              <FileListPreview :fileUrl="detailData.businessLicense" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 联系人信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-user"></i>
          <span>联系人信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="联系人">
              {{ detailData.contactPerson }}
            </el-descriptions-item>
            <el-descriptions-item label="所属部门">
              {{ detailData.contactDept }}
            </el-descriptions-item>
            <el-descriptions-item label="电子邮件">
              {{ detailData.email }}
            </el-descriptions-item>
            <el-descriptions-item label="系统电话">
              {{ detailData.telephone }}
            </el-descriptions-item>
            <el-descriptions-item label="联系地址" :span="2">
              {{ detailData.address }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 负责人信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-s-custom"></i>
          <span>负责人信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="采购负责人">
              {{ detailData.purchaseOwner }}
            </el-descriptions-item>
            <el-descriptions-item label="财务负责人">
              {{ detailData.financeOwner }}
            </el-descriptions-item>
            <el-descriptions-item label="销售负责人">
              {{ detailData.saleOwner }}
            </el-descriptions-item>
            <el-descriptions-item label="售后负责人">
              {{ detailData.afterAles }}
            </el-descriptions-item>
            <el-descriptions-item label="产品负责人" :span="2">
              {{ detailData.productOwner }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 财务信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-money"></i>
          <span>财务信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="纳税人识别号">
              {{ detailData.taxNo }}
            </el-descriptions-item>
            <el-descriptions-item label="税率">
              {{ detailData.taxPercent }}
            </el-descriptions-item>
            <el-descriptions-item label="开户行">
              {{ detailData.bankName }}
            </el-descriptions-item>
            <el-descriptions-item label="开户账号">
              {{ detailData.bankAccount }}
            </el-descriptions-item>
            <el-descriptions-item label="开户地址" :span="2">
              {{ detailData.bankAddress }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 商品信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-goods"></i>
          <span>商品信息</span>
        </div>
        <div class="section-content">
          <!-- 历史商品 -->
          <div class="product-section">
            <div class="product-section-header">
              <div class="product-section-title">
                <div class="product-type-badge bg-blue-500">
                  <i class="el-icon-goods"></i>
                  历史商品
                </div>
                <span class="product-count-badge">{{ historicalGoods.length }} 个</span>
              </div>
            </div>
            
            <div v-if="historicalGoods.length > 0" class="product-table-container">
              <!-- 统计信息 -->
              <div class="product-stats">
                <div class="stat-item">
                  <span class="stat-label">总数量</span>
                  <span class="stat-value">{{ historicalGoods.length }}</span>
                </div>
                <div class="stat-item" v-if="historicalGoodsStats.avgPurchasePrice">
                  <span class="stat-label">平均采购价</span>
                  <span class="stat-value">¥{{ historicalGoodsStats.avgPurchasePrice }}</span>
                </div>
                <div class="stat-item" v-if="historicalGoodsStats.avgSalePrice">
                  <span class="stat-label">平均销售价</span>
                  <span class="stat-value">¥{{ historicalGoodsStats.avgSalePrice }}</span>
                </div>
                <div class="stat-item" v-if="historicalGoodsStats.totalValue">
                  <span class="stat-label">总价值</span>
                  <span class="stat-value">¥{{ historicalGoodsStats.totalValue }}</span>
                </div>
              </div>
              
              <el-table 
                :data="historicalGoods" 
                stripe 
                border
                :show-overflow-tooltip="true"
                class="product-table"
                size="small"
              >
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="商品名称" prop="name" min-width="150" show-overflow-tooltip />
                <el-table-column label="规格型号" prop="standard" min-width="120" show-overflow-tooltip />
                <el-table-column label="分类" prop="categoryName" width="100" align="center" />
                <el-table-column label="单位" prop="unitName" width="80" align="center" />
                <el-table-column 
                  label="采购价格" 
                  prop="purchasePrice" 
                  width="100" 
                  align="right"
                  :formatter="erpPriceTableColumnFormatter" 
                />
                <el-table-column 
                  label="销售价格" 
                  prop="salePrice" 
                  width="100" 
                  align="right"
                  :formatter="erpPriceTableColumnFormatter" 
                />
                <el-table-column label="状态" width="80" align="center">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="no-data-container">
              <el-empty description="暂无历史商品" :image-size="100" />
            </div>
          </div>

          <!-- 主推商品 -->
          <div class="product-section">
            <div class="product-section-header">
              <div class="product-section-title">
                <div class="product-type-badge bg-green-500">
                  <i class="el-icon-star-on"></i>
                  主推商品
                </div>
              </div>
            </div>
            
            <div class="featured-products-content">
              <div v-if="detailData.featuredProducts && detailData.featuredProducts.trim()" class="rich-text-content">
                <div class="rich-text-viewer" v-html="detailData.featuredProducts"></div>
              </div>
              <div v-else class="no-data-container">
                <el-empty description="暂无主推商品信息" :image-size="100" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-info"></i>
          <span>其他信息</span>
        </div>
        <div class="section-content">
          <el-descriptions :column="2" border class="elegant-descriptions">
            <el-descriptions-item label="开启状态">
              <el-tag v-if="detailData.status === 1" type="success" class="elegant-tag">开启</el-tag>
              <el-tag v-else-if="detailData.status === 2" type="danger" class="elegant-tag">关闭</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ detailData.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { SupplierApi } from '@/api/erp/supplier'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { erpPriceTableColumnFormatter } from '@/utils'

defineOptions({ name: 'SupplierDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})

const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

// 商品相关数据  
interface ProductItem {
  purchasePrice: number
  salePrice: number
  [key: string]: any
}

const historicalGoods = ref<ProductItem[]>([]) // 历史商品列表

// 历史商品统计信息
const historicalGoodsStats = computed(() => {
  if (historicalGoods.value.length === 0) {
    return {}
  }
  
  const validPurchasePrices = historicalGoods.value
    .map(item => parseFloat(item.purchasePrice))
    .filter(price => !isNaN(price) && price > 0)
  
  const validSalePrices = historicalGoods.value
    .map(item => parseFloat(item.salePrice))
    .filter(price => !isNaN(price) && price > 0)
  
  const avgPurchasePrice = validPurchasePrices.length > 0 
    ? (validPurchasePrices.reduce((sum, price) => sum + price, 0) / validPurchasePrices.length).toFixed(2)
    : null
  
  const avgSalePrice = validSalePrices.length > 0 
    ? (validSalePrices.reduce((sum, price) => sum + price, 0) / validSalePrices.length).toFixed(2)
    : null
  
  const totalValue = validSalePrices.length > 0 
    ? validSalePrices.reduce((sum, price) => sum + price, 0).toFixed(2)
    : null
  
  return {
    avgPurchasePrice,
    avgSalePrice,
    totalValue
  }
})

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await SupplierApi.getSupplier(props.id || queryId)
    
    // 解析历史商品数据
    if (detailData.value.historicalGoods) {
      try {
        historicalGoods.value = JSON.parse(detailData.value.historicalGoods)
      } catch (e) {
        console.error('解析历史商品数据失败:', e)
        historicalGoods.value = []
      }
    } else {
      historicalGoods.value = []
    }
    
    // 主推商品现在是富文本内容，直接使用
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/erp/purchase/supplier')
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
.supplier-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  position: relative;
  
  .back-button {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: #ffffff;
    font-weight: 500;
    transition: all 0.2s ease;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-50%) translateX(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .el-icon {
      margin-right: 6px;
    }
  }
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
  }
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  
  i {
    font-size: 20px;
    color: #3b82f6;
  }
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}

.section-content {
  padding: 24px;
}

.elegant-descriptions {
  :deep(.el-descriptions__header) {
    margin-bottom: 0;
  }
  
  :deep(.el-descriptions__body) {
    .el-descriptions__table {
      border-radius: 8px;
      overflow: hidden;
      
      .el-descriptions__cell {
        padding: 16px 20px;
        font-size: 14px;
        border-color: #e2e8f0;
        
        &.el-descriptions__label {
          background: #f8fafc;
          font-weight: 600;
          color: #374151;
          width: 180px;
        }
        
        &.el-descriptions__content {
          background: #ffffff;
          color: #1f2937;
        }
      }
    }
  }
}

.elegant-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
  font-size: 12px;
}

// 商品信息表格样式
.product-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .product-section-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    
    .product-section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .product-type-badge {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 20px;
        color: white;
        font-weight: 600;
        font-size: 14px;
        
        i {
          font-size: 16px;
        }
      }
      
      .product-count-badge {
        background: #e2e8f0;
        color: #64748b;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .product-table-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .product-stats {
      display: flex;
      justify-content: space-around;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      padding: 16px;
      border-bottom: 1px solid #e2e8f0;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          display: block;
          font-size: 12px;
          color: #64748b;
          margin-bottom: 4px;
          font-weight: 500;
        }
        
        .stat-value {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }
      }
    }
    
    .product-table {
      :deep(.el-table__header) {
        background: #f8fafc;
        
        th {
          background: #f8fafc !important;
          color: #374151;
          font-weight: 600;
          border-color: #e2e8f0;
        }
      }
      
      :deep(.el-table__body) {
        tr {
          &:hover {
            background: #f8fafc;
          }
          
          td {
            border-color: #e2e8f0;
          }
        }
      }
      
      :deep(.el-table__border) {
        border-color: #e2e8f0;
      }
    }
  }
  
  .no-data-container {
    padding: 40px 20px;
    text-align: center;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px dashed #cbd5e1;
  }
}

// 商品类型徽章颜色
.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-500 {
  background-color: #10b981;
}

// 富文本内容展示样式
.featured-products-content {
  .rich-text-content {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    
    .rich-text-viewer {
      line-height: 1.6;
      color: #1f2937;
      font-size: 14px;
      
      // 富文本内容样式
      :deep(h1) {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 16px 0;
        color: #1e293b;
      }
      
      :deep(h2) {
        font-size: 20px;
        font-weight: 600;
        margin: 16px 0 12px 0;
        color: #1e293b;
      }
      
      :deep(h3) {
        font-size: 18px;
        font-weight: 600;
        margin: 12px 0 8px 0;
        color: #1e293b;
      }
      
      :deep(h4, h5, h6) {
        font-size: 16px;
        font-weight: 600;
        margin: 8px 0 4px 0;
        color: #1e293b;
      }
      
      :deep(p) {
        margin: 0 0 12px 0;
        color: #374151;
      }
      
      :deep(ul, ol) {
        margin: 0 0 12px 0;
        padding-left: 24px;
        
        li {
          margin: 4px 0;
          color: #374151;
        }
      }
      
      :deep(blockquote) {
        margin: 12px 0;
        padding: 12px 16px;
        background: #f8fafc;
        border-left: 4px solid #3b82f6;
        border-radius: 0 8px 8px 0;
        color: #64748b;
      }
      
      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 12px 0;
        
        th, td {
          border: 1px solid #e2e8f0;
          padding: 8px 12px;
          text-align: left;
        }
        
        th {
          background: #f8fafc;
          font-weight: 600;
          color: #374151;
        }
        
        td {
          color: #1f2937;
        }
      }
      
      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 12px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      :deep(a) {
        color: #3b82f6;
        text-decoration: none;
        
        &:hover {
          color: #2563eb;
          text-decoration: underline;
        }
      }
      
      :deep(strong) {
        font-weight: 600;
        color: #1e293b;
      }
      
      :deep(em) {
        font-style: italic;
        color: #64748b;
      }
      
      :deep(code) {
        background: #f1f5f9;
        color: #e11d48;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
      }
      
      :deep(pre) {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
        overflow-x: auto;
        margin: 12px 0;
        
        code {
          background: none;
          color: #1f2937;
          padding: 0;
          font-size: 14px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .supplier-detail-container {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 20px;
    
    .back-button {
      position: static;
      transform: none;
      margin-bottom: 16px;
      
      &:hover {
        transform: none;
      }
    }
    
    .page-title {
      font-size: 24px;
    }
    
    .page-subtitle {
      font-size: 14px;
    }
  }
  
  .content-wrapper {
    gap: 16px;
  }
  
  .section-header {
    padding: 16px 20px;
    
    span {
      font-size: 16px;
    }
  }
  
  .section-content {
    padding: 20px;
  }
  
  .elegant-descriptions {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 12px 16px;
          
          &.el-descriptions__label {
            width: 120px;
          }
        }
      }
    }
  }
  
  // 移动端商品表格样式调整
  .product-section {
    margin-bottom: 24px;
    
    .product-section-header {
      padding: 12px 16px;
      
      .product-section-title {
        .product-type-badge {
          padding: 6px 12px;
          font-size: 13px;
          
          i {
            font-size: 14px;
          }
        }
        
        .product-count-badge {
          padding: 3px 8px;
          font-size: 11px;
        }
      }
    }
    
    .product-table-container {
      .product-stats {
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px;
        
        .stat-item {
          flex: 1;
          min-width: calc(50% - 4px);
          
          .stat-label {
            font-size: 11px;
            margin-bottom: 2px;
          }
          
          .stat-value {
            font-size: 14px;
          }
        }
      }
      
      .product-table {
        font-size: 12px;
        
        :deep(.el-table__header) {
          th {
            padding: 8px 4px;
            font-size: 12px;
          }
        }
        
        :deep(.el-table__body) {
          tr {
            td {
              padding: 8px 4px;
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .no-data-container {
      padding: 20px 16px;
    }
  }
  
  // 移动端富文本样式调整
  .featured-products-content {
    .rich-text-content {
      padding: 16px;
      
      .rich-text-viewer {
        font-size: 13px;
        
        :deep(h1) {
          font-size: 20px;
        }
        
        :deep(h2) {
          font-size: 18px;
        }
        
        :deep(h3) {
          font-size: 16px;
        }
        
        :deep(table) {
          font-size: 12px;
          
          th, td {
            padding: 6px 8px;
          }
        }
      }
    }
  }
}
</style>
