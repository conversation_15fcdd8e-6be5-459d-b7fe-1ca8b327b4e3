<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="88px">
      <el-form-item label="企业名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商等级" prop="supplierLevel">
        <el-input
          v-model="queryParams.supplierLevel"
          placeholder="请输入供应商等级"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb4">
      <el-button
        type="primary"
        plain
        @click="openForm('create')"
        v-hasPermi="['erp:supplier:create']"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['erp:supplier:export']"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="供应商编号" align="center" prop="id" /> -->
      <el-table-column label="企业名称" align="center" prop="name" min-width="180">
        <template #default="scope">
          <el-link
            v-if="scope.row.name"
            type="primary"
            :underline="false"
            @click="handleSupplierNameClick(scope.row)"
          >
            {{ scope.row.name }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="企业法人" align="center" prop="legalRepresentative" />
      <el-table-column label="注册资金" align="center" prop="registeredCapital" />
      <el-table-column label="企业类型" align="center" prop="companyType" width="100" />
      <el-table-column label="企业规模" align="center" prop="companyScale" />
      <el-table-column label="年营业额" align="center" prop="annualTurnover" width="100" />
      <!-- <el-table-column label="供应商等级" align="center" prop="supplierLevel" /> -->
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="telephone" />
      <el-table-column label="开启状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.status === 0">开启</el-tag>
          <el-tag type="danger" v-else-if="scope.row.status === 1">关闭</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="250" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button
            link
            type="success"
            @click="handleSubmit(scope.row)"
            v-if="!scope.row.result || scope.row.result === -1"
          >
            提交
          </el-button>
          <el-button link type="primary" @click="handleEdit(scope.row)" v-if="onlySeeSelf">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SupplierForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { SupplierApi, SupplierVO } from '@/api/erp/supplier'
import SupplierForm from './SupplierForm.vue'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { useUserStore } from '@/store/modules/user'

/** ERP 供应商主表 列表 */
defineOptions({ name: 'ErpSupplier' })
const userStore = useUserStore()

const roles = userStore.getRoles // 当前登录人角色

const onlySeeSelf = computed(() => {
  return roles.includes('super_admin') || roles.includes('CG01')
})

let currentSupplierType = ref()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<SupplierVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
let queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined as string | undefined,
  supplierLevel: undefined as string | undefined,
  creditCode: undefined as string | undefined,
  otherCertifications: undefined as string | undefined,
  supplierType: undefined as number | undefined,
  createTime: [] as any[]
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SupplierApi.getSupplierPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    supplierLevel: undefined,
    creditCode: undefined,
    otherCertifications: undefined,
    supplierType: currentSupplierType.value,
    createTime: []
  }
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  router.push({ name: 'SupplierCreate' })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SupplierApi.deleteSupplier(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SupplierApi.exportSupplier(queryParams)
    download.excel(data, 'ERP 供应商主表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
/** 编辑操作 */
const handleEdit = (row: any) => {
  router.push({
    name: 'SupplierCreate',
    query: {
      id: row.id,
      type: 'edit'
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'SupplierDetail',
    query: {
      id: row.id
    }
  })
}

/** 点击供应商名称跳转到详情页面 */
const handleSupplierNameClick = (row: any) => {
  router.push({
    name: 'SupplierDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消操作 */
const cancelLeave = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}
/** 审批进度 */
const handleProcessDetail = (row: any) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const reissue = (row: any) => {
  router.push({
    name: 'SupplierCreate',
    query: {
      id: row.id
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  switch (route.name) {
    case 'ErpSupplier1':
      queryParams.supplierType = 1
      currentSupplierType.value = 1
      break

    case 'ErpSupplier2':
      queryParams.supplierType = 2
      currentSupplierType.value = 2
      break

    case 'ErpSupplier3':
      queryParams.supplierType = 3
      currentSupplierType.value = 3
      break

    default:
      break
  }
  await getList()
})

/** 提交操作 */
const handleSubmit = async (row: any) => {
  try {
    // 提交的二次确认
    await message.confirm('确认要提交该供应商信息吗？')
    // 发起提交
    await SupplierApi.updateSupplier({
      id: row.id,
      name: row.name,
      status: row.status,
      sort: row.sort
    })
    message.success('提交成功')
    // 刷新列表
    await getList()
  } catch {}
}
</script>
