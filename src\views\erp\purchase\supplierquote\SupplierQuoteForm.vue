<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="供应商" prop="supplierId">
        <el-input v-model="formData.supplierName" disabled class="!w-440px" />
        <el-button type="primary" class="ml-2" @click="openSupplierDialog">选择供应商</el-button>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="formData.productName" disabled class="!w-440px" />
        <el-button type="primary" class="ml-2" @click="openProductDialog">选择产品</el-button>
      </el-form-item>
      <!-- <el-form-item label="产品名称" prop="productName">
        <el-input v-model="formData.productName" disabled />
      </el-form-item> -->
      <el-form-item label="报价数量" prop="quantity">
        <el-input v-model="formData.quantity" placeholder="请输入报价数量" />
      </el-form-item>
      <el-form-item label="报价单价" prop="unitPrice">
        <el-input v-model="formData.unitPrice" placeholder="请输入报价单价" />
      </el-form-item>
      <el-form-item label="报价总价" prop="totalPrice">
        <el-input v-model="formData.totalPrice" placeholder="请输入报价总价" />
      </el-form-item>
      <el-form-item label="货币类型" prop="currency">
        <el-select v-model="formData.currency" placeholder="请输入货币类型" class="w-1/1">
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.CURRENCY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报价日期" prop="quotationDate">
        <el-date-picker
          class="!w-full"
          v-model="formData.quotationDate"
          type="date"
          value-format="x"
          placeholder="选择报价日期"
        />
      </el-form-item>
      <el-form-item label="供货周期" prop="leadTime">
        <el-input-number
          class="!w-full"
          v-model="formData.leadTime"
          placeholder="请输入供货周期(天)"
        />
      </el-form-item>
      <el-form-item label="有效期至" prop="validUntil">
        <el-date-picker
          class="!w-full"
          v-model="formData.validUntil"
          type="date"
          value-format="x"
          placeholder="选择有效期至"
        />
      </el-form-item>
      <el-form-item label="购买期限" prop="duration">
        <el-input v-model="formData.duration" placeholder="请输入购买期限" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="附件" prop="attachment">
        <UploadFile
          v-model="formData.attachment"
          :file-size="5120"
          :limit="1"
          @uploading="handleUploading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading || isUploading"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 供应商选择弹窗 -->
  <el-dialog v-model="supplierDialogVisible" title="选择供应商" width="1000px" append-to-body>
    <el-form :model="supplierQuery" inline>
      <el-form-item label="企业名称">
        <el-input
          v-model="supplierQuery.name"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="getSupplierList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSupplierList">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetSupplierQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="supplierLoading"
      :data="supplierList"
      @row-click="handleSupplierRowClick"
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="供应商编号" prop="id" width="100" />
      <el-table-column label="企业名称" prop="name" min-width="180" />
      <el-table-column label="企业法人" prop="legalRepresentative" />
      <el-table-column label="联系人" prop="contactPerson" />
      <el-table-column label="联系电话" prop="telephone" />
    </el-table>
    <Pagination
      :total="supplierTotal"
      v-model:page="supplierQuery.pageNo"
      v-model:limit="supplierQuery.pageSize"
      @pagination="getSupplierList"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="confirmSupplier">确 定</el-button>
        <el-button @click="supplierDialogVisible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 产品选择弹窗 -->
  <el-dialog v-model="productDialogVisible" title="选择产品" width="1000px" append-to-body>
    <el-form :model="productQuery" inline>
      <el-form-item label="产品名称">
        <el-input
          v-model="productQuery.name"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="getProductList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getProductList">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetProductQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="productLoading"
      :data="productList"
      @row-click="handleProductRowClick"
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="产品编号" prop="id" width="100" />
      <el-table-column label="产品名称" prop="name" min-width="180" />
      <el-table-column label="规格型号" prop="standard" />
      <el-table-column label="分类" prop="categoryName" />
      <el-table-column label="单位" prop="unitName" />
    </el-table>
    <Pagination
      :total="productTotal"
      v-model:page="productQuery.pageNo"
      v-model:limit="productQuery.pageSize"
      @pagination="getProductList"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="confirmProduct">确 定</el-button>
        <el-button @click="productDialogVisible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { SupplierQuoteApi, SupplierQuoteVO } from '@/api/erp/purchase/supplierquote'
import { SupplierApi } from '@/api/erp/purchase/supplier'
import { ProductApi } from '@/api/erp/product/product'
import { DICT_TYPE, getIntDictOptions, getDictOptions } from '@/utils/dict'

/** ERP 供应商报价 表单 */
defineOptions({ name: 'SupplierQuoteForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题

const supplierDialogVisible = ref(false)
const supplierLoading = ref(false)
const supplierList = ref([])
const supplierTotal = ref(0)
const supplierQuery = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined
})
const selectedSupplier = ref<any>(null)

const productDialogVisible = ref(false)
const productLoading = ref(false)
const productList = ref([])
const productTotal = ref(0)
const productQuery = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined
})
const selectedProduct = ref<any>(null)

const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  supplierId: undefined,
  supplierName: undefined,
  productId: undefined,
  productName: undefined,
  quantity: undefined,
  unitPrice: undefined,
  totalPrice: undefined,
  currency: undefined,
  quotationDate: undefined,
  validUntil: undefined,
  duration: undefined,
  remark: undefined
})
const formRules = reactive({
  supplierId: [{ required: true, message: '供应商编号不能为空', trigger: 'blur' }],
  supplierName: [{ required: true, message: '供应商名称不能为空', trigger: 'blur' }],
  productId: [{ required: true, message: '产品编号不能为空', trigger: 'blur' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '报价数量不能为空', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '报价单价不能为空', trigger: 'blur' }],
  totalPrice: [{ required: true, message: '报价总价不能为空', trigger: 'blur' }],
  currency: [{ required: true, message: '货币类型不能为空', trigger: 'blur' }],
  quotationDate: [{ required: true, message: '报价日期不能为空', trigger: 'blur' }],
  validUntil: [{ required: true, message: '有效期至不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupplierQuoteApi.getSupplierQuote(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SupplierQuoteVO
    if (formType.value === 'create') {
      await SupplierQuoteApi.createSupplierQuote(data)
      message.success(t('common.createSuccess'))
    } else {
      await SupplierQuoteApi.updateSupplierQuote(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const isUploading = ref(false) // 上传状态，初始为 false

// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

/** 打开供应商选择弹窗 */
const openSupplierDialog = () => {
  supplierDialogVisible.value = true
  getSupplierList()
}

/** 获取供应商列表 */
const getSupplierList = async () => {
  supplierLoading.value = true
  try {
    const data = await SupplierApi.getSupplierPage(supplierQuery)
    supplierList.value = data.list
    supplierTotal.value = data.total
  } finally {
    supplierLoading.value = false
  }
}

/** 重置供应商查询条件 */
const resetSupplierQuery = () => {
  supplierQuery.name = undefined
  supplierQuery.pageNo = 1
  getSupplierList()
}

/** 处理供应商行点击 */
const handleSupplierRowClick = (row) => {
  selectedSupplier.value = row
}

/** 确认选择供应商 */
const confirmSupplier = () => {
  if (!selectedSupplier.value) {
    message.warning('请选择供应商')
    return
  }
  formData.value.supplierId = selectedSupplier.value.id
  formData.value.supplierName = selectedSupplier.value.name
  supplierDialogVisible.value = false
}

/** 打开产品选择弹窗 */
const openProductDialog = () => {
  productDialogVisible.value = true
  getProductList()
}

/** 获取产品列表 */
const getProductList = async () => {
  productLoading.value = true
  try {
    const data = await ProductApi.getProductPage(productQuery)
    productList.value = data.list
    productTotal.value = data.total
  } finally {
    productLoading.value = false
  }
}

/** 重置产品查询条件 */
const resetProductQuery = () => {
  productQuery.name = undefined
  productQuery.pageNo = 1
  getProductList()
}

/** 处理产品行点击 */
const handleProductRowClick = (row) => {
  selectedProduct.value = row
}

/** 确认选择产品 */
const confirmProduct = () => {
  if (!selectedProduct.value) {
    message.warning('请选择产品')
    return
  }
  formData.value.productId = selectedProduct.value.id
  formData.value.productName = selectedProduct.value.name
  productDialogVisible.value = false
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    supplierId: undefined,
    supplierName: undefined,
    productId: undefined,
    productName: undefined,
    quantity: undefined,
    unitPrice: undefined,
    totalPrice: undefined,
    currency: undefined,
    quotationDate: undefined,
    validUntil: undefined,
    duration: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
