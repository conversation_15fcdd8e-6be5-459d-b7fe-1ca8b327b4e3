<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="供应商">
        {{ detailData.supplierName }}
      </el-descriptions-item>
      <el-descriptions-item label="产品名称">
        {{ detailData.productName }}
      </el-descriptions-item>
      <el-descriptions-item label="报价数量">
        {{ detailData.quantity }}
      </el-descriptions-item>
      <el-descriptions-item label="报价单价">
        {{ detailData.unitPrice }}
      </el-descriptions-item>
      <el-descriptions-item label="报价总价">
        {{ detailData.totalPrice }}
      </el-descriptions-item>
      <el-descriptions-item label="货币类型">
        <dict-tag :type="DICT_TYPE.CURRENCY" :value="detailData.currency" />
      </el-descriptions-item>
      <el-descriptions-item label="报价日期">
        {{ formatDate(detailData.quotationDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="供货周期">
        {{ detailData.leadTime }}
      </el-descriptions-item>
      <el-descriptions-item label="有效期至">
        {{ formatDate(detailData.validUntil, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="购买期限">
        {{ detailData.duration }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="附件">
        <FileListPreview :fileUrl="detailData.attachment" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate, dateFormatter } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { SupplierQuoteApi, SupplierQuoteVO } from '@/api/erp/purchase/supplierquote'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ErpPurchaseSupplierquoteDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await SupplierQuoteApi.getSupplierQuote(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}
/** 初始化 **/
onMounted(() => {
  getInfo()
})
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
