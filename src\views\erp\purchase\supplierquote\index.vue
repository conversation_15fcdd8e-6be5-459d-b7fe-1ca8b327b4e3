<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="供应商编号" align="center" prop="supplierId" /> -->
      <el-table-column label="供应商名称" align="center" prop="supplierName" />
      <el-table-column label="产品编号" align="center" prop="productId" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="报价数量" align="center" prop="quantity" />
      <el-table-column label="报价单价" align="center" prop="unitPrice" />
      <el-table-column label="报价总价" align="center" prop="totalPrice" />
      <el-table-column label="货币类型" align="center" prop="currency">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.CURRENCY, scope.row.currency) }}
        </template>
      </el-table-column>
      <el-table-column
        label="报价日期"
        align="center"
        prop="quotationDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="有效期至"
        align="center"
        prop="validUntil"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="附件" align="center" prop="attachment">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="购买期限" align="center" prop="duration" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="200px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleEdit(scope.row)"> 详情 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SupplierQuoteForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { SupplierQuoteApi, SupplierQuoteVO } from '@/api/erp/purchase/supplierquote'
import SupplierQuoteForm from './SupplierQuoteForm.vue'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'

/** ERP 供应商报价 列表 */
defineOptions({ name: 'SupplierQuote' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const router = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<SupplierQuoteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  supplierName: undefined,
  productName: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SupplierQuoteApi.getSupplierQuotePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const handleEdit = (row) => {
  router.push({
    name: 'ErpPurchaseSupplierquoteDetail',
    query: {
      id: row.id
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SupplierQuoteApi.deleteSupplierQuote(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SupplierQuoteApi.exportSupplierQuote(queryParams)
    download.excel(data, 'ERP 供应商报价.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
