<!--
 * @Description: 申请新增页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-26 16:10:20
 * @LastEditTime: 2024-08-12 16:49:46
-->

<template>
  <div class="simple-form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
      v-loading="formLoading"
      class="simple-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请类型" prop="applyType">
            <el-select
              class="simple-select"
              v-model="formData.applyType"
              placeholder="请选择申请类型"
              clearable
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.OUT_BUSINESS_APPLY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="outStartDate">
            <el-date-picker
              class="simple-date-picker"
              v-model="formData.outStartDate"
              type="date"
              value-format="x"
              placeholder="选择开始日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="outEndDate">
            <el-date-picker
              class="simple-date-picker"
              v-model="formData.outEndDate"
              type="date"
              value-format="x"
              placeholder="选择结束日期"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="省市与地区" prop="outBusinessAddr">
            <el-input 
              v-model="formData.outBusinessAddr" 
              placeholder="请输入省市与地区"
              class="simple-input"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请借款（元）" prop="applyLoan">
            <el-input 
              v-model="formData.applyLoan" 
              placeholder="请输入申请借款金额"
              class="simple-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="申请事项" prop="vehicle">
            <el-checkbox-group v-model="formData.vehicle" class="simple-checkbox-group">
              <el-checkbox
                v-for="(item, index) in getIntDictOptions(DICT_TYPE.INTER_CITY_TRAVEL_MODE)"
                :key="index"
                :label="item.value"
                class="simple-checkbox"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属项目" prop="projectId">
            <el-select
              class="simple-select"
              ref="projectRef"
              filterable
              v-model="formData.projectId"
              placeholder="请选择所属项目"
              @change="projectChange"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="`【${item.contractCode}】${item.customerName}`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请原因" prop="reasonOutBusiness">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              v-model="formData.reasonOutBusiness"
              placeholder="请详细描述申请原因"
              class="simple-textarea"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              v-model="formData.remark"
              placeholder="请输入备注信息"
              class="simple-textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="相关文档" prop="fileUrl">
            <div class="upload-section">
              <UploadFile
                v-model="formData.fileUrl"
                :file-size="5120"
                :limit="5"
                @uploading="handleUploading"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-form-item class="form-actions">
          <el-button @click="submitForm" type="primary" :disabled="formLoading || isUploading" class="submit-btn">
            确定
          </el-button>
          <el-button @click="handleClose" type="warning" :disabled="formLoading" class="cancel-btn">
            关闭
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { OutBusinessApplyApi } from '@/api/finance/businessTripApply'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isArray, isString } from '@/utils/is'
import { cloneDeep } from 'lodash-es'
import { getMyProjectPage } from '@/api/bpm/task'

defineOptions({ name: 'BusinessTripApplyCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  userId: undefined,
  userName: undefined,
  applyCode: undefined,
  applyDate: undefined,
  outStartDate: undefined,
  outEndDate: undefined,
  outBusinessAddr: undefined,
  vehicle: undefined,
  applyLoan: undefined,
  projectId: undefined,
  projectName: undefined,
  describeVal: undefined,
  reasonOutBusiness: undefined,
  remark: undefined,
  fileUrl: undefined,
  result: undefined,
  processInstanceId: undefined,
  applyType: undefined
})
// const formRules = reactive({
//   applyType: [{ required: true, message: '申请类型不能为空', trigger: 'blur' }],
//   outStartDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
//   outEndDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
//   outBusinessAddr: [{ required: true, message: '省市与地区不能为空', trigger: 'blur' }],
//   vehicle: [{ required: true, message: '申请事项不能为空', trigger: 'blur' }],
//   projectId: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }],
//   reasonOutBusiness: [{ required: true, message: '申请原因不能为空', trigger: 'blur' }]
// })

// 动态生成表单验证规则
const formRules = computed(() => ({
  applyType: [{ required: true, message: '申请类型不能为空', trigger: 'blur' }],
  outStartDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  outEndDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
  outBusinessAddr: [
    {
      required: formData.value.applyType !== 0, // 根据 applyType 动态设置是否必填
      message: '省市与地区不能为空',
      trigger: 'blur'
    }
  ],
  vehicle: [{ required: true, message: '申请事项不能为空', trigger: 'blur' }],
  projectId: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }],
  reasonOutBusiness: [{ required: true, message: '申请原因不能为空', trigger: 'blur' }]
}))

const formRef = ref() // 表单 Ref
const projectRef = ref()
const userId = useUserStore().getUser.id // 当前登录的编号

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ...cloneDeep(formData.value)
    }
    if (isArray(data.vehicle)) {
      data.vehicle = data.vehicle.join()
    }
    delete data.id
    await OutBusinessApplyApi.createOutBusinessApply(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/finance/businessTripApply')
}

const projectList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await getMyProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2,
      userId
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}

const projectChange = (val: any) => {
  const selectedItem = projectList.value.find((item) => item.id === val)
  formData.value.projectName = selectedItem.projectName
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

onMounted(async () => {
  getProjectList()
  if (route.query.id) {
    formData.value = await OutBusinessApplyApi.getOutBusinessApply(route.query.id as any)
    if (formData.value.vehicle && isString(formData.value.vehicle)) {
      formData.value.vehicle = formData.value.vehicle.split(',').map(Number)
    }
  }
})
</script>

<style lang="scss" scoped>
.simple-form-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  
  .simple-form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin: 0 auto;
    
    :deep(.el-form-item) {
      margin-bottom: 20px;
      
      .el-form-item__label {
        font-weight: 500;
        color: #333;
      }
    }
    
    .simple-select,
    .simple-input,
    .simple-date-picker {
      width: 100%;
      
      :deep(.el-input__wrapper) {
        border: 1px solid #ddd;
        border-radius: 4px;
        
        &:hover {
          border-color: #409eff;
        }
        
        &.is-focus {
          border-color: #409eff;
        }
      }
    }
    
    .simple-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      
      .simple-checkbox {
        margin-right: 0;
      }
    }
    
    .simple-textarea {
      :deep(.el-textarea__inner) {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 12px;
        
        &:hover {
          border-color: #409eff;
        }
        
        &:focus {
          border-color: #409eff;
        }
      }
    }
    
    .upload-section {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background: #fafafa;
    }
    
    .form-actions {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      
      .submit-btn,
      .cancel-btn {
        margin: 0 10px;
        padding: 8px 20px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .simple-form-container {
    padding: 16px;
    
    .simple-form {
      .form-actions {
        flex-direction: column;
        gap: 12px;
        
        .submit-btn,
        .cancel-btn {
          width: 100%;
        }
      }
    }
  }
}
</style>
