<!--
 * @Description: 申请详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-26 16:10:20
 * @LastEditTime: 2024-07-10 15:32:35
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="申请单编号">
        {{ detailData.applyCode }}
      </el-descriptions-item>
      <el-descriptions-item label="客户名">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="合同号">
        {{ detailData.contractCode }}
      </el-descriptions-item>
      <el-descriptions-item label="申请类型">
        <dict-tag :type="DICT_TYPE.OUT_BUSINESS_APPLY_TYPE" :value="detailData.applyType" />
      </el-descriptions-item>
      <el-descriptions-item label="申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="申请日期">
        {{ formatDate(detailData.applyDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="开始日期">
        {{ formatDate(detailData.outStartDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="结束日期">
        {{ formatDate(detailData.outEndDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="省市与地区">
        {{ detailData.outBusinessAddr }}
      </el-descriptions-item>
      <el-descriptions-item label="申请事项">
        <dict-tag :type="DICT_TYPE.INTER_CITY_TRAVEL_MODE" :value="detailData.vehicle" />
      </el-descriptions-item>
      <el-descriptions-item label="申请借款（元）">
        {{ detailData.applyLoan }}
      </el-descriptions-item>
      <el-descriptions-item label="所属项目">
        {{ detailData.projectName }}
      </el-descriptions-item>
      <el-descriptions-item label="申请原因">
        {{ detailData.reasonOutBusiness }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="文档">
        <FileListPreview :fileUrl="detailData.fileUrl" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate, dateFormatter2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { OutBusinessApplyApi } from '@/api/finance/businessTripApply'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'BusinessTripApplyDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await OutBusinessApplyApi.getOutBusinessApply(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/finance/businessTripApply')
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
