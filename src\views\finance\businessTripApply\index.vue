<!--
 * @Description: 申请
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-26 16:10:20
 * @LastEditTime: 2025-01-07 14:04:34
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="申请单编号" prop="applyCode">
        <el-input
          v-model="queryParams.applyCode"
          placeholder="请输入申请单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="userId">
        <el-select
          class="!w-240px"
          v-model="queryParams.userId"
          filterable
          placeholder="请选择申请人"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb8">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleCreate()">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="申请单编号" align="center" prop="applyCode" width="150" />
      <el-table-column label="客户名" align="center" prop="customerName" min-width="150" />
      <el-table-column label="合同号" align="center" prop="contractCode" width="150" />
      <el-table-column label="申请类型" align="center" prop="applyType" width="130">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OUT_BUSINESS_APPLY_TYPE" :value="scope.row.applyType" />
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="userName" width="120" />
      <el-table-column
        label="申请日期"
        align="center"
        prop="applyDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column
        label="开始日期"
        align="center"
        prop="outStartDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="结束日期"
        align="center"
        prop="outEndDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <!-- <el-table-column label="省市与地区" align="center" prop="outBusinessAddr" width="200" />
      <el-table-column label="申请事项" align="center" prop="vehicle" width="170">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INTER_CITY_TRAVEL_MODE" :value="scope.row.vehicle" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="申请借款（元）" align="center" prop="applyLoan" width="150" /> -->
      <el-table-column label="项目名称" align="center" prop="projectName" width="240" />
      <!-- <el-table-column label="申请原因" align="center" prop="reasonOutBusiness" width="200" /> -->
      <!-- <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      /> -->
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="handleReimbursement(scope.row)"
            v-if="scope.row.result === 2"
          >
            发起报销
          </el-button>
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { OutBusinessApplyApi } from '@/api/finance/businessTripApply'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'BusinessTripApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const router = useRouter() // 路由
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  userName: undefined,
  applyCode: undefined,
  applyDate: [],
  outStartDate: [],
  outEndDate: [],
  outBusinessAddr: undefined,
  vehicle: undefined,
  applyLoan: undefined,
  projectId: undefined,
  projectName: undefined,
  describeVal: undefined,
  reasonOutBusiness: undefined,
  remark: undefined,
  fileUrl: undefined,
  result: undefined,
  processInstanceId: undefined,
  createTime: [],
  applyType: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OutBusinessApplyApi.getOutBusinessApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'BusinessTripApplyCreate' })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'BusinessTripApplyDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const reissue = (row) => {
  router.push({
    name: 'BusinessTripApplyCreate',
    query: {
      id: row.id
    }
  })
}
const handleReimbursement = (row) => {
  router.push({
    name: 'ReimbursementCreate',
    query: {
      row: JSON.stringify(row),
      type: 'INTER_CITY'
    }
  })
}

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
  userList.value = await UserApi.getSimpleUserList()
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
