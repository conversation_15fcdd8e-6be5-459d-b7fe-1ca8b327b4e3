<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="用户姓名" prop="userId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.userId"
          placeholder="请先选择用户姓名"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          class="!w-240px"
          clearable
          placeholder="请输入合同编号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          class="!w-240px"
          clearable
          placeholder="请输入客户名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="月份" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          value-format="YYYY-MM"
          @change="handleQuery"
          type="month"
          placeholder="请输入月份"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button :loading="exportLoading" plain type="success" @click="handleExport">
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="用户姓名" align="center" prop="userName" />
      <el-table-column label="合同编号" align="center" prop="contractCode" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="合同金额(元)" align="center" prop="contractAmount" />
      <el-table-column label="总人工天(小时)" align="center" prop="totalManHours" />
      <el-table-column label="合同单价(元/小时)" align="center" prop="contractPricePerHour" />
      <el-table-column label="咨询师月工时(小时)" align="center" prop="monthlyHours" />
      <el-table-column label="咨询师月产出(元)" align="center" prop="monthlyOutput" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { OutputApi } from '@/api/finance/output'
import dayjs from 'dayjs'
import * as UserApi from '@/api/system/user'
import download from '@/utils/download'

const userList = ref<UserApi.UserVO[]>([]) // 用户列表

defineOptions({ name: 'CrmProduct' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  yearMonth: dayjs().format('YYYY-MM'),
  userId: undefined,
  contractCode: undefined,
  customerName: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OutputApi.getOutputPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OutputApi.exportOutput(queryParams)
    download.excel(data, '月度产出.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.yearMonth = dayjs().format('YYYY-MM')
  handleQuery()
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
