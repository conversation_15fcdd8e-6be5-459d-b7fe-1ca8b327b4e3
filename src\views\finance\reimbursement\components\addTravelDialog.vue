<!--
 * @Description: 新增/修改行程单弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-04 11:30:22
 * @LastEditTime: 2024-07-12 17:21:08
-->

<template>
  <el-dialog
    title="行程单"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
    center
  >
    <div class="mb-12px flex">
      <el-button @click="handleTravelAdd" type="primary">添加行程</el-button>
      <div class="ml-15px text-red-500 mt-5px"
        >1.起点和终点必须有具体的路牌号； 2.公里数参照地图的公里数</div
      >
    </div>

    <el-table :data="travelTableData" border stripe>
      <el-table-column label="序号" type="index" width="80"></el-table-column>
      <el-table-column label="出发地">
        <template #default="scope">
          <el-input
            v-model="scope.row.startAddr"
            placeholder="请输入出发地"
            v-if="scope.row.isRowEdit"
          ></el-input>
          <span v-else>{{ scope.row.startAddr }}</span>
        </template>
      </el-table-column>
      <el-table-column label="目的地">
        <template #default="scope">
          <el-input
            v-model="scope.row.endAddr"
            placeholder="请输入目的地"
            v-if="scope.row.isRowEdit"
          ></el-input>
          <span v-else>{{ scope.row.endAddr }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事由">
        <template #default="scope">
          <!-- <el-input
            v-model="scope.row.reason"
            placeholder="请输入事由"
          ></el-input> -->
          <el-select
            v-if="scope.row.isRowEdit"
            v-model="scope.row.reason"
            placeholder="请选择事由"
            multiple
            clearable
          >
            <el-option
              v-for="dict in getDictOptions(DICT_TYPE.USE_REASON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <span v-else>{{ scope.row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公里数" width="150">
        <template #default="scope">
          <el-input-number
            class="!w-95%"
            :min="0"
            :max="9999"
            :step="0.01"
            :precision="2"
            v-model="scope.row.distance"
            placeholder="请输入公里数"
            v-if="scope.row.isRowEdit"
            controls-position="right"
          ></el-input-number>
          <span v-else>{{ scope.row.distance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" width="150">
        <template #default="scope">
          <el-input-number
            class="!w-95%"
            :min="0"
            :max="9999"
            :step="0.01"
            :precision="2"
            controls-position="right"
            v-model="scope.row.price"
            placeholder="请输入金额"
            v-if="scope.row.isRowEdit"
          ></el-input-number>
          <span v-else>{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template #default="scope">
          <el-button link type="danger" @click="handleTravelDelete(scope.$index)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { DICT_TYPE, getIntDictOptions, getDictOptions, getDictLabel } from '@/utils/dict'

defineOptions({ name: 'AddTravelDialog' })

const emit = defineEmits(['fetch-data'])

const dialogVisible = ref(false)
const message = useMessage() // 消息弹窗

const travelTableData = ref<any>([])
const handleTravelAdd = () => {
  travelTableData.value.push({
    startAddr: '',
    endAddr: '',
    reason: undefined,
    distance: 0,
    price: 0,
    isRowEdit: true
  })
}
const handleTravelDelete = async (index) => {
  await message.confirm('是否确定删除这条行程？')
  travelTableData.value.splice(index, 1)
  message.success('删除成功')
}

const manhourId = ref()
const openDialog = async (scope, table) => {
  manhourId.value = scope.row.id
  travelTableData.value = cloneDeep(scope.row.itineraryVOList) || []
  dialogVisible.value = true
  if (table && table.length > 0 && scope.$index !== 0) {
    travelTableData.value.push({
      startAddr: table[0].startAddr,
      endAddr: table[0].endAddr,
      reason: table[0].reason,
      distance: table[0].distance,
      price: table[0].price,
      isRowEdit: true
    })
  }
}

const handleConfirm = () => {
  if (travelTableData.value.length === 0) {
    return message.warning('请至少添加一条行程！')
  }
  // 必填校验
  for (let i in travelTableData.value) {
    if (
      !travelTableData.value[i].startAddr ||
      !travelTableData.value[i].endAddr ||
      !travelTableData.value[i].reason
    ) {
      return message.warning(`请将第${Number(i) + 1}行的行程信息填写完整！`)
    }
  }
  emit(
    'fetch-data',
    manhourId.value,
    travelTableData.value.map((item) => {
      delete item.isRowEdit
      return {
        ...item,
        reason: Array.isArray(item.reason) ? item.reason.join(',') : item.reason,
        manhourId: manhourId.value
      }
    })
  )
  message.success('添加成功')
  dialogVisible.value = false
}

defineExpose({
  openDialog
})
</script>
