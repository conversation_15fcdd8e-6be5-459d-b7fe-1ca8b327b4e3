<!--
 * @Description: 费用报销新增页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-28 11:19:49
 * @LastEditTime: 2024-08-12 16:50:40
-->

<template>
  <div class="simple-form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
      v-loading="formLoading"
      class="simple-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报销类型" prop="expensesClaimType">
            <el-select
              class="simple-select"
              v-model="formData.expensesClaimType"
              placeholder="请选择报销类型"
              clearable
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.EXPENSES_CLAIM_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="出行方式" prop="travelMode">
            <el-checkbox-group v-model="formData.travelMode" class="simple-checkbox-group">
              <el-checkbox
                v-for="(item, index) in getIntDictOptions(
                  DICT_TYPE[`${route.query.type}_TRAVEL_MODE`]
                )"
                :key="index"
                :label="item.value"
                class="simple-checkbox"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="route.query.type === 'INTER_CITY'">
        <el-col :span="12">
          <el-form-item label="关联申请单" prop="applyId">
            <div class="flex w-100%">
              <el-input
                class="mr-50px simple-input"
                v-model="formData.associatedCode"
                disabled
                placeholder="请选择关联申请单"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属项目" prop="projectId">
            <el-select
              :disabled="route.query.type === 'INTER_CITY'"
              class="simple-select"
              ref="projectRef"
              filterable
              v-model="formData.projectId"
              placeholder="请选择所属项目"
              @change="projectChange"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="`【${item.contractCode}】${item.customerName}`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" v-if="formData.projectId">
          <el-form-item label="项目日志" prop="logId">
            <div class="log-section">
              <el-button type="primary" class="select-log-btn" @click="openLogDialog">
                选择项目日志
              </el-button>
              <div class="table-section">
                <el-table ref="tableRef" :data="dailyLogTableData" :row-class-name="getRowClass" class="simple-table">
                  <el-table-column type="expand">
                    <template #default="scope">
                      <el-table :data="scope.row.itineraryVOList" :border="false" class="inner-table">
                        <el-table-column label="序号" type="index" width="80"></el-table-column>
                        <el-table-column label="出发地" prop="startAddr" />
                        <el-table-column label="目的地" prop="endAddr" />
                        <el-table-column label="事由" prop="reason" />
                        <el-table-column label="公里数" width="150" prop="distance" />
                        <el-table-column label="金额" width="150" prop="price" />
                      </el-table>
                    </template>
                  </el-table-column>
                  <el-table-column label="序号" type="index" width="100" />
                  <el-table-column label="填报人" prop="userName" width="150" />
                  <el-table-column label="填报工时" prop="consumeHour" width="150">
                    <template #default="scope"> {{ scope.row.consumeHour || 0 }} H </template>
                  </el-table-column>
                  <el-table-column
                    label="发生日期"
                    prop="consumeDate"
                    width="180"
                    :formatter="dateFormatter2"
                  />
                  <el-table-column label="工作内容" prop="remark" />
                  <el-table-column label="操作" width="200">
                    <template #default="scope">
                      <el-button
                        type="primary"
                        link
                        @click="openTravelDialog(scope, dailyLogTableData[0])"
                      >
                        {{ scope.row.itineraryVOList.length ? '修改' : '添加' }}行程
                      </el-button>
                      <el-button type="danger" link @click="handleLogDelete(scope.$index)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合计公里数">
            <span class="stat-text">{{ distanceSum }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合计金额">
            <span class="stat-text">{{ priceSum }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              v-model="formData.remark"
              placeholder="请输入备注"
              class="simple-textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="文档" prop="fileUrl">
            <div class="upload-section">
              <UploadFile
                v-model="formData.fileUrl"
                :file-size="5120"
                :limit="5"
                @uploading="handleUploading"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-form-item class="form-actions">
          <el-button @click="submitForm" type="primary" :disabled="formLoading || isUploading" class="submit-btn">
            确定
          </el-button>
          <el-button @click="handleClose" type="warning" :disabled="formLoading" class="cancel-btn">
            关闭
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <ChooseLogDialog ref="chooseLogDialogRef" @fetch-data="chooseLogDone" />
    <AddTravelDialog ref="addTravelDialogRef" @fetch-data="addTravelDone" />
  </div>
</template>
<script lang="ts" setup>
import AddTravelDialog from './components/addTravelDialog.vue'
import { useUserStore } from '@/store/modules/user'
import { dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ExpensesClaimApplyApi } from '@/api/finance/reimbursement'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isArray, isString } from '@/utils/is'
import { cloneDeep, uniqBy } from 'lodash-es'
import { getMyProjectPage } from '@/api/bpm/task'

defineOptions({ name: 'ReimbursementCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  userId: undefined,
  userName: undefined,
  applyCode: undefined,
  applyDate: undefined,
  expensesClaimType: undefined,
  applyId: undefined,
  projectId: undefined,
  projectName: undefined,
  describeVal: undefined,
  travelMode: undefined,
  count: undefined,
  hasReturn: undefined,
  startAddr: undefined,
  endAddr: undefined,
  sumPrice: undefined,
  remark: undefined,
  fileUrl: undefined,
  result: undefined,
  processInstanceId: undefined,
  associatedCode: undefined
})
const formRules = reactive({
  expensesClaimType: [{ required: true, message: '报销类型不能为空', trigger: 'blur' }],
  applyId: [{ required: true, message: '关联申请单不能为空', trigger: 'blur' }],
  projectId: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }],
  travelMode: [{ required: true, message: '出行方式不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const projectRef = ref()
const userId = useUserStore().getUser.id // 当前登录的编号

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const handleClose = () => {
  delView(unref(currentRoute))
  push('/finance/reimbursement')
}

const projectList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await getMyProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2,
      userId
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}

const projectChange = (val: any) => {
  const selectedItem = projectList.value.find((item) => item.id === val)
  formData.value.projectName = selectedItem.projectName
  dailyLogTableData.value = []
}

// 选择日志逻辑
const dailyLogTableData = ref<any>([])
const chooseLogDialogRef = ref()
const openLogDialog = () => {
  chooseLogDialogRef.value.openDialog(formData.value.projectId)
}
const chooseLogDone = (list) => {
  // 原本已经添加行程单的日志在再次添加同一日志时不清空行程单数据
  dailyLogTableData.value = uniqBy(dailyLogTableData.value.concat(list), 'id').map((item: any) => {
    if (isArray(item.itineraryVOList) && item.itineraryVOList.length > 0) {
      return item
    } else {
      return {
        ...item,
        itineraryVOList: []
      }
    }
  })
}

const handleLogDelete = async (index) => {
  await message.confirm('是否确定删除这条日志？')
  dailyLogTableData.value.splice(index, 1)
  message.success('删除成功')
}

const addTravelDialogRef = ref()
const openTravelDialog = (row, table) => {
  addTravelDialogRef.value.openDialog(row, table.itineraryVOList)
}
const addTravelDone = (id, travelList) => {
  dailyLogTableData.value.find((item) => item.id === id).itineraryVOList = travelList || []
  expandRowsWithInnerData()
}
// 判断表格是否有子项，无子项不显示展开按钮
const getRowClass = (row, rowIndex) => {
  // children 是你子项的数组 key
  if (row.row.itineraryVOList.length === 0) {
    return 'row-expand-cover'
  }
}

// 公里数合计
const distanceSum = ref(0)
// 金额合计
const priceSum = ref(0)
watch(
  () => dailyLogTableData.value,
  (val) => {
    if (!val || val.length === 0) return
    let distanceTotal = 0
    let priceTotal = 0
    for (let i = 0; i < val.length; i++) {
      if (!val[i].itineraryVOList || val[i].itineraryVOList.length === 0) continue
      for (let j = 0; j < val[i].itineraryVOList.length; j++) {
        distanceTotal += Number(val[i].itineraryVOList[j].distance)
        priceTotal += Number(val[i].itineraryVOList[j].price)
      }
    }
    distanceSum.value = distanceTotal
    priceSum.value = priceTotal
  },
  {
    immediate: true,
    deep: true
  }
)

const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 项目日志校验
  if (dailyLogTableData.value.length === 0) {
    return message.warning('请先添加项目日志')
  }
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ...cloneDeep(formData.value),
      // itineraryVOList: travelTableData.value, // 行程单
      taskManhourVOList: dailyLogTableData.value // 项目日志表格
    }
    if (isArray(data.travelMode)) {
      data.travelMode = data.travelMode.join()
    }
    delete data.id
    await ExpensesClaimApplyApi.createExpensesClaimApply(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const tableRef = ref()
// 自动展开有行程单的日志数据
const expandRowsWithInnerData = () => {
  dailyLogTableData.value.forEach((row) => {
    if (row.itineraryVOList && row.itineraryVOList.length > 0) {
      nextTick(() => {
        tableRef.value.toggleRowExpansion(row, true)
      })
    }
  })
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

onMounted(async () => {
  getProjectList()
  // 从费用报销列表点击新增进入
  if (route.query.type === 'LOCAL') {
    // 报销类型默认选择 市内交通费
    formData.value.expensesClaimType = 4
  }
  if (route.query.row) {
    const row = JSON.parse(route.query.row as any)
    formData.value.applyId = row.id
    formData.value.associatedCode = row.applyCode
    formData.value.projectId = row.projectId
    formData.value.projectName = row.projectName
  }
  if (route.query.id) {
    formData.value = await ExpensesClaimApplyApi.getExpensesClaimApply(route.query.id as any)
    if (formData.value.travelMode && isString(formData.value.travelMode)) {
      formData.value.travelMode = formData.value.travelMode.split(',').map(Number)
    }
    if (formData.value.taskManhourVOList) {
      dailyLogTableData.value = formData.value.taskManhourVOList
    }
    nextTick(() => {
      expandRowsWithInnerData()
    })
  }
})
</script>

<style lang="scss" scoped>
.simple-form-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  
  .simple-form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin: 0 auto;
    
    :deep(.el-form-item) {
      margin-bottom: 20px;
      
      .el-form-item__label {
        font-weight: 500;
        color: #333;
      }
    }
    
    .simple-select,
    .simple-input {
      width: 100%;
      
      :deep(.el-input__wrapper) {
        border: 1px solid #ddd;
        border-radius: 4px;
        
        &:hover {
          border-color: #409eff;
        }
        
        &.is-focus {
          border-color: #409eff;
        }
      }
    }
    
    .simple-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      
      .simple-checkbox {
        margin-right: 0;
      }
    }
    
    .log-section {
      .select-log-btn {
        margin-bottom: 15px;
      }
      
      .table-section {
        border: 1px solid #eee;
        border-radius: 4px;
        overflow: hidden;
        
        .simple-table {
          :deep(.el-table__header) {
            background: #fafafa;
            
            th {
              background: transparent;
              border-bottom: 1px solid #eee;
              color: #333;
              font-weight: 500;
            }
          }
          
          :deep(.el-table__body) {
            tr {
              &:hover {
                background: #f5f7fa;
              }
            }
            
            td {
              border-bottom: 1px solid #eee;
            }
          }
        }
        
        .inner-table {
          :deep(.el-table__header) {
            background: #f5f7fa;
            
            th {
              background: transparent;
              color: #666;
            }
          }
        }
      }
    }
    
    .stat-text {
      font-size: 16px;
      font-weight: 600;
      color: #409eff;
    }
    
    .simple-textarea {
      :deep(.el-textarea__inner) {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 12px;
        
        &:hover {
          border-color: #409eff;
        }
        
        &:focus {
          border-color: #409eff;
        }
      }
    }
    
    .upload-section {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background: #fafafa;
    }
    
    .form-actions {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      
      .submit-btn,
      .cancel-btn {
        margin: 0 10px;
        padding: 8px 20px;
      }
    }
  }
}

// 保持原有的表格展开按钮隐藏样式
:deep(.el-table .row-expand-cover .cell .el-table__expand-icon) {
  display: none;
}
</style>
