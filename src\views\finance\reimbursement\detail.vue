<!--
 * @Description: 费用报销详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-28 11:19:49
 * @LastEditTime: 2024-07-18 16:02:01
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="费用报销申请单编号">
        {{ detailData.applyCode }}
      </el-descriptions-item>
      <el-descriptions-item label="报销类型">
        <dict-tag :type="DICT_TYPE.EXPENSES_CLAIM_TYPE" :value="detailData.expensesClaimType" />
      </el-descriptions-item>
      <el-descriptions-item label="申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="申请日期">
        {{ formatDate(detailData.applyDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="关联申请单">
        {{ detailData.associatedCode }}
      </el-descriptions-item>
      <el-descriptions-item label="所属客户">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="所属项目">
        {{ detailData.projectName }}
      </el-descriptions-item>
      <el-descriptions-item label="项目日志">
        <el-table
          ref="tableRef"
          :data="detailData.taskManhourVOList"
          :row-class-name="getRowClass"
          :border="false"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <el-table :data="scope.row.itineraryVOList">
                <el-table-column label="序号" type="index" width="80"></el-table-column>
                <el-table-column label="出发地" prop="startAddr" />
                <el-table-column label="目的地" prop="endAddr" />
                <el-table-column label="事由" prop="reason" />
                <el-table-column label="公里数" width="150" prop="distance" />
                <el-table-column label="金额" width="150" prop="price" />
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column label="填报人" prop="userName" width="150" />
          <el-table-column label="填报工时" prop="consumeHour" width="150">
            <template #default="scope"> {{ scope.row.consumeHour || 0 }} H </template>
          </el-table-column>
          <el-table-column
            label="发生日期"
            prop="consumeDate"
            width="180"
            :formatter="dateFormatter2"
          />
          <el-table-column label="工作内容" prop="remark" />
        </el-table>
      </el-descriptions-item>
      <el-descriptions-item label="合计公里数">
        {{ distanceSum }}
      </el-descriptions-item>
      <el-descriptions-item label="合计金额">
        {{ priceSum }}
      </el-descriptions-item>
      <el-descriptions-item label="出行方式">
        <dict-tag
          :type="
            detailData.applyId ? DICT_TYPE.INTER_CITY_TRAVEL_MODE : DICT_TYPE.LOCAL_TRAVEL_MODE
          "
          :value="detailData.travelMode"
        />
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="文档">
        <FileListPreview :fileUrl="detailData.fileUrl" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate, dateFormatter2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ExpensesClaimApplyApi } from '@/api/finance/reimbursement'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ReimbursementDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ExpensesClaimApplyApi.getExpensesClaimApply(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/finance/reimbursement')
}
// 判断表格是否有子项，无子项不显示展开按钮
const getRowClass = (row, rowIndex) => {
  // children 是你子项的数组 key
  if (row.row.itineraryVOList.length === 0) {
    return 'row-expand-cover'
  }
}

// 公里数合计
const distanceSum = ref(0)
// 金额合计
const priceSum = ref(0)
watch(
  () => detailData.value.taskManhourVOList,
  (val) => {
    if (!val || val.length === 0) return
    let distanceTotal = 0
    let priceTotal = 0
    for (let i = 0; i < val.length; i++) {
      if (!val[i].itineraryVOList || val[i].itineraryVOList.length === 0) continue
      for (let j = 0; j < val[i].itineraryVOList.length; j++) {
        distanceTotal += Number(val[i].itineraryVOList[j].distance)
        priceTotal += Number(val[i].itineraryVOList[j].price)
      }
    }
    distanceSum.value = distanceTotal
    priceSum.value = priceTotal
  },
  {
    immediate: true,
    deep: true
  }
)

const tableRef = ref()
// 自动展开有行程单的日志数据
const expandRowsWithInnerData = () => {
  detailData.value.taskManhourVOList.forEach((row) => {
    if (row.itineraryVOList && row.itineraryVOList.length > 0) {
      tableRef.value.toggleRowExpansion(row, true)
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  await getInfo()
  expandRowsWithInnerData()
})

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
:deep(.el-table .row-expand-cover .cell .el-table__expand-icon) {
  display: none;
}
</style>
