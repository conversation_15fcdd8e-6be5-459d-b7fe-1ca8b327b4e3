<!--
 * @Description: 费用报销列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-28 11:19:49
 * @LastEditTime: 2025-01-07 14:11:28
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
    >
      <el-form-item label="费用报销申请单编号" prop="applyCode" label-width="150">
        <el-input
          v-model="queryParams.applyCode"
          placeholder="请输入费用报销申请单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同号" prop="contractCode" label-width="150">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->

      <el-form-item label="报销类型" prop="expensesClaimType">
        <el-select
          v-model="queryParams.expensesClaimType"
          placeholder="请选择报销类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EXPENSES_CLAIM_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="申请人" prop="userId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.userId"
          placeholder="请选择申请人"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="事件发生日期" prop="occurDateRange" label-width="125">
        <el-date-picker
          v-model="occurDateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectOccurDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态" prop="result">
        <el-select
          v-model="queryParams.result"
          placeholder="请选择审核状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mt5 items-center">
      <el-form-item class="ml-5px">
        <el-button type="primary" plain @click="handleCreate()">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
      <el-form-item class="ml20">
        <div class="text-20px font-bold">当前页总金额：{{ list[0]?.sumTotal || 0 }}元</div>
      </el-form-item>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="费用报销申请单编号" align="center" prop="applyCode" width="160" />
      <el-table-column label="客户名称" align="center" prop="customerName" width="200" />
      <el-table-column label="合同号" align="center" prop="contractCode" width="150" />
      <el-table-column label="申请人" align="center" prop="userName" width="100" />
      <el-table-column label="当前审批人" align="center" prop="todoUserName" width="100" />
      <el-table-column
        label="申请日期"
        align="center"
        prop="applyDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column label="实际发生日期" align="center" prop="exactDate" width="200">
        <template #default="scope">
          <div
            v-if="scope.row.exactDate"
            class="inline-flex gap-5px items-center justify-center flex-wrap"
          >
            <el-tag v-for="(item, index) in scope.row.exactDate?.split(',')">{{ item }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发生总金额" align="center" prop="sumPriceStr" width="100">
        <template #default="scope">
          <div
            v-if="scope.row.sumPriceStr"
            class="inline-flex gap-5px items-center justify-center flex-wrap"
          >
            <el-tag type="warning" v-for="(item, index) in scope.row.sumPriceStr?.split(',')">
              {{ item }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>

      <el-table-column label="报销类型" align="center" prop="expensesClaimType" width="130">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EXPENSES_CLAIM_TYPE" :value="scope.row.expensesClaimType" />
        </template>
      </el-table-column>
      <el-table-column label="关联申请单编号" align="center" prop="associatedCode" width="160" />
      <el-table-column label="项目名称" align="center" prop="projectName" width="200" />
      <el-table-column label="项目名称" align="center" prop="projectName" width="200" />
      <el-table-column label="出行方式" align="center" prop="travelMode" width="210">
        <template #default="scope">
          <dict-tag
            :type="
              scope.row.applyId ? DICT_TYPE.INTER_CITY_TRAVEL_MODE : DICT_TYPE.LOCAL_TRAVEL_MODE
            "
            :value="scope.row.travelMode"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { ExpensesClaimApplyApi } from '@/api/finance/reimbursement'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'Reimbursement' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dateRange = ref<any>([])
const occurDateRange = ref<any>([])
const router = useRouter() // 路由
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list: any = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const exportLoading = ref(false) // 导出的加载中

const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  customerName: undefined,
  userName: undefined,
  applyCode: undefined,
  applyDate: [],
  contractCode: undefined,
  expensesClaimType: undefined,
  applyId: undefined,
  projectId: undefined,
  projectName: undefined,
  describeVal: undefined,
  travelMode: undefined,
  count: undefined,
  hasReturn: undefined,
  startAddr: undefined,
  endAddr: undefined,
  sumPrice: undefined,
  remark: undefined,
  fileUrl: undefined,
  result: undefined,
  processInstanceId: undefined,
  createTime: [],
  startDate: undefined,
  endDate: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ExpensesClaimApplyApi.getExpensesClaimApplyPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  occurDateRange.value = []
  queryFormRef.value.resetFields()
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    userId: undefined,
    userName: undefined,
    applyCode: undefined,
    applyDate: [],
    expensesClaimType: undefined,
    contractCode: undefined,
    applyId: undefined,
    projectId: undefined,
    projectName: undefined,
    describeVal: undefined,
    travelMode: undefined,
    count: undefined,
    hasReturn: undefined,
    startAddr: undefined,
    endAddr: undefined,
    sumPrice: undefined,
    remark: undefined,
    fileUrl: undefined,
    result: undefined,
    processInstanceId: undefined,
    createTime: [],
    startDate: undefined,
    endDate: undefined,
    exactStartDate: undefined,
    exactEndDate: undefined
  }
  handleQuery()
}
/** 添加操作 */
const handleCreate = () => {
  router.push({
    name: 'ReimbursementCreate',
    query: {
      type: 'LOCAL'
    }
  })
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ExpensesClaimApplyApi.exportExpensesClaimApply(queryParams.value)
    download.excel(data, '费用报销信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ReimbursementDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const reissue = (row) => {
  router.push({
    name: 'ReimbursementCreate',
    query: {
      id: row.id,
      type: row.applyId ? 'INTER_CITY' : 'LOCAL'
    }
  })
}
const selectDate = () => {
  if (dateRange.value != null) {
    queryParams.value.startDate = dateRange.value[0]
    queryParams.value.endDate = dateRange.value[1]
  } else {
    queryParams.value.startDate = ''
    queryParams.value.endDate = ''
  }
}
const selectOccurDate = () => {
  if (occurDateRange.value != null) {
    queryParams.value.exactStartDate = occurDateRange.value[0]
    queryParams.value.exactEndDate = occurDateRange.value[1]
  } else {
    queryParams.value.exactStartDate = ''
    queryParams.value.exactEndDate = ''
  }
}
const userList = ref<any[]>([]) // 用户列表
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
