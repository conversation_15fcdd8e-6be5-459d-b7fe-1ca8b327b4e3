<template>
  <ContentWrap>
    <FullCalendar :options="calendarOptions" style="width: 100%" ref="fullCalendarRef" />
  </ContentWrap>
</template>

<script setup lang="ts">
import FullCalendar from '@fullcalendar/vue3'
import multiMonthPlugin from '@fullcalendar/multimonth'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import locale from '@fullcalendar/core/locales/zh-cn'
import dayjs from 'dayjs'
import { CarApplyApi, CarApplyVO } from '@/api/hrm/carApply'
const router = useRouter() // 路由

let carList = ref<any>([])
const startDate = ref('')
const endDate = ref('')
const fullCalendarRef = ref()
const isAlreadyEnter = ref(false)

const calendarOptions = ref({
  plugins: [multiMonthPlugin, dayGridPlugin, timeGridPlugin], // 需要加载的插件
  initialView: 'dayGridMonth', // 初始视图
  multiMonthMaxColumns: 3, // 多月展示时最多几个月一行
  height: 'auto',
  locale, // 语言汉化
  selectable: true,
  editable: true,
  dayMaxEventRows: 10, // 事件最大展示列数
  fixedWeekCount: false, // 因为每月起始日是星期几不固定，导致一个月的行数会不固定，是否固定行数
  showNonCurrentDates: false, // 隐藏非当前月份的日期
  handleWindowResize: true,
  windowResizeDelay: 100,
  aspectRatio: 2, // 宽高比
  // 日历上方的按钮和title
  headerToolbar: {
    left: '',
    center: 'title',
    // right: 'multiMonthYear,dayGridMonth,timeGridWeek,timeGridDay'
    right: 'prevYear,prev,next,nextYear today'
  },
  //多数据源展示
  eventSources: [carList.value],
  // 事件的点击
  eventClick: (info) => {
    const {
      event: { extendedProps, id, startStr }
    } = info
    router.push({
      name: 'HrmCarApplyDetail',
      query: {
        id: id
      }
    })
  },
  datesSet: (info) => {
    startDate.value = dayjs(info.start).format('YYYY-MM-DD')
    endDate.value = dayjs(info.end).format('YYYY-MM-DD')
    getList()
  }
})

/** 查询列表 */
const getList = async () => {
  try {
    const queryData = {
      pageSize: -1,
      startDate: startDate.value,
      endDate: endDate.value
    }
    const data = await CarApplyApi.getCarApplyPage(queryData)
    data.list.forEach((item) => {
      carList.value.push({
        ...item,
        title: item.applicant + '申请使用' + item.applyCar,
        start: dayjs(item.actualUseDate).format('YYYY-MM-DD'),
        backgroundColor: '#3788d8',
        borderColor: '#3788d8',
        allDay: true
      })
    })
    // 组装数据，重新渲染
    calendarOptions.value.eventSources = [carList.value]
    nextTick(() => {
      fullCalendarRef.value.getApi().render()
    })
  } finally {
    isAlreadyEnter.value = true
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
})

onActivated(async () => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
