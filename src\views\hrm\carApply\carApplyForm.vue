<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="申请人" prop="applicant">
        <el-input v-model="formData.applicant" placeholder="请输入申请人" />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input v-model="formData.department" placeholder="请输入部门" />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input v-model="formData.phoneNumber" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="申请用车日期" prop="applicationDate">
        <el-date-picker
          v-model="formData.applicationDate"
          type="date"
          value-format="x"
          placeholder="选择申请用车日期"
        />
      </el-form-item>
      <el-form-item label="实际用车日期" prop="actualUseDate">
        <el-date-picker
          v-model="formData.actualUseDate"
          type="date"
          value-format="x"
          placeholder="选择实际用车日期"
        />
      </el-form-item>
      <el-form-item label="本月用车次数" prop="monthlyUsageCount">
        <el-input v-model="formData.monthlyUsageCount" placeholder="请输入本月用车次数" />
      </el-form-item>
      <el-form-item label="车辆属性" prop="vehicleType">
        <el-select v-model="formData.vehicleType" placeholder="请选择车辆属性">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请事由" prop="useReason">
        <el-input v-model="formData.useReason" placeholder="请输入申请事由" />
      </el-form-item>
      <el-form-item label="发车时间" prop="departureTime">
        <el-date-picker
          v-model="formData.departureTime"
          type="date"
          value-format="x"
          placeholder="选择发车时间"
        />
      </el-form-item>
      <el-form-item label="发车地点" prop="departureLocation">
        <el-input v-model="formData.departureLocation" placeholder="请输入发车地点" />
      </el-form-item>
      <el-form-item label="客户名称" prop="clientName">
        <el-input v-model="formData.clientName" placeholder="请输入客户名称" />
      </el-form-item>
      <el-form-item label="客户详细地址" prop="clientAddress">
        <el-input v-model="formData.clientAddress" placeholder="请输入客户详细地址" />
      </el-form-item>
      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item label="联系人部门" prop="contactDepartment">
        <el-input v-model="formData.contactDepartment" placeholder="请输入联系人部门" />
      </el-form-item>
      <el-form-item label="联系人职务" prop="contactPosition">
        <el-input v-model="formData.contactPosition" placeholder="请输入联系人职务" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contactInfo">
        <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CarApplyApi, CarApplyVO } from '@/api/hrm/carApply'

/** 用车申请 表单 */
defineOptions({ name: 'CarApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  applicant: undefined,
  department: undefined,
  phoneNumber: undefined,
  applicationDate: undefined,
  actualUseDate: undefined,
  monthlyUsageCount: undefined,
  vehicleType: undefined,
  useReason: undefined,
  departureTime: undefined,
  departureLocation: undefined,
  clientName: undefined,
  clientAddress: undefined,
  contactPerson: undefined,
  contactDepartment: undefined,
  contactPosition: undefined,
  contactInfo: undefined
})
const formRules = reactive({
  applicant: [{ required: true, message: '申请人不能为空', trigger: 'blur' }],
  department: [{ required: true, message: '部门不能为空', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  applicationDate: [{ required: true, message: '申请用车日期不能为空', trigger: 'blur' }],
  monthlyUsageCount: [{ required: true, message: '本月用车次数不能为空', trigger: 'blur' }],
  vehicleType: [{ required: true, message: '车辆属性不能为空', trigger: 'change' }],
  useReason: [{ required: true, message: '申请事由不能为空', trigger: 'blur' }],
  departureTime: [{ required: true, message: '发车时间不能为空', trigger: 'blur' }],
  departureLocation: [{ required: true, message: '发车地点不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CarApplyApi.getCarApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CarApplyVO
    if (formType.value === 'create') {
      await CarApplyApi.createCarApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await CarApplyApi.updateCarApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    applicant: undefined,
    department: undefined,
    phoneNumber: undefined,
    applicationDate: undefined,
    actualUseDate: undefined,
    monthlyUsageCount: undefined,
    vehicleType: undefined,
    useReason: undefined,
    departureTime: undefined,
    departureLocation: undefined,
    clientName: undefined,
    clientAddress: undefined,
    contactPerson: undefined,
    contactDepartment: undefined,
    contactPosition: undefined,
    contactInfo: undefined
  }
  formRef.value?.resetFields()
}
</script>
