<template>
  <el-form
    :rules="formRules"
    ref="formRef"
    :model="formData"
    label-width="120px"
    v-loading="formLoading"
  >
    <el-row :gutter="10">
      <el-col :span="10">
        <el-form-item label="申请人" prop="userId">
          <el-select
            filterable
            v-model="formData.userId"
            placeholder="请先选择申请人"
            value-key="id"
            lable-key="nickname"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="formData.phoneNumber" placeholder="请输入手机号" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="申请用车日期" prop="applicationDate">
          <el-date-picker
            v-model="formData.applicationDate"
            type="date"
            value-format="x"
            placeholder="选择申请用车日期"
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="实际用车日期" prop="actualUseDate">
          <el-date-picker
            v-model="formData.actualUseDate"
            type="date"
            value-format="x"
            placeholder="选择实际用车日期"
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="本月用车次数" prop="monthlyUsageCount">
          <el-input v-model="formData.monthlyUsageCount" placeholder="请输入本月用车次数" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="车辆属性" prop="vehicleType">
          <el-radio-group v-model="formData.vehicleType" class="!w-100%">
            <el-radio label="公司">公司</el-radio>
            <el-radio label="自备">自备</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="用车申请" prop="applyCar">
          <el-radio-group v-model="formData.applyCar" class="!w-100%">
            <el-radio label="半天">半天</el-radio>
            <el-radio label="一天">一天</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="申请事由" prop="useReason">
          <el-input v-model="formData.useReason" placeholder="请输入申请事由" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="发车时间" prop="departureTime">
          <el-date-picker
            v-model="formData.departureTime"
            type="date"
            value-format="x"
            placeholder="选择发车时间"
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="发车地点" prop="departureLocation">
          <el-input v-model="formData.departureLocation" placeholder="请输入发车地点" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="客户名称" prop="clientName">
          <el-input v-model="formData.clientName" placeholder="请输入客户名称" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="客户详细地址" prop="clientAddress">
          <el-input v-model="formData.clientAddress" placeholder="请输入客户详细地址" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="联系人部门" prop="contactDepartment">
          <el-input v-model="formData.contactDepartment" placeholder="请输入联系人部门" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="联系人职务" prop="contactPosition">
          <el-input v-model="formData.contactPosition" placeholder="请输入联系人职务" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item>
          <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
          <el-button @click="handleClose" type="warning" :disabled="formLoading">关 闭</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { CarApplyApi, CarApplyVO } from '@/api/hrm/carApply'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'CarApplyCreate' })
const { t } = useI18n() // 国际化

const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const route = useRoute()
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const customerDialogRef = ref()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  applicant: undefined,
  department: undefined,
  phoneNumber: undefined,
  applicationDate: undefined,
  actualUseDate: undefined,
  monthlyUsageCount: undefined,
  vehicleType: undefined,
  applyCar: undefined,
  useReason: undefined,
  departureTime: undefined,
  departureLocation: undefined,
  clientName: undefined,
  clientAddress: undefined,
  contactPerson: undefined,
  contactDepartment: undefined,
  contactPosition: undefined,
  contactInfo: undefined,
  userId: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '申请人不能为空', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  actualUseDate: [{ required: true, message: '实际用车日期不能为空', trigger: 'blur' }],
  applyCar: [{ required: true, message: '车辆属性不能为空', trigger: 'change' }],
  useReason: [{ required: true, message: '申请事由不能为空', trigger: 'blur' }],
  departureTime: [{ required: true, message: '发车时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CarApplyVO as any
    delete data.id
    await CarApplyApi.createCarApply(data)
    message.success(t('common.createSuccess'))

    // 发送操作成功的事件
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}

onMounted(async () => {
  if (route.query.id) {
    formData.value = await CarApplyApi.getCarApply(route.query.id as any)
  }
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 100%;
}
</style>
