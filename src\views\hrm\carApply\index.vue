<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="申请人" align="center" prop="applicant">
        <template #default="scope">
          {{
            scope.row.applicant
              ? scope.row.applicant
              : userList.find((item) => item.id === scope.row.userId)?.nickname
          }}
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center" prop="department" />
      <el-table-column label="手机号" align="center" prop="phoneNumber" />
      <el-table-column label="申请用车日期" align="center" prop="applicationDate">
        <template #default="scope">
          {{ formatDate(scope.row.applicationDate, 'YYYY-MM-DD') }}
        </template>
      </el-table-column>
      <el-table-column label="实际用车日期" align="center" prop="actualUseDate">
        <template #default="scope">
          {{ formatDate(scope.row.actualUseDate, 'YYYY-MM-DD') }}
        </template>
      </el-table-column>
      <el-table-column label="本月用车次数" align="center" prop="monthlyUsageCount" />
      <el-table-column label="车辆属性" align="center" prop="vehicleType" />
      <el-table-column label="用车申请" align="center" prop="applyCar" />
      <el-table-column label="申请事由" align="center" prop="useReason" />
      <el-table-column
        label="发车时间"
        align="center"
        prop="departureTime"
        :formatter="dateFormatter"
        width="180px"
      >
        <template #default="scope">
          {{ formatDate(scope.row.departureTime, 'YYYY-MM-DD') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CarApplyForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CarApplyApi, CarApplyVO } from '@/api/hrm/carApply'
import CarApplyForm from './carApplyForm.vue'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { formatDate } from '@/utils/formatTime'
import * as UserApi from '@/api/system/user'

/** 用车申请 列表 */
defineOptions({ name: 'CarApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<CarApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  applicant: undefined,
  department: undefined,
  phoneNumber: undefined,
  applicationDate: [],
  actualUseDate: [],
  monthlyUsageCount: undefined,
  vehicleType: undefined,
  applyCar: undefined,
  useReason: undefined,
  departureTime: [],
  departureLocation: undefined,
  clientName: undefined,
  clientAddress: undefined,
  contactPerson: undefined,
  contactDepartment: undefined,
  contactPosition: undefined,
  contactInfo: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CarApplyApi.getCarApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  router.push({ name: 'HrmCarApplyCreate' })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CarApplyApi.deleteCarApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CarApplyApi.exportCarApply(queryParams)
    download.excel(data, '用车申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const reissue = (row) => {
  router.push({
    name: 'CrmWorkReportCreate',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'HrmCarApplyDetail',
    query: {
      id: row.id
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
