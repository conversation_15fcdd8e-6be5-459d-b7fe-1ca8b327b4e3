<template>
  <Dialog width="75%" title="员工详情" v-model="dialogVisible" class="employee-detail-dialog">
    <div class="detail-container" v-loading="detailLoading">
      <div v-if="detailData" class="detail-content">
        <!-- 基本信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:user" />
            </div>
            <h3>基本信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">档案/工号：</span>
                  <span class="value">{{ detailData.workNo || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">姓名：</span>
                  <span class="value">{{ detailData.userName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">部门：</span>
                  <span class="value">{{ detailData.deptName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职务：</span>
                  <span class="value">{{ detailData.occupation || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">类别：</span>
                  <span class="value">{{ detailData.staffType || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">入职时间：</span>
                  <span class="value">{{ formatDate(detailData.employedTime) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 联系信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:phone" />
            </div>
            <h3>联系信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">联系方式：</span>
                  <span class="value">{{ detailData.phone || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">邮箱：</span>
                  <span class="value">{{ detailData.email || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">户籍地址：</span>
                  <span class="value">{{ detailData.residenceAddress || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">居住地址：</span>
                  <span class="value">{{ detailData.address || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">紧急联系人：</span>
                  <span class="value">{{ detailData.emergencyContactPerson || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">紧急联系人电话：</span>
                  <span class="value">{{ detailData.emergencyContact || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 身份信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:document" />
            </div>
            <h3>身份信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">身份证号：</span>
                  <span class="value">{{ detailData.idNumber || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">出生日期：</span>
                  <span class="value">{{ formatDate(detailData.birthDay) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">年龄：</span>
                  <span class="value">{{ calculateAge(detailData.birthDay) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">性别：</span>
                  <span class="value">{{ detailData.sex || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">政治面貌：</span>
                  <span class="value">{{ detailData.politicalStatus || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 教育背景区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:reading" />
            </div>
            <h3>教育背景</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">学历：</span>
                  <span class="value">{{ detailData.highestDegree || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">学校：</span>
                  <span class="value">{{ detailData.highestSchool || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">专业：</span>
                  <span class="value">{{ detailData.major || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 工作信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:briefcase" />
            </div>
            <h3>工作信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">试用期|转正：</span>
                  <span class="value">{{ detailData.workStatusDesc || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">合同签定日期：</span>
                  <span class="value">{{ formatDate(detailData.contractStartDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">试用期结束时间：</span>
                  <span class="value">{{ formatDate(detailData.trialEndDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">合同到期日期：</span>
                  <span class="value">{{ formatDate(detailData.contractEndDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">合同续签日期：</span>
                  <span class="value">{{ formatDate(detailData.contractRenewStartDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">合同续签到期日期：</span>
                  <span class="value">{{ formatDate(detailData.contractRenewEndDate) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 财务信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:money" />
            </div>
            <h3>财务信息</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">银行卡号：</span>
                  <span class="value">{{ detailData.bankAccount || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">公积金账号：</span>
                  <span class="value">{{ detailData.fundAccount || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 职称证书区域 -->
        <div class="detail-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:trophy" />
            </div>
            <h3>职称证书</h3>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职称1：</span>
                  <span class="value">{{ detailData.titleOne || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职称2：</span>
                  <span class="value">{{ detailData.titleTwo || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职称3：</span>
                  <span class="value">{{ detailData.titleThree || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职业资格证书1：</span>
                  <span class="value">{{ detailData.certificateOne || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职业资格证书2：</span>
                  <span class="value">{{ detailData.certificateTwo || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">职业资格证书3：</span>
                  <span class="value">{{ detailData.certificateThree || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 备注信息区域 -->
        <div class="detail-section" v-if="detailData.remark">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:edit" />
            </div>
            <h3>备注信息</h3>
          </div>
          <div class="section-content">
            <div class="info-item full-width">
              <span class="label">备注：</span>
              <span class="value">{{ detailData.remark }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" class="close-btn">关 闭</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { EmployeeInfoApi, EmployeeInfoVO } from '@/api/OA/employeeinfo'

/** 员工信息详情 */
defineOptions({ name: 'EmployeeInfoDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<EmployeeInfoVO | null>(null) // 详情数据

// 计算年龄的方法
const calculateAge = (birthDay: number | undefined) => {
  if (!birthDay) return '-'
  const birthDate = new Date(birthDay)
  const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const m = today.getMonth() - birthDate.getMonth()
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  return age + '岁'
}

// 格式化日期
const formatDate = (timestamp: number | undefined) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  detailLoading.value = true
  try {
    detailData.value = await EmployeeInfoApi.getEmployeeInfo(id)
  } finally {
    detailLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
.employee-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    
    .el-dialog__body {
      padding: 0;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 24px 32px;
      background: #f8fafc;
      border-top: 1px solid #e2e8f0;
    }
  }
}

.detail-container {
  padding: 32px;
  background: #ffffff;
}

.detail-content {
  .detail-section {
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    .section-header {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      
      .section-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
    
    .section-content {
      padding: 24px;
      
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
      
      .info-item {
        display: flex;
        align-items: flex-start;
        line-height: 1.6;
        
        &.full-width {
          width: 100%;
        }
        
        .label {
          font-weight: 500;
          color: #475569;
          font-size: 14px;
          min-width: 120px;
          flex-shrink: 0;
        }
        
        .value {
          color: #1e293b;
          font-size: 14px;
          word-break: break-all;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  
  .close-btn {
    padding: 10px 32px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .employee-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  
  .detail-container {
    padding: 20px;
  }
  
  .detail-content {
    .detail-section {
      .section-header {
        padding: 16px 20px;
        
        .section-icon {
          width: 28px;
          height: 28px;
          
          .svg-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        h3 {
          font-size: 15px;
        }
      }
      
      .section-content {
        padding: 20px;
        
        .info-item {
          .label {
            min-width: 100px;
          }
        }
      }
    }
  }
}
</style> 