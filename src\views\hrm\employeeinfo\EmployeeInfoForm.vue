<template>
  <Dialog width="75%" :title="dialogTitle" v-model="dialogVisible" class="employee-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:user" />
            </div>
            <h3>基本信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="档案/工号" prop="workNo">
                <el-input 
                  v-model="formData.workNo" 
                  placeholder="请输入档案/工号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="姓名" prop="userName">
                <el-input 
                  v-model="formData.userName" 
                  placeholder="请输入姓名"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门" prop="deptId">
                <el-tree-select
                  v-model="formData.deptId"
                  :data="deptList"
                  default-expand-all
                  :props="defaultProps"
                  check-strictly
                  node-key="id"
                  placeholder="请选择所属部门"
                  @current-change="handleDeptChange"
                  class="modern-select"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职务" prop="occupation">
                <el-input 
                  v-model="formData.occupation" 
                  placeholder="请输入职务"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类别" prop="staffType">
                <el-input 
                  v-model="formData.staffType" 
                  placeholder="请输入类别"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入职时间" prop="employedTime">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.employedTime"
                  type="date"
                  value-format="x"
                  placeholder="选择入职时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 联系信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:phone" />
            </div>
            <h3>联系信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="联系方式" prop="phone">
                <el-input 
                  v-model="formData.phone" 
                  placeholder="请输入联系方式"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input 
                  v-model="formData.email" 
                  placeholder="请输入邮箱"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="户籍地址" prop="residenceAddress">
                <el-input 
                  v-model="formData.residenceAddress" 
                  placeholder="请输入户籍地址"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="居住地址" prop="address">
                <el-input 
                  v-model="formData.address" 
                  placeholder="请输入居住地址"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急联系人" prop="emergencyContactPerson">
                <el-input 
                  v-model="formData.emergencyContactPerson" 
                  placeholder="请输入紧急联系人"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急联系人电话" prop="emergencyContact">
                <el-input 
                  v-model="formData.emergencyContact" 
                  placeholder="请输入紧急联系人电话"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 身份信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:document" />
            </div>
            <h3>身份信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="身份证号" prop="idNumber">
                <el-input
                  @input="handleIdCardInput"
                  maxlength="18"
                  v-model="formData.idNumber"
                  placeholder="请输入身份证号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出生日期" prop="birthDay">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.birthDay"
                  type="date"
                  value-format="x"
                  placeholder="选择出生日期"
                  @change="updateAge"
                  :disabled-date="disableFutureDates"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年龄">
                <el-input 
                  v-model="age" 
                  disabled 
                  placeholder="年龄将根据出生日期自动计算"
                  class="modern-input disabled"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="sex">
                <el-input 
                  v-model="formData.sex" 
                  placeholder="请输入性别"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="政治面貌" prop="politicalStatus">
                <el-input 
                  v-model="formData.politicalStatus" 
                  placeholder="请输入政治面貌"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 教育背景区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:reading" />
            </div>
            <h3>教育背景</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="学历" prop="highestDegree">
                <el-input 
                  v-model="formData.highestDegree" 
                  placeholder="请输入学历"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学校" prop="highestSchool">
                <el-input 
                  v-model="formData.highestSchool" 
                  placeholder="请输入学校"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专业" prop="major">
                <el-input 
                  v-model="formData.major" 
                  placeholder="请输入专业"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 工作信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:briefcase" />
            </div>
            <h3>工作信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="试用期|转正" prop="workStatusDesc">
                <el-input 
                  v-model="formData.workStatusDesc" 
                  placeholder="请输入试用期|转正"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同签定日期" prop="contractStartDate">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.contractStartDate"
                  type="date"
                  value-format="x"
                  placeholder="选择合同签定日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试用期结束时间" prop="trialEndDate">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.trialEndDate"
                  type="date"
                  value-format="x"
                  placeholder="选择试用期结束时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同到期日期" prop="contractEndDate">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.contractEndDate"
                  type="date"
                  value-format="x"
                  placeholder="选择合同到期日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同续签日期" prop="contractRenewStartDate">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.contractRenewStartDate"
                  type="date"
                  value-format="x"
                  placeholder="选择合同续签日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同续签到期日期" prop="contractRenewEndDate">
                <el-date-picker
                  class="modern-date-picker"
                  v-model="formData.contractRenewEndDate"
                  type="date"
                  value-format="x"
                  placeholder="选择合同续签到期日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 财务信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:money" />
            </div>
            <h3>财务信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="bankAccount">
                <el-input 
                  v-model="formData.bankAccount" 
                  placeholder="请输入银行卡号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公积金账号" prop="fundAccount">
                <el-input 
                  v-model="formData.fundAccount" 
                  placeholder="请输入公积金账号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 职称证书区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:trophy" />
            </div>
            <h3>职称证书</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="职称1" prop="titleOne">
                <el-input 
                  v-model="formData.titleOne" 
                  placeholder="请输入职称1"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职称2" prop="titleTwo">
                <el-input 
                  v-model="formData.titleTwo" 
                  placeholder="请输入职称2"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职称3" prop="titleThree">
                <el-input 
                  v-model="formData.titleThree" 
                  placeholder="请输入职称3"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职业资格证书1" prop="certificateOne">
                <el-input 
                  v-model="formData.certificateOne" 
                  placeholder="请输入职业资格证书1"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职业资格证书2" prop="certificateTwo">
                <el-input 
                  v-model="formData.certificateTwo" 
                  placeholder="请输入职业资格证书2"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职业资格证书3" prop="certificateThree">
                <el-input 
                  v-model="formData.certificateThree" 
                  placeholder="请输入职业资格证书3"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 备注区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:edit" />
            </div>
            <h3>备注信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  v-model="formData.remark"
                  placeholder="请输入备注信息"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 目标设置区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:target" />
            </div>
            <h3>目标设置</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="目标管理">
                <div class="target-setting-container">
                  <el-button 
                    type="primary" 
                    @click="openTargetSetting"
                    class="target-setting-btn"
                  >
                    <svg-icon name="ep:setting" class="btn-icon" />
                    设置员工目标
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" class="cancel-btn">取 消</el-button>
        <el-button @click="submitForm" type="primary" :disabled="formLoading" class="submit-btn">
          <svg-icon name="ep:check" class="btn-icon" />
          确 定
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { EmployeeInfoApi, EmployeeInfoVO } from '@/api/OA/employeeinfo'
// import { DICT_TYPE, getIntDictOptions, getDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import { useRouter } from 'vue-router'

/** 员工信息 表单 */
defineOptions({ name: 'EmployeeInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const router = useRouter() // 路由
const deptList = ref<Tree[]>([]) // 树形结构
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const age = ref<string>('') // 年龄字段

// 禁用今天之后的日期
const disableFutureDates = (time: Date) => {
  return time.getTime() > Date.now()
}

// 计算年龄的方法
const calculateAge = (birthDay: number | undefined) => {
  if (!birthDay) return ''
  const birthDate = new Date(birthDay)
  const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const m = today.getMonth() - birthDate.getMonth()
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  return age + '岁'
}

// 更新年龄
const updateAge = () => {
  age.value = calculateAge(formData.value.birthDay)
}

const formData = ref<any>({
  id: undefined,
  userName: undefined,
  politicalStatus: undefined,
  bankAccount: undefined,
  major: undefined,
  remark: undefined,
  userId: undefined,
  workNo: undefined,
  deptId: undefined,
  deptName: undefined,
  occupation: undefined,
  staffType: undefined,
  employedTime: undefined,
  phone: undefined,
  idNumber: undefined,
  birthDay: undefined,
  sex: undefined,
  highestDegree: undefined,
  highestSchool: undefined,
  workStatusDesc: undefined,
  contractStartDate: undefined,
  trialEndDate: undefined,
  contractEndDate: undefined,
  contractRenewStartDate: undefined,
  contractRenewEndDate: undefined,
  residenceAddress: undefined,
  address: undefined,
  emergencyContactPerson: undefined,
  emergencyContact: undefined,
  email: undefined,
  fundAccount: undefined,
  titleOne: undefined,
  titleTwo: undefined,
  titleThree: undefined,
  certificateOne: undefined,
  certificateTwo: undefined,
  certificateThree: undefined,
  workStatus: undefined
})
const formRules = reactive({
  workNo: [{ required: true, message: '工号不能为空', trigger: 'blur' }],
  userName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '部门不能为空', trigger: 'blur' }],
  sex: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  workStatus: [{ required: true, message: '工作状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await EmployeeInfoApi.getEmployeeInfo(id)
      formData.value.deptId = Number(formData.value.deptId)
      // 计算年龄
      updateAge()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as EmployeeInfoVO
    if (formType.value === 'create') {
      await EmployeeInfoApi.createEmployeeInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await EmployeeInfoApi.updateEmployeeInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const handleIdCardInput = (value: string) => {
  if (value.length === 18) {
    const birthDate = value.substring(6, 14)
    const formattedBirthDate = `${birthDate.substring(0, 4)}-${birthDate.substring(
      4,
      6
    )}-${birthDate.substring(6, 8)}`
    formData.value.birthDay = formattedBirthDate
    // 身份证号变更时更新年龄
    updateAge()
  } else {
    formData.value.birthDay = undefined
    age.value = ''
  }
}

// 部门树变化
const handleDeptChange = async (data, node) => {
  formData.value.deptName = data.name
  formData.value.userId = undefined
  formData.value.userName = undefined
  formData.value.accountId = undefined
}

/** 打开目标设置 */
const openTargetSetting = () => {
  router.push({
    path: '/target/setting'
  })
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    userName: undefined,
    politicalStatus: undefined,
    bankAccount: undefined,
    major: undefined,
    remark: undefined,
    userId: undefined,
    workNo: undefined,
    deptId: undefined,
    deptName: undefined,
    occupation: undefined,
    staffType: undefined,
    employedTime: undefined,
    phone: undefined,
    idNumber: undefined,
    birthDay: undefined,
    sex: undefined,
    highestDegree: undefined,
    highestSchool: undefined,
    workStatusDesc: undefined,
    contractStartDate: undefined,
    trialEndDate: undefined,
    contractEndDate: undefined,
    contractRenewStartDate: undefined,
    contractRenewEndDate: undefined,
    residenceAddress: undefined,
    address: undefined,
    emergencyContactPerson: undefined,
    emergencyContact: undefined,
    email: undefined,
    fundAccount: undefined,
    titleOne: undefined,
    titleTwo: undefined,
    titleThree: undefined,
    certificateOne: undefined,
    certificateTwo: undefined,
    certificateThree: undefined,
    workStatus: undefined
  }
  age.value = ''
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.employee-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    
    .el-dialog__body {
      padding: 0;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 24px 32px;
      background: #f8fafc;
      border-top: 1px solid #e2e8f0;
    }
  }
}

.form-container {
  padding: 32px;
  background: #ffffff;
}

.modern-form {
  .form-section {
    margin-bottom: 40px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    .section-header {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      
      .section-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
    
    .el-row {
      padding: 24px;
      
      .el-col {
        margin-bottom: 8px;
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #475569;
      font-size: 14px;
    }
    
    .el-form-item__content {
      .modern-input {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
        
        .el-input__inner {
          font-size: 14px;
          color: #1e293b;
          
          &::placeholder {
            color: #94a3b8;
          }
        }
        
        &.disabled {
          .el-input__wrapper {
            background-color: #f8fafc;
            border-color: #e2e8f0;
            
            .el-input__inner {
              color: #64748b;
            }
          }
        }
      }
      
      .modern-select {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-date-picker {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-textarea {
        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          font-size: 14px;
          color: #1e293b;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .cancel-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid #d1d5db;
    color: #475569;
    background: white;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #94a3b8;
      color: #1e293b;
      background: #f8fafc;
    }
  }
  
  .submit-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
}

// 目标设置区域样式
.target-setting-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .target-setting-btn {
    width: fit-content;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }
    
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
      cursor: not-allowed;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
  
  .target-setting-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    color: #92400e;
    font-size: 14px;
    
    .warning-icon {
      width: 16px;
      height: 16px;
      color: #f59e0b;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .employee-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  
  .form-container {
    padding: 20px;
  }
  
  .modern-form {
    .form-section {
      .section-header {
        padding: 16px 20px;
        
        .section-icon {
          width: 28px;
          height: 28px;
          
          .svg-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        h3 {
          font-size: 15px;
        }
      }
      
      .el-row {
        padding: 20px;
      }
    }
  }
  
  .target-setting-container {
    .target-setting-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
