<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb-4">
      <el-button type="primary" plain @click="openForm('create')">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button type="warning" plain @click="handleImport">
        <Icon icon="ep:upload" class="mr-5px" /> 导入
      </el-button>
      <el-button type="success" plain @click="handleExport" :loading="exportLoading">
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="工号" align="center" prop="workNo" />
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="年龄" align="center">
        <template #default="scope">
          {{ calculateAge(scope.row.birthDay) }}
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center" prop="sex" />
      <el-table-column label="部门" align="center" prop="deptName" />
      <el-table-column
        label="入职时间"
        align="center"
        prop="employedTime"
        :formatter="dateFormatter2"
      />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openDetail(scope.row.id)">
            详情
          </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <EmployeeInfoForm ref="formRef" @success="getList" />
  <!-- 详情弹窗 -->
  <EmployeeInfoDetail ref="detailRef" />
  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="EmployeeInfoApi.upUrl"
    :fieldNames="{
      createField: 'createNames',
      updateField: 'updateNames',
      failureField: 'failureNames'
    }"
  />
</template>

<script setup lang="ts">
import {  dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { EmployeeInfoApi, EmployeeInfoVO } from '@/api/OA/employeeinfo'
import EmployeeInfoForm from './EmployeeInfoForm.vue'
import { getDictLabel, DICT_TYPE } from '@/utils/dict'
import EmployeeInfoDetail from './EmployeeInfoDetail.vue'

/** 员工信息 列表 */
defineOptions({ name: 'EmployeeInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<EmployeeInfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userName: undefined,
  politicalStatus: undefined,
  bankAccount: undefined,
  major: undefined,
  remark: undefined,
  createTime: [],
  userId: undefined,
  workNo: undefined,
  deptId: undefined,
  deptName: undefined,
  occupation: undefined,
  staffType: undefined,
  employedTime: [],
  phone: undefined,
  idNumber: undefined,
  birthDay: undefined,
  sex: undefined,
  highestDegree: undefined,
  highestSchool: undefined,
  workStatusDesc: undefined,
  contractStartDate: [],
  trialEndDate: [],
  contractEndDate: [],
  contractRenewStartDate: [],
  contractRenewEndDate: [],
  residenceAddress: undefined,
  address: undefined,
  emergencyContactPerson: undefined,
  emergencyContact: undefined,
  email: undefined,
  fundAccount: undefined,
  titleOne: undefined,
  titleTwo: undefined,
  titleThree: undefined,
  certificateOne: undefined,
  certificateTwo: undefined,
  certificateThree: undefined,
  workStatus: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 计算年龄的方法
const calculateAge = (birthDay: number | undefined) => {
  if (!birthDay) return '-'
  const birthDate = new Date(birthDay)
  const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const m = today.getMonth() - birthDate.getMonth()
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  return age + '岁'
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await EmployeeInfoApi.getEmployeeInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (id: number) => {
  detailRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EmployeeInfoApi.deleteEmployeeInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EmployeeInfoApi.exportEmployeeInfo(queryParams)
    download.excel(data, '员工信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
const importTemplate = async () => {
  try {
    const data = await EmployeeInfoApi.getImportTemplate()
    download.excel(data, '员工信息模板.xls')
  } catch {
  } finally {
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
