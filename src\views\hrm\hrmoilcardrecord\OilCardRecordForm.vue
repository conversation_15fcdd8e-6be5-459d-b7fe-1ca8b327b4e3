<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="油卡编号" prop="cardNo">
        <!-- <el-input v-model="formData.cardNo" placeholder="请输入油卡编号" /> -->
         <el-select v-model="formData.cardNo" placeholder="请选择油卡">
          <el-option label="1000113100006777545" value="1000113100006777545" />
          <el-option label="1000113100006777546" value="1000113100006777546" />
         </el-select>
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-select v-model="formData.plateNo" placeholder="请选择车牌号">
          <el-option label="沪DVA886" value="沪DVA886" />
          <el-option label="沪LC8037" value="沪LC8037" />
        </el-select>
      </el-form-item>
      <el-form-item label="使用日期" prop="useDate">
        <el-date-picker
          v-model="formData.useDate"
          type="date"
          value-format="x"
          placeholder="选择使用日期"
        />
      </el-form-item>
      <el-form-item label="当前余额" prop="currentBalance">
        <el-input type="number" v-model="formData.currentBalance" placeholder="请输入当前余额" />
      </el-form-item>
      <el-form-item label="充值日期" prop="rechargeDate">
        <el-date-picker
          v-model="formData.rechargeDate"
          type="date"
          value-format="x"
          placeholder="选择充值日期"
        />
      </el-form-item>
      <el-form-item label="充值金额" prop="rechargeAmount">
        <el-input type="number" v-model="formData.rechargeAmount" placeholder="请输入充值金额" />
      </el-form-item>
      <el-form-item label="单价" prop="unitPrice">
        <el-input type="number" v-model="formData.unitPrice" placeholder="请输入单价" />
      </el-form-item>
      <el-form-item label="升数" prop="liters">
        <el-input type="number" v-model="formData.liters" placeholder="请输入升数" />
      </el-form-item>
      <el-form-item label="使用金额" prop="useAmount">
        <el-input type="number" v-model="formData.useAmount" placeholder="请输入使用金额" />
      </el-form-item>
      <el-form-item label="剩余金额" prop="remainingAmount">
        <el-input type="number" v-model="formData.remainingAmount" placeholder="请输入剩余金额" />
      </el-form-item>
      <el-form-item label="上期公里数" prop="lastMileage">
        <el-input type="number" v-model="formData.lastMileage" placeholder="请输入上期公里数" />
      </el-form-item>
      <el-form-item label="本期公里数" prop="currentMileage">
        <el-input type="number" v-model="formData.currentMileage" placeholder="请输入本期公里数" />
      </el-form-item>
      <el-form-item label="行驶公里数" prop="drivingMileage">
        <el-input type="number" v-model="formData.drivingMileage" placeholder="请输入行驶公里数" />
      </el-form-item>
      <el-form-item label="平均油耗" prop="avgFuelConsumption">
        <el-input type="number" v-model="formData.avgFuelConsumption" placeholder="请输入平均油耗" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { OilCardRecordApi, OilCardRecordVO } from '@/api/hrm/hrmoilcardrecord'

/** 油卡记录 表单 */
defineOptions({ name: 'OilCardRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  cardNo: undefined,
  plateNo: undefined,
  useDate: undefined,
  currentBalance: undefined,
  rechargeDate: undefined,
  rechargeAmount: undefined,
  unitPrice: undefined,
  liters: undefined,
  useAmount: undefined,
  remainingAmount: undefined,
  lastMileage: undefined,
  currentMileage: undefined,
  drivingMileage: undefined,
  avgFuelConsumption: undefined,
})
const formRules = reactive({
  cardNo: [{ required: true, message: '油卡编号不能为空', trigger: 'blur' }],
  plateNo: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
  useDate: [{ required: true, message: '使用日期不能为空', trigger: 'blur' }],
  currentBalance: [{ required: true, message: '当前余额不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OilCardRecordApi.getOilCardRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OilCardRecordVO
    if (formType.value === 'create') {
      await OilCardRecordApi.createOilCardRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await OilCardRecordApi.updateOilCardRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    cardNo: undefined,
    plateNo: undefined,
    useDate: undefined,
    currentBalance: undefined,
    rechargeDate: undefined,
    rechargeAmount: undefined,
    unitPrice: undefined,
    liters: undefined,
    useAmount: undefined,
    remainingAmount: undefined,
    lastMileage: undefined,
    currentMileage: undefined,
    drivingMileage: undefined,
    avgFuelConsumption: undefined,
  }
  formRef.value?.resetFields()
}
</script>