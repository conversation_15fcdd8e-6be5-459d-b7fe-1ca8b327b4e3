<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="油卡编号" prop="cardNo">
        <el-input
          v-model="queryParams.cardNo"
          placeholder="请输入油卡编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="使用日期" prop="useDate">
        <el-date-picker
          v-model="queryParams.useDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="当前余额" prop="currentBalance">
        <el-input
          v-model="queryParams.currentBalance"
          placeholder="请输入当前余额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="充值日期" prop="rechargeDate">
        <el-date-picker
          v-model="queryParams.rechargeDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="充值金额" prop="rechargeAmount">
        <el-input
          v-model="queryParams.rechargeAmount"
          placeholder="请输入充值金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="单价" prop="unitPrice">
        <el-input
          v-model="queryParams.unitPrice"
          placeholder="请输入单价"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="升数" prop="liters">
        <el-input
          v-model="queryParams.liters"
          placeholder="请输入升数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="使用金额" prop="useAmount">
        <el-input
          v-model="queryParams.useAmount"
          placeholder="请输入使用金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="剩余金额" prop="remainingAmount">
        <el-input
          v-model="queryParams.remainingAmount"
          placeholder="请输入剩余金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="上期公里数" prop="lastMileage">
        <el-input
          v-model="queryParams.lastMileage"
          placeholder="请输入上期公里数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="本期公里数" prop="currentMileage">
        <el-input
          v-model="queryParams.currentMileage"
          placeholder="请输入本期公里数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="行驶公里数" prop="drivingMileage">
        <el-input
          v-model="queryParams.drivingMileage"
          placeholder="请输入行驶公里数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="平均油耗" prop="avgFuelConsumption">
        <el-input
          v-model="queryParams.avgFuelConsumption"
          placeholder="请输入平均油耗"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hrm:oil-card-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hrm:oil-card-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="油卡编号" align="center" prop="cardNo" />
      <el-table-column label="车牌号" align="center" prop="plateNo" />
      <el-table-column
        label="使用日期"
        align="center"
        prop="useDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="当前余额" align="center" prop="currentBalance" />
      <el-table-column
        label="充值日期"
        align="center"
        prop="rechargeDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="充值金额" align="center" prop="rechargeAmount" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="升数" align="center" prop="liters" />
      <el-table-column label="使用金额" align="center" prop="useAmount" />
      <el-table-column label="剩余金额" align="center" prop="remainingAmount" />
      <el-table-column label="上期公里数" align="center" prop="lastMileage" />
      <el-table-column label="本期公里数" align="center" prop="currentMileage" />
      <el-table-column label="行驶公里数" align="center" prop="drivingMileage" />
      <el-table-column label="平均油耗" align="center" prop="avgFuelConsumption" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hrm:oil-card-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hrm:oil-card-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OilCardRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter,dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { OilCardRecordApi, OilCardRecordVO } from '@/api/hrm/hrmoilcardrecord'
import OilCardRecordForm from './OilCardRecordForm.vue'

/** 油卡记录 列表 */
defineOptions({ name: 'HrmOilCardRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OilCardRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  cardNo: undefined,
  plateNo: undefined,
  useDate: [],
  currentBalance: undefined,
  rechargeDate: [],
  rechargeAmount: undefined,
  unitPrice: undefined,
  liters: undefined,
  useAmount: undefined,
  remainingAmount: undefined,
  lastMileage: undefined,
  currentMileage: undefined,
  drivingMileage: undefined,
  avgFuelConsumption: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OilCardRecordApi.getOilCardRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OilCardRecordApi.deleteOilCardRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OilCardRecordApi.exportOilCardRecord(queryParams)
    download.excel(data, '油卡记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>