<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="车牌" prop="plateNo">
        <!-- <el-input v-model="formData.plateNo" placeholder="请输入车牌" /> -->
        <el-select v-model="formData.plateNo" placeholder="请选择">
          <el-option label="沪DVA886" value="沪DVA886" />
          <el-option label="沪LC8037" value="沪LC8037" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="司机ID" prop="driverId">
        <el-input v-model="formData.driverId" placeholder="请输入司机ID" />
      </el-form-item> -->
      <el-form-item label="司机" prop="driverName">
        <el-input v-model="formData.driverName" placeholder="请输入司机名称" />
      </el-form-item>
      <el-form-item label="日期时间" prop="dateTime">
        <el-date-picker
          v-model="formData.dateTime"
          type="date"
          value-format="x"
          placeholder="选择日期 时间"
        />
      </el-form-item>
      <el-form-item label="上午下午" prop="period">
        <!-- <el-input v-model="formData.period" placeholder="请输入上午或下午" /> -->
        <el-select v-model="formData.period" placeholder="请选择">
          <el-option label="上午" value="上午" />
          <el-option label="上午" value="下午" />
          <el-option label="全天" value="全天" />
        </el-select>
      </el-form-item>
      <el-form-item label="使用人" prop="userId">
        <!-- <el-input v-model="formData.userId" placeholder="请输入使用人" /> -->
        <!-- multiple -->
        <el-select
          filterable
          ref="userRef"
          v-model="formData.userId"
          placeholder="请先选择使用人"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="使用人姓名" prop="userName">
        <el-input v-model="formData.userName" placeholder="请输入使用人姓名" />
      </el-form-item> -->
      <el-form-item label="上车地点" prop="startLocation">
        <el-input v-model="formData.startLocation" placeholder="请输入上车地点" />
      </el-form-item>
      <el-form-item label="下车地点" prop="endLocation">
        <el-input v-model="formData.endLocation" placeholder="请输入下车地点" />
      </el-form-item>
      <el-form-item label="发车前里程数" prop="startMileage">
        <el-input
          type="number"
          :precision="0"
          v-model="formData.startMileage"
          placeholder="请输入发车前里程数"
        />
      </el-form-item>
      <el-form-item label="返回后里程数" prop="endMileage">
        <el-input
          type="number"
          :precision="0"
          v-model="formData.endMileage"
          placeholder="请输入返回后里程数"
        />
      </el-form-item>
      <el-form-item label="用车里程数" prop="tripMileage">
        <el-input
          type="number"
          :precision="0"
          v-model="formData.tripMileage"
          placeholder="请输入用车里程数"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { VehicleTripRecordApi, VehicleTripRecordVO } from '@/api/hrm/hrmvehicletriprecord'
import * as UserApi from '@/api/system/user'

/** 车辆行程记录单 表单 */
defineOptions({ name: 'VehicleTripRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  plateNo: undefined,
  driverId: undefined,
  driverName: undefined,
  dateTime: undefined,
  period: undefined,
  userId: undefined,
  userName: undefined,
  startLocation: undefined,
  endLocation: undefined,
  startMileage: undefined,
  endMileage: undefined,
  tripMileage: undefined,
  remark: undefined
})
const formRules = reactive({
  plateNo: [{ required: true, message: '车牌不能为空', trigger: 'blur' }],
  dateTime: [{ required: true, message: '日期 时间不能为空', trigger: 'blur' }],
  period: [{ required: true, message: '上午或下午不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '使用人不能为空', trigger: 'blur' }],
  // userName: [{ required: true, message: '使用人姓名不能为空', trigger: 'blur' }],
  startLocation: [{ required: true, message: '上车地点不能为空', trigger: 'blur' }],
  endLocation: [{ required: true, message: '下车地点不能为空', trigger: 'blur' }],
  startMileage: [{ required: true, message: '发车前里程数不能为空', trigger: 'blur' }],
  endMileage: [{ required: true, message: '返回后里程数不能为空', trigger: 'blur' }],
  tripMileage: [{ required: true, message: '用车里程数不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  userList.value = await UserApi.getSimpleUserList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await VehicleTripRecordApi.getVehicleTripRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const userRef = ref()
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as VehicleTripRecordVO
    data.userName = userRef.value.currentPlaceholder
    if (formType.value === 'create') {
      await VehicleTripRecordApi.createVehicleTripRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await VehicleTripRecordApi.updateVehicleTripRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    plateNo: undefined,
    driverId: undefined,
    driverName: undefined,
    dateTime: undefined,
    period: undefined,
    userId: undefined,
    userName: undefined,
    startLocation: undefined,
    endLocation: undefined,
    startMileage: undefined,
    endMileage: undefined,
    tripMileage: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
