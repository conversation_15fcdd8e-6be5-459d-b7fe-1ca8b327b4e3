<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="车牌" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="司机" prop="driverId">
        <el-input
          v-model="queryParams.driverId"
          placeholder="请输入司机ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="司机名称" prop="driverName">
        <el-input
          v-model="queryParams.driverName"
          placeholder="请输入司机名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="日期时间" prop="dateTime">
        <el-date-picker
          v-model="queryParams.dateTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="上午或下午" prop="period">
        <el-input
          v-model="queryParams.period"
          placeholder="请输入上午或下午"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="使用人" prop="userId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.userId"
          placeholder="请先选择使用人"
          value-key="id"
          lable-key="nickname"
          @change="getList"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="使用人姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入使用人姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="上车地点" prop="startLocation">
        <el-input
          v-model="queryParams.startLocation"
          placeholder="请输入上车地点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="下车地点" prop="endLocation">
        <el-input
          v-model="queryParams.endLocation"
          placeholder="请输入下车地点"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="发车前里程数" prop="startMileage">
        <el-input
          v-model="queryParams.startMileage"
          placeholder="请输入发车前里程数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="返回后里程数" prop="endMileage">
        <el-input
          v-model="queryParams.endMileage"
          placeholder="请输入返回后里程数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用车里程数" prop="tripMileage">
        <el-input
          v-model="queryParams.tripMileage"
          placeholder="请输入用车里程数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hrm:vehicle-trip-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hrm:vehicle-trip-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="车牌" align="center" prop="plateNo" />
      <!-- <el-table-column label="司机ID" align="center" prop="driverId" /> -->
      <el-table-column label="司机名称" align="center" prop="driverName" />
      <el-table-column
        label="日期时间"
        align="center"
        prop="dateTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="上午或下午" align="center" prop="period" />
      <el-table-column label="使用人" align="center" prop="userName" />
      <!-- <el-table-column label="使用人姓名" align="center" prop="userName" /> -->
      <el-table-column label="上车地点" align="center" prop="startLocation" />
      <el-table-column label="下车地点" align="center" prop="endLocation" />
      <el-table-column label="发车前里程数" align="center" prop="startMileage" />
      <el-table-column label="返回后里程数" align="center" prop="endMileage" />
      <el-table-column label="用车里程数" align="center" prop="tripMileage" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hrm:vehicle-trip-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hrm:vehicle-trip-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <VehicleTripRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter,dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { VehicleTripRecordApi, VehicleTripRecordVO } from '@/api/hrm/hrmvehicletriprecord'
import VehicleTripRecordForm from './VehicleTripRecordForm.vue'
import * as UserApi from '@/api/system/user'

/** 车辆行程记录单 列表 */
defineOptions({ name: 'HrmVehicleTripRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const loading = ref(true) // 列表的加载中
const list = ref<VehicleTripRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  plateNo: undefined,
  driverId: undefined,
  driverName: undefined,
  dateTime: [],
  period: undefined,
  userId: undefined,
  userName: undefined,
  startLocation: undefined,
  endLocation: undefined,
  startMileage: undefined,
  endMileage: undefined,
  tripMileage: undefined,
  remark: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VehicleTripRecordApi.getVehicleTripRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await VehicleTripRecordApi.deleteVehicleTripRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await VehicleTripRecordApi.exportVehicleTripRecord(queryParams)
    download.excel(data, '车辆行程记录单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>