<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="发生时间" prop="date">
        <el-date-picker
          v-model="formData.date"
          type="datetime"
          value-format="x"
          placeholder="选择发生时间"
          style="width:100%"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型">
          <el-option label="违章" value="违章" />
          <el-option label="交通事故" value="交通事故" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="驾驶员ID" prop="driverId">
        <el-input v-model="formData.driverId" placeholder="请输入驾驶员ID" />
      </el-form-item> -->
      <el-form-item label="驾驶员姓名" prop="driverName">
        <el-input v-model="formData.driverName" placeholder="请输入驾驶员姓名" />
      </el-form-item>
      <!-- <el-form-item label="时间" prop="time">
        <el-date-picker
          v-model="formData.time"
          type="date"
          value-format="x"
          placeholder="选择时间"
        />
      </el-form-item> -->
      <el-form-item label="地点" prop="location">
        <el-input v-model="formData.location" placeholder="请输入地点" />
      </el-form-item>
      <el-form-item label="结果" prop="result">
        <el-input v-model="formData.result" placeholder="请输入结果" />
      </el-form-item>
      <el-form-item label="事情经过" prop="process">
        <el-input type="textarea" v-model="formData.process" placeholder="请输入事情经过" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { VehicleViolationAccidentApi, VehicleViolationAccidentVO } from '@/api/hrm/hrmvehicleviolationaccident'

/** 车辆违章事故记录 表单 */
defineOptions({ name: 'VehicleViolationAccidentForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  date: undefined,
  type: undefined,
  driverId: undefined,
  driverName: undefined,
  time: undefined,
  location: undefined,
  result: undefined,
  process: undefined,
  remark: undefined,
})
const formRules = reactive({
  date: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  driverName: [{ required: true, message: '驾驶员姓名不能为空', trigger: 'blur' }],
  time: [{ required: true, message: '时间不能为空', trigger: 'blur' }],
  location: [{ required: true, message: '地点不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await VehicleViolationAccidentApi.getVehicleViolationAccident(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as VehicleViolationAccidentVO
    if (formType.value === 'create') {
      await VehicleViolationAccidentApi.createVehicleViolationAccident(data)
      message.success(t('common.createSuccess'))
    } else {
      await VehicleViolationAccidentApi.updateVehicleViolationAccident(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    date: undefined,
    type: undefined,
    driverId: undefined,
    driverName: undefined,
    time: undefined,
    location: undefined,
    result: undefined,
    process: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>