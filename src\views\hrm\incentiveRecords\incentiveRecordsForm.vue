<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户ID" prop="userId">
        <el-select
          @change="changeUserName"
          v-model="formData.userId"
          clearable
          style="width: 100%"
          placeholder="请输入用户"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="奖惩类型" prop="incentiveType">
        <el-select v-model="formData.incentiveType" placeholder="请选择奖惩类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INCENTIVE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="奖惩项目" prop="incentiveItem">
        <el-select v-model="formData.incentiveItem" placeholder="请选择奖惩项目">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INCENTIVE_ITEM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="奖惩时间" prop="incentiveTime">
        <el-date-picker
          v-model="formData.incentiveTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择奖惩时间"
        />
      </el-form-item>
      <el-form-item label="奖惩金额" prop="incentiveAmount">
        <el-input v-model="formData.incentiveAmount" placeholder="请输入奖惩金额" />
      </el-form-item>
      <el-form-item label="相关文件" prop="attachId">
        <UploadFile v-model="formData.attachId" :file-size="5120" :limit="5" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IncentiveRecordsApi, IncentiveRecordsVO } from '@/api/hrm/incentiveRecords'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 奖惩记录 表单 */
defineOptions({ name: 'IncentiveRecordsForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<any>([]) // 用户列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  userId: undefined,
  userName: undefined,
  incentiveType: undefined,
  incentiveItem: undefined,
  incentiveTime: undefined,
  incentiveAmount: undefined,
  attachId: undefined,
  remark: undefined,
  sortNo: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  incentiveType: [{ required: true, message: '奖惩类型不能为空', trigger: 'change' }],
  incentiveItem: [{ required: true, message: '奖惩项目不能为空', trigger: 'blur' }],
  incentiveAmount: [{ required: true, message: '奖惩金额不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await IncentiveRecordsApi.getIncentiveRecords(id)
    } finally {
      formLoading.value = false
    }
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IncentiveRecordsVO
    if (formType.value === 'create') {
      await IncentiveRecordsApi.createIncentiveRecords(data)
      message.success(t('common.createSuccess'))
    } else {
      await IncentiveRecordsApi.updateIncentiveRecords(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeUserName = (val) => {
  formData.value.userName = userList.value.find((item) => item.id === val).nickname
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    userId: undefined,
    userName: undefined,
    incentiveType: undefined,
    incentiveItem: undefined,
    incentiveTime: undefined,
    incentiveAmount: undefined,
    attachId: undefined,
    remark: undefined,
    sortNo: undefined
  }
  formRef.value?.resetFields()
}
</script>
