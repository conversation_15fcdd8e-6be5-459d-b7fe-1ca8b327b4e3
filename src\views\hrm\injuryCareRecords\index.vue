<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="关怀人员" prop="userId">
        <el-select
          v-model="queryParams.userId"
          placeholder="请输入关怀人员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关怀类型" prop="careType">
        <el-select
          v-model="queryParams.careType"
          placeholder="请选择关怀类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CARE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关怀日期" prop="careTime">
        <el-date-picker
          v-model="queryParams.careTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="关怀主题" align="center" prop="subject" />
      <el-table-column label="关怀人员" align="center" prop="userName" />
      <el-table-column label="关怀类型" align="center" prop="careType">
        <template #default="scope">
          <span>{{ getDictLabel(DICT_TYPE.CARE_TYPE, scope.row.careType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参加关怀人员" align="center" prop="joinUser" />
      <el-table-column label="关怀结果" align="center" prop="careResult" />
      <el-table-column label="附件ID" align="center" prop="attachId">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachId" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="关怀日期" align="center" prop="careTime" />
      <el-table-column label="关怀金额" align="center" prop="careFunds" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InjuryCareRecordsForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InjuryCareRecordsApi, InjuryCareRecordsVO } from '@/api/hrm/injuryCareRecords'
import InjuryCareRecordsForm from './injuryCareRecordsForm.vue'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'

/** 员工关怀记录 列表 */
defineOptions({ name: 'HrmInjuryCareRecords' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const loading = ref(true) // 列表的加载中
const list = ref<InjuryCareRecordsVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  subject: undefined,
  userId: undefined,
  careType: undefined,
  joinUser: undefined,
  careResult: undefined,
  attachId: undefined,
  remark: undefined,
  applyTime: [],
  createTime: [],
  sortNo: undefined,
  userName: undefined,
  careTime: [],
  careFunds: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InjuryCareRecordsApi.getInjuryCareRecordsPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InjuryCareRecordsApi.deleteInjuryCareRecords(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InjuryCareRecordsApi.exportInjuryCareRecords(queryParams)
    download.excel(data, '员工关怀记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
