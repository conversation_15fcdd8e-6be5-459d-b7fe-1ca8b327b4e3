<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="关怀主题" prop="subject">
        <el-input v-model="formData.subject" placeholder="请输入关怀主题" />
      </el-form-item>
      <el-form-item label="关怀人员" prop="userId">
        <el-select
          @change="changeUserName"
          v-model="formData.userId"
          clearable
          style="width: 100%"
          placeholder="请输入用户"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关怀类型" prop="careType">
        <el-select v-model="formData.careType" placeholder="请选择关怀类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CARE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="参加关怀人员" prop="joinUser">
        <el-input
          type="textarea"
          :rows="4"
          v-model="formData.joinUser"
          placeholder="请输入参加关怀人员"
        />
      </el-form-item>
      <el-form-item label="关怀结果" prop="careResult">
        <el-input
          type="textarea"
          :rows="4"
          v-model="formData.careResult"
          placeholder="请输入关怀结果"
        />
      </el-form-item>
      <el-form-item label="附件" prop="attachId">
        <UploadFile v-model="formData.attachId" :file-size="5120" :limit="5" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="关怀日期" prop="careTime">
        <el-date-picker
          v-model="formData.careTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择关怀日期"
        />
      </el-form-item>
      <el-form-item label="关怀金额" prop="careFunds">
        <el-input v-model="formData.careFunds" placeholder="请输入关怀金额" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InjuryCareRecordsApi, InjuryCareRecordsVO } from '@/api/hrm/injuryCareRecords'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 员工关怀记录 表单 */
defineOptions({ name: 'InjuryCareRecordsForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<any>([]) // 用户列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  subject: undefined,
  userId: undefined,
  careType: undefined,
  joinUser: undefined,
  careResult: undefined,
  attachId: undefined,
  remark: undefined,
  applyTime: undefined,
  sortNo: undefined,
  userName: undefined,
  careTime: undefined,
  careFunds: undefined
})
const formRules = reactive({
  subject: [{ required: true, message: '关怀主题不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  careType: [{ required: true, message: '关怀类型不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InjuryCareRecordsApi.getInjuryCareRecords(id)
    } finally {
      formLoading.value = false
    }
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InjuryCareRecordsVO
    if (formType.value === 'create') {
      await InjuryCareRecordsApi.createInjuryCareRecords(data)
      message.success(t('common.createSuccess'))
    } else {
      await InjuryCareRecordsApi.updateInjuryCareRecords(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeUserName = (val) => {
  formData.value.userName = userList.value.find((item) => item.id === val).nickname
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    subject: undefined,
    userId: undefined,
    careType: undefined,
    joinUser: undefined,
    careResult: undefined,
    attachId: undefined,
    remark: undefined,
    applyTime: undefined,
    sortNo: undefined,
    userName: undefined,
    careTime: undefined,
    careFunds: undefined
  }
  formRef.value?.resetFields()
}
</script>
