<template>
  <Dialog width="75%" :title="dialogTitle" v-model="dialogVisible" class="licence-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:trophy" />
            </div>
            <h3>证照基本信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="证照名称" prop="name">
                <el-input 
                  v-model="formData.name" 
                  placeholder="请输入证照名称"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联人员" prop="userId">
                <el-select
                  v-model="formData.userId"
                  clearable
                  placeholder="请选择关联人员"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证照编号" prop="licenceCode">
                <el-input 
                  v-model="formData.licenceCode" 
                  placeholder="请输入证照编号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证照类型" prop="licenceType">
                <el-select 
                  v-model="formData.licenceType" 
                  placeholder="请选择证照类型"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.LICENCE_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发证机构" prop="notifiedBody">
                <el-input 
                  v-model="formData.notifiedBody" 
                  placeholder="请输入发证机构"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:clock" />
            </div>
            <h3>有效期信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="生效日期" prop="beginTime">
                <el-date-picker
                  v-model="formData.beginTime"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择生效日期"
                  class="modern-date-picker"
                  @change="validateLicenceDates"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终止日期" prop="endTime">
                <el-date-picker
                  v-model="formData.endTime"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择终止日期"
                  class="modern-date-picker"
                  @change="validateLicenceDates"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="licenceDuration">
              <div class="duration-info">
                <svg-icon name="ep:info-filled" class="info-icon" />
                <span>证照有效期：{{ licenceDuration }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 文件信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:folder" />
            </div>
            <h3>文件信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="相关文件" prop="attachId">
                <UploadFile v-model="formData.attachId" :file-size="5120" :limit="5" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <svg-icon name="ep:edit" />
            </div>
            <h3>备注信息</h3>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  v-model="formData.remark"
                  placeholder="请输入备注信息"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" class="cancel-btn">取 消</el-button>
        <el-button @click="submitForm" type="primary" :disabled="formLoading" class="submit-btn">
          <svg-icon name="ep:check" class="btn-icon" />
          确 定
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { LicenceInfoApi, LicenceInfoVO } from '@/api/hrm/licenceInfo/index'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 证照信息 表单 */
defineOptions({ name: 'LicenceInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const licenceDuration = ref<string>('') // 证照有效期

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  userId: undefined,
  licenceCode: undefined,
  notifiedBody: undefined,
  licenceType: undefined,
  sendToUser: undefined,
  beginTime: undefined,
  endTime: undefined,
  attachId: undefined,
  remark: undefined,
  sortNo: undefined,
  reminder: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '证照名称不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  licenceCode: [{ required: true, message: '证照编号不能为空', trigger: 'blur' }],
  licenceType: [{ required: true, message: '证照类型不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 验证证照日期
const validateLicenceDates = () => {
  if (formData.value.beginTime && formData.value.endTime) {
    const startDate = new Date(formData.value.beginTime)
    const endDate = new Date(formData.value.endTime)
    
    if (startDate >= endDate) {
      message.warning('生效日期必须早于终止日期')
      formData.value.endTime = undefined
      return
    }
    
    // 计算证照有效期
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    const years = Math.floor(diffDays / 365)
    const months = Math.floor((diffDays % 365) / 30)
    const days = diffDays % 30
    
    let duration = ''
    if (years > 0) duration += `${years}年`
    if (months > 0) duration += `${months}个月`
    if (days > 0) duration += `${days}天`
    
    licenceDuration.value = duration || '1天'
  } else {
    licenceDuration.value = ''
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LicenceInfoApi.getLicenceInfo(id)
      validateLicenceDates()
    } finally {
      formLoading.value = false
    }
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LicenceInfoVO
    if (formType.value === 'create') {
      await LicenceInfoApi.createLicenceInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await LicenceInfoApi.updateLicenceInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    userId: undefined,
    licenceCode: undefined,
    notifiedBody: undefined,
    licenceType: undefined,
    sendToUser: undefined,
    beginTime: undefined,
    endTime: undefined,
    attachId: undefined,
    remark: undefined,
    sortNo: undefined,
    reminder: undefined
  }
  licenceDuration.value = ''
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.licence-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    
    .el-dialog__body {
      padding: 0;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 24px 32px;
      background: #f8fafc;
      border-top: 1px solid #e2e8f0;
    }
  }
}

.form-container {
  padding: 32px;
  background: #ffffff;
}

.modern-form {
  .form-section {
    margin-bottom: 40px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    .section-header {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      
      .section-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }
    
    .el-row {
      padding: 24px;
      
      .el-col {
        margin-bottom: 8px;
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #475569;
      font-size: 14px;
    }
    
    .el-form-item__content {
      .modern-input {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
        
        .el-input__inner {
          font-size: 14px;
          color: #1e293b;
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
      
      .modern-select {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-date-picker {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-textarea {
        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          font-size: 14px;
          color: #1e293b;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
          
          &::placeholder {
            color: #94a3b8;
          }
        }
      }
    }
  }
}

.duration-info {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 8px;
  border: 1px solid #93c5fd;
  margin-top: 8px;
  
  .info-icon {
    width: 16px;
    height: 16px;
    color: #3b82f6;
    margin-right: 8px;
  }
  
  span {
    color: #1e40af;
    font-weight: 500;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .cancel-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid #d1d5db;
    color: #475569;
    background: white;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #94a3b8;
      color: #1e293b;
      background: #f8fafc;
    }
  }
  
  .submit-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    &:disabled {
      background: #94a3b8;
      transform: none;
      box-shadow: none;
    }
    
    .btn-icon {
      width: 16px;
      height: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .licence-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  
  .form-container {
    padding: 20px;
  }
  
  .modern-form {
    .form-section {
      .section-header {
        padding: 16px 20px;
        
        .section-icon {
          width: 28px;
          height: 28px;
          
          .svg-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        h3 {
          font-size: 15px;
        }
      }
      
      .el-row {
        padding: 20px;
      }
    }
  }
}
</style>
