<template>
  <div class="licence-list-page">
    <!-- 搜索工作栏 -->
    <div class="search-section">
      <div class="search-header">
        <div class="search-icon">
          <svg-icon name="ep:search" />
        </div>
        <h3>搜索条件</h3>
      </div>
      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="100px"
        class="modern-search-form"
      >
        <el-form-item label="关联人员" prop="userId">
          <el-select
            v-model="queryParams.userId"
            placeholder="请选择关联人员"
            clearable
            @keyup.enter="handleQuery"
            class="modern-select"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="证照名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入证照名称"
            class="modern-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="证照类型" prop="licenceType">
          <el-select
            v-model="queryParams.licenceType"
            placeholder="请选择证照类型"
            clearable
            class="modern-select"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.LICENCE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发证机构" prop="notifiedBody">
          <el-input
            v-model="queryParams.notifiedBody"
            placeholder="请输入发证机构"
            class="modern-input"
            clearable
          />
        </el-form-item>
        <el-form-item label="生效时间" prop="beginTime">
          <el-date-picker
            v-model="queryParams.beginTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="modern-date-picker"
          />
        </el-form-item>
        <el-form-item label="终止时间" prop="endTime">
          <el-date-picker
            v-model="queryParams.endTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="modern-date-picker"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button @click="handleQuery" class="search-btn">
            <svg-icon name="ep:search" class="btn-icon" />
            搜索
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <svg-icon name="ep:refresh" class="btn-icon" />
            重置
          </el-button>
          <el-button @click="openForm('create')" class="add-btn">
            <svg-icon name="ep:plus" class="btn-icon" />
            新增证照
          </el-button>
          <el-button @click="handleExport" :loading="exportLoading" class="export-btn">
            <svg-icon name="ep:download" class="btn-icon" />
            导出数据
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表区域 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-title">
          <div class="title-icon">
            <svg-icon name="ep:trophy" />
          </div>
          <h3>证照信息列表</h3>
          <span class="record-count">共 {{ total }} 条记录</span>
        </div>
        <div class="table-actions">
          <el-button @click="getList" class="refresh-btn" :loading="loading">
            <svg-icon name="ep:refresh" class="btn-icon" />
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="table-container">
        <el-table 
          v-loading="loading" 
          :data="list" 
          :stripe="true" 
          :show-overflow-tooltip="true"
          class="modern-table"
          :header-cell-style="{ background: '#f8fafc', color: '#475569', fontWeight: '600' }"
        >
          <el-table-column label="证照名称" align="center" prop="name" min-width="120">
            <template #default="scope">
              <div class="licence-name">
                <svg-icon name="ep:trophy" class="name-icon" />
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="关联人员" align="center" prop="userId" min-width="100">
            <template #default="scope">
              <div class="user-info">
                <div class="user-avatar">
                  <svg-icon name="ep:user" />
                </div>
                <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="证照编号" align="center" prop="licenceCode" min-width="120">
            <template #default="scope">
              <div class="licence-code">
                <svg-icon name="ep:document" class="code-icon" />
                <span>{{ scope.row.licenceCode }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发证机构" align="center" prop="notifiedBody" min-width="120" />
          <el-table-column label="证照类型" align="center" prop="licenceType" min-width="100">
            <template #default="scope">
              <el-tag 
                :type="getLicenceTypeTagType(scope.row.licenceType)"
                class="licence-type-tag"
              >
                {{ getDictLabel(DICT_TYPE.LICENCE_TYPE, scope.row.licenceType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="证照有效期" align="center" min-width="120">
            <template #default="scope">
              <div class="licence-duration">
                <div class="duration-date">
                  <div class="start-date">
                    <span class="date-label">生效</span>
                    <span class="date-value">{{ scope.row.beginTime }}</span>
                  </div>
                  <div class="duration-separator">至</div>
                  <div class="end-date">
                    <span class="date-label">终止</span>
                    <span class="date-value">{{ scope.row.endTime }}</span>
                  </div>
                </div>
                <div class="duration-status" :class="getDurationStatusClass(scope.row)">
                  {{ getDurationStatus(scope.row) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="相关文件" align="center" prop="attachId" min-width="100">
            <template #default="scope">
              <FileListPreview :fileUrl="scope.row.attachId" />
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.remark" class="remark-text">{{ scope.row.remark }}</span>
              <span v-else class="no-remark">-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            min-width="140"
          />
          <el-table-column label="操作" align="center" min-width="120" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button 
                  link 
                  type="primary" 
                  @click="openForm('update', scope.row.id)"
                  class="action-btn edit-btn"
                >
                  <svg-icon name="ep:edit" class="action-icon" />
                  编辑
                </el-button>
                <el-button 
                  link 
                  type="danger" 
                  @click="handleDelete(scope.row.id)"
                  class="action-btn delete-btn"
                >
                  <svg-icon name="ep:delete" class="action-icon" />
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 表单弹窗：添加/修改 -->
    <LicenceInfoForm ref="formRef" @success="getList" />
  </div>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { LicenceInfoApi, LicenceInfoVO } from '@/api/hrm/licenceInfo/index'
import LicenceInfoForm from './LicenceInfoForm.vue'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'

/** 证照信息 列表 */
defineOptions({ name: 'HrmLicenceInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const loading = ref(true) // 列表的加载中
const list = ref<LicenceInfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  userId: undefined,
  licenceCode: undefined,
  notifiedBody: undefined,
  licenceType: undefined,
  sendToUser: undefined,
  beginTime: [],
  endTime: [],
  attachId: undefined,
  remark: undefined,
  createTime: [],
  sortNo: undefined,
  reminder: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 获取证照类型标签样式
const getLicenceTypeTagType = (type: number): 'primary' | 'success' | 'warning' | 'info' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info'> = {
    1: 'primary',   // 职业资格证
    2: 'success',   // 技能等级证
    3: 'warning',   // 特种作业证
    4: 'info'       // 其他证照
  }
  return typeMap[type] || 'info'
}

// 获取证照有效期状态
const getDurationStatus = (row: any) => {
  if (!row.beginTime || !row.endTime) return '未设置'
  
  const now = new Date()
  const startDate = new Date(row.beginTime)
  const endDate = new Date(row.endTime)
  
  if (now < startDate) return '未生效'
  if (now > endDate) return '已过期'
  return '有效'
}

// 获取证照有效期状态样式类
const getDurationStatusClass = (row: any) => {
  const status = getDurationStatus(row)
  const classMap: Record<string, string> = {
    '未设置': 'status-unknown',
    '未生效': 'status-pending',
    '有效': 'status-active',
    '已过期': 'status-expired'
  }
  return classMap[status] || 'status-unknown'
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LicenceInfoApi.getLicenceInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LicenceInfoApi.deleteLicenceInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LicenceInfoApi.exportLicenceInfo(queryParams)
    download.excel(data, '证照信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style lang="scss" scoped>
.licence-list-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}

.search-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
  
  .search-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    
    .search-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      
      .svg-icon {
        width: 18px;
        height: 18px;
        color: white;
      }
    }
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }
  }
  
  .modern-search-form {
    padding: 24px;
    
    :deep(.el-form-item) {
      margin-bottom: 16px;
      margin-right: 24px;
      
      .el-form-item__label {
        font-weight: 500;
        color: #475569;
        font-size: 14px;
      }
      
      .modern-select {
        width: 200px;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-input {
        width: 200px;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      .modern-date-picker {
        width: 240px;
        
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #d1d5db;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
          }
          
          &.is-focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
      
      &.search-buttons {
        margin-right: 0;
        margin-bottom: 0;
        
        .search-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: white;
          border-radius: 8px;
          padding: 10px 20px;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
          
          .btn-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        .reset-btn {
          background: white;
          border: 1px solid #d1d5db;
          color: #475569;
          border-radius: 8px;
          padding: 10px 20px;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          
          &:hover {
            border-color: #94a3b8;
            color: #1e293b;
            background: #f8fafc;
          }
          
          .btn-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        .add-btn {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          border: none;
          color: white;
          border-radius: 8px;
          padding: 10px 20px;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
          }
          
          .btn-icon {
            width: 16px;
            height: 16px;
          }
        }
        
        .export-btn {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          border: none;
          color: white;
          border-radius: 8px;
          padding: 10px 20px;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
          }
          
          .btn-icon {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}

.table-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    
    .table-title {
      display: flex;
      align-items: center;
      
      .title-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .svg-icon {
          width: 18px;
          height: 18px;
          color: white;
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-right: 12px;
      }
      
      .record-count {
        color: #64748b;
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .refresh-btn {
      background: white;
      border: 1px solid #d1d5db;
      color: #475569;
      border-radius: 8px;
      padding: 8px 16px;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      
      &:hover {
        border-color: #667eea;
        color: #667eea;
        background: #f8fafc;
      }
      
      .btn-icon {
        width: 14px;
        height: 14px;
      }
    }
  }
  
  .table-container {
    .modern-table {
      :deep(.el-table__header) {
        th {
          background: #f8fafc !important;
          color: #475569 !important;
          font-weight: 600 !important;
          border-bottom: 1px solid #e2e8f0;
        }
      }
      
      :deep(.el-table__row) {
        &:hover {
          background-color: #f1f5f9 !important;
        }
      }
      
      :deep(.el-table__cell) {
        border-bottom: 1px solid #f1f5f9;
        padding: 16px 8px;
      }
    }
    
    .licence-name {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .name-icon {
        width: 16px;
        height: 16px;
        color: #f59e0b;
      }
      
      span {
        font-weight: 500;
        color: #1e293b;
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .user-avatar {
        width: 24px;
        height: 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .svg-icon {
          width: 14px;
          height: 14px;
          color: white;
        }
      }
      
      span {
        font-weight: 500;
        color: #1e293b;
      }
    }
    
    .licence-code {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .code-icon {
        width: 16px;
        height: 16px;
        color: #667eea;
      }
      
      span {
        font-weight: 500;
        color: #1e293b;
      }
    }
    
    .licence-type-tag {
      border-radius: 6px;
      font-weight: 500;
    }
    
    .licence-duration {
      .duration-date {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-bottom: 8px;
        
        .start-date, .end-date {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .date-label {
            font-size: 12px;
            color: #64748b;
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            min-width: 32px;
            text-align: center;
          }
          
          .date-value {
            font-size: 13px;
            color: #1e293b;
            font-weight: 500;
          }
        }
        
        .duration-separator {
          font-size: 12px;
          color: #94a3b8;
          text-align: center;
          margin: 2px 0;
        }
      }
      
      .duration-status {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        text-align: center;
        font-weight: 500;
        
        &.status-unknown {
          background: #f1f5f9;
          color: #64748b;
        }
        
        &.status-pending {
          background: #fef3c7;
          color: #d97706;
        }
        
        &.status-active {
          background: #d1fae5;
          color: #059669;
        }
        
        &.status-expired {
          background: #fee2e2;
          color: #dc2626;
        }
      }
    }
    
    .remark-text {
      color: #475569;
      font-size: 13px;
    }
    
    .no-remark {
      color: #94a3b8;
      font-style: italic;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;
      
      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        
        .action-icon {
          width: 14px;
          height: 14px;
        }
        
        &.edit-btn {
          color: #667eea;
          
          &:hover {
            background: #eff6ff;
            color: #3730a3;
          }
        }
        
        &.delete-btn {
          color: #ef4444;
          
          &:hover {
            background: #fef2f2;
            color: #dc2626;
          }
        }
      }
    }
  }
  
  .pagination-container {
    padding: 20px 24px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .licence-list-page {
    padding: 16px;
  }
  
  .search-section {
    .modern-search-form {
      padding: 16px;
      
      :deep(.el-form-item) {
        margin-right: 16px;
        
        .modern-select,
        .modern-input {
          width: 160px;
        }
        
        .modern-date-picker {
          width: 200px;
        }
        
        &.search-buttons {
          .search-btn,
          .reset-btn,
          .add-btn,
          .export-btn {
            padding: 8px 16px;
            font-size: 13px;
            
            .btn-icon {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
  }
  
  .table-section {
    .table-header {
      padding: 16px 20px;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      .table-title {
        h3 {
          font-size: 15px;
        }
        
        .record-count {
          font-size: 13px;
        }
      }
    }
    
    .table-container {
      overflow-x: auto;
      
      .modern-table {
        min-width: 800px;
      }
    }
  }
}
</style>
