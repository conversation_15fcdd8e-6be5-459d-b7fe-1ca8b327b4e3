<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="申领标题" prop="title">
        <div>{{ formData.title }}</div>
      </el-form-item>
      <el-form-item label="物资名称" prop="suppliesName">
        <div>{{ formData.suppliesName }}</div>
      </el-form-item>
      <el-form-item label="申领数量" prop="quantity">
        <div>{{ formData.quantity }}</div>
      </el-form-item>
      <el-form-item label="使用人" prop="userName">
        <div>{{ formData.userName }}</div>
      </el-form-item>
      <el-form-item label="审批意见" prop="result">
        <el-radio-group v-model="formData.result">
          <el-radio-button value="同意">同意</el-radio-button>
          <el-radio-button value="不同意">不同意</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MaterialsRequestApi, MaterialsRequestVO } from '@/api/hrm/material/materialsRequest'

/** 物资申领 表单 */
defineOptions({ name: 'MaterialsRequestForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('物资审批') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: undefined,
  quantity: undefined,
  result: undefined,
  processInstanceId: undefined,
  remark: undefined,
  userId: undefined,
  userName: undefined,
  suppliesName: undefined,
  suppliesId: undefined
})
const formRules = reactive({
  result: [{ required: true, message: '审批意见不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  formData.value = row
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MaterialsRequestVO
    await MaterialsRequestApi.updateMaterialsRequest(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    quantity: undefined,
    result: undefined,
    processInstanceId: undefined,
    remark: undefined,
    userId: undefined,
    userName: undefined,
    suppliesName: undefined,
    suppliesId: undefined
  }
  formRef.value?.resetFields()
}
</script>
