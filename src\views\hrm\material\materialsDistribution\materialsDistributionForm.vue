<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="申领标题" prop="title">
        <div>{{ formData.title }}</div>
      </el-form-item>
      <el-form-item label="物资名称" prop="suppliesName">
        <div>{{ formData.suppliesName }}</div>
      </el-form-item>
      <el-form-item label="待发放" prop="count">
        <div>{{ formData.count }}</div>
      </el-form-item>
      <el-form-item label="使用人" prop="userName">
        <div>{{ formData.userName }}</div>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item label="发放数量" prop="quantity">
        <el-input v-model="formData.quantity"></el-input>
      </el-form-item>
      <el-form-item label="领取人" prop="getUserId">
        <el-select
          @change="changeUserName"
          v-model="formData.getUserId"
          clearable
          style="width: 100%"
          placeholder="请输入用户"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {
  MaterialsDistributionApi,
  MaterialsDistributionVO
} from '@/api/hrm/material/materialsDistribution'
import * as UserApi from '@/api/system/user'

/** 物资发放 表单 */
defineOptions({ name: 'MaterialsDistributionForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('物资发放') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  suppliesId: undefined,
  quantity: undefined,
  remark: undefined,
  approvalQuantity: undefined,
  getUserId: undefined,
  getUserName: undefined,
  title: undefined,
  suppliesName: undefined,
  count: undefined,
  userName: undefined
})
const formRules = reactive({
  suppliesId: [{ required: true, message: '物资ID不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '发放数量不能为空', trigger: 'blur' }],
  getUserId: [{ required: true, message: '领取人id不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const userList = ref<any>([]) // 用户列表

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  formData.value = {
    title: row.title,
    suppliesName: row.suppliesName,
    suppliesId: row.suppliesId,
    userName: row.userName,
    count: row.quantity
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const changeUserName = (val) => {
  formData.value.getUserName = userList.value.find((item) => item.id === val).nickname
}
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MaterialsDistributionVO
    await MaterialsDistributionApi.createMaterialsDistribution(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    suppliesId: undefined,
    quantity: undefined,
    remark: undefined,
    approvalQuantity: undefined,
    getUserId: undefined,
    getUserName: undefined,
    title: undefined,
    suppliesName: undefined,
    count: undefined,
    userName: undefined
  }
  formRef.value?.resetFields()
}
</script>
