<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="物资名称" prop="materialsName">
        <el-input v-model="formData.materialsName" placeholder="请输入物资名称" />
      </el-form-item>
      <el-form-item label="物资编码" prop="materialsCode">
        <el-input v-model="formData.materialsCode" placeholder="请输入物资编码" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select
          v-model="formData.categoryId"
          placeholder="请选择分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <el-input v-model="formData.brand" placeholder="请输入品牌" />
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="formData.model" placeholder="请输入型号" />
      </el-form-item>
      <el-form-item label="数量" prop="quantity">
        <el-input v-model="formData.quantity" placeholder="请输入数量" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="所属部门" prop="ownDeptId">
        <el-tree-select
          ref="deptRef"
          v-model="formData.ownDeptId"
          :data="deptList"
          multiple
          default-expand-all
          check-strictly
          :props="defaultProps"
          show-checkbox
          node-key="id"
          placeholder="请选择所属部门"
        />
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-select v-model="formData.unit" placeholder="请选择单位" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.UNIT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {
  MaterialsManagementApi,
  MaterialsManagementVO
} from '@/api/hrm/material/materialsManagement'
import { DICT_TYPE, getIntDictOptions, getDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

/** 物资管理 表单 */
defineOptions({ name: 'MaterialsManagementForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const deptList = ref<Tree[]>([]) // 树形结构
const deptRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  materialsName: undefined,
  materialsCode: undefined,
  categoryId: undefined,
  brand: undefined,
  model: undefined,
  quantity: undefined,
  remark: undefined,
  sortNo: undefined,
  ownDeptId: undefined,
  ownDeptName: undefined,
  unit: undefined
})
const formRules = reactive({
  materialsName: [{ required: true, message: '物资名称不能为空', trigger: 'blur' }],
  materialsCode: [{ required: true, message: '物资编码不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
  unit: [{ required: true, message: '单位不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MaterialsManagementApi.getMaterialsManagement(id)
      if (formData.value.ownDeptId) {
        formData.value.ownDeptId = formData.value.ownDeptId.split(',').map(Number)
      }
    } finally {
      formLoading.value = false
    }
  }
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MaterialsManagementVO as any

    if (data.ownDeptId) {
      data.ownDeptId = data.ownDeptId.join(',')
      const checkedNodes = deptRef.value.getCheckedNodes()
      data.ownDeptName = checkedNodes.map((node) => node.name).join(',')
    }
    if (formType.value === 'create') {
      await MaterialsManagementApi.createMaterialsManagement(data)
      message.success(t('common.createSuccess'))
    } else {
      await MaterialsManagementApi.updateMaterialsManagement(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    materialsName: undefined,
    materialsCode: undefined,
    categoryId: undefined,
    brand: undefined,
    model: undefined,
    quantity: undefined,
    remark: undefined,
    sortNo: undefined,
    ownDeptId: undefined,
    ownDeptName: undefined,
    unit: undefined
  }
  formRef.value?.resetFields()
}
</script>
