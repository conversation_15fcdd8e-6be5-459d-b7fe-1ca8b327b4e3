<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="申领标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入申领标题" />
      </el-form-item>
      <el-form-item label="物资名称" prop="materialsName">
        <div>{{ formData.materialsName }}</div>
      </el-form-item>
      <el-form-item label="可用数量" prop="count">
        <div>{{ formData.count }}</div>
      </el-form-item>
      <el-form-item label="申领数量" prop="quantity">
        <el-input v-model="formData.quantity" placeholder="请输入申领数量" />
      </el-form-item>

      <el-form-item label="使用人id" prop="userId">
        <el-select
          @change="changeUserName"
          v-model="formData.userId"
          clearable
          style="width: 100%"
          placeholder="请输入用户"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MaterialsRequestApi, MaterialsRequestVO } from '@/api/hrm/material/materialsRequest'
import * as UserApi from '@/api/system/user'

/** 物资申领 表单 */
defineOptions({ name: 'MaterialsRequestForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<any>([]) // 用户列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('物资申请') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  title: undefined,
  quantity: undefined,
  result: '审批中',
  processInstanceId: undefined,
  remark: undefined,
  userId: undefined,
  userName: undefined,
  suppliesName: undefined,
  suppliesId: undefined
})
const formRules = reactive({
  title: [{ required: true, message: '申领标题不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '申领数量不能为空', trigger: 'blur' }],
  userName: [{ required: true, message: '使用人不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const changeUserName = (val) => {
  formData.value.userName = userList.value.find((item) => item.id === val).nickname
}

/** 打开弹窗 */
const open = async (row: any) => {
  dialogVisible.value = true
  resetForm()
  formData.value = {
    ...row,
    result: '审批中',
    suppliesId: row.id,
    suppliesName: row.materialsName,
    count: row.quantity,
    quantity: undefined,
    id: undefined
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MaterialsRequestVO

    await MaterialsRequestApi.createMaterialsRequest(data)
    message.success(t('common.createSuccess'))

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    quantity: undefined,
    result: '审批中',
    processInstanceId: undefined,
    remark: undefined,
    userId: undefined,
    userName: undefined,
    suppliesName: undefined,
    suppliesId: undefined
  }
  formRef.value?.resetFields()
}
</script>
