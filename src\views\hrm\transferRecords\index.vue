<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="调动人员" prop="userId">
        <el-select
          v-model="queryParams.userId"
          placeholder="请输入调动人员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="调动类型" prop="transferType">
        <el-select
          v-model="queryParams.transferType"
          placeholder="请选择调动类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRANSFER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="调动时间" prop="transferTime">
        <el-date-picker
          v-model="queryParams.transferTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="调动标题" align="center" prop="title" />
      <el-table-column label="调动人员" align="center" prop="userName" />
      <el-table-column label="调动类型" align="center" prop="transferType">
        <template #default="scope">
          <span>{{ getDictLabel(DICT_TYPE.TRANSFER_TYPE, scope.row.transferType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调动时间" align="center" prop="transferTime" />
      <el-table-column label="原公司名称" align="center" prop="compName" />
      <el-table-column label="调动后公司名称" align="center" prop="transferComp" />
      <el-table-column label="原部门名称" align="center" prop="deptName" />
      <el-table-column label="调动后部门名称" align="center" prop="transferDept" />
      <el-table-column label="原职级名称" align="center" prop="levelName" />
      <el-table-column label="调动后职级名称" align="center" prop="transferLevel" />
      <el-table-column label="调动条件" align="center" prop="transferCondition" />
      <el-table-column label="附件" align="center" prop="attachId">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachId" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="生效时间" align="center" prop="startTime" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TransferRecordsForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TransferRecordsApi, TransferRecordsVO } from '@/api/hrm/transferRecords'
import TransferRecordsForm from './transferRecordsForm.vue'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'

/** 员工调动记录 列表 */
defineOptions({ name: 'HrmTransferRecords' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const loading = ref(true) // 列表的加载中
const list = ref<TransferRecordsVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  sortNo: undefined,
  title: undefined,
  userId: undefined,
  transferType: undefined,
  transferTime: [],
  compName: undefined,
  transferComp: undefined,
  deptName: undefined,
  transferDept: undefined,
  levelName: undefined,
  transferLevel: undefined,
  transferCondition: undefined,
  attachId: undefined,
  remark: undefined,
  createTime: [],
  userName: undefined,
  startTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TransferRecordsApi.getTransferRecordsPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TransferRecordsApi.deleteTransferRecords(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TransferRecordsApi.exportTransferRecords(queryParams)
    download.excel(data, '员工调动记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
