<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="调动标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入调动标题" />
      </el-form-item>
      <el-form-item label="调动人员" prop="userId">
        <el-select
          @change="changeUserName"
          v-model="formData.userId"
          clearable
          style="width: 100%"
          placeholder="请输入调动人员"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="调动类型" prop="transferType">
        <el-select v-model="formData.transferType" placeholder="请选择调动类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRANSFER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="调动时间" prop="transferTime">
        <el-date-picker
          v-model="formData.transferTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择调动时间"
        />
      </el-form-item>
      <el-form-item label="原公司名称" prop="compName">
        <el-input v-model="formData.compName" placeholder="请输入原公司名称" />
      </el-form-item>
      <el-form-item label="调动后公司名称" prop="transferComp">
        <el-input v-model="formData.transferComp" placeholder="请输入调动后公司名称" />
      </el-form-item>
      <el-form-item label="原部门名称" prop="deptName">
        <el-input v-model="formData.deptName" placeholder="请输入原部门名称" />
      </el-form-item>
      <el-form-item label="调动后部门名称" prop="transferDept">
        <el-input v-model="formData.transferDept" placeholder="请输入调动后部门名称" />
      </el-form-item>
      <el-form-item label="原职级名称" prop="levelName">
        <el-input v-model="formData.levelName" placeholder="请输入原职级名称" />
      </el-form-item>
      <el-form-item label="调动后职级名称" prop="transferLevel">
        <el-input v-model="formData.transferLevel" placeholder="请输入调动后职级名称" />
      </el-form-item>
      <el-form-item label="调动条件" prop="transferCondition">
        <el-input v-model="formData.transferCondition" placeholder="请输入调动条件" />
      </el-form-item>
      <el-form-item label="附件" prop="attachId">
        <UploadFile v-model="formData.attachId" :file-size="5120" :limit="5" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="生效时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择生效时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TransferRecordsApi, TransferRecordsVO } from '@/api/hrm/transferRecords'
import * as UserApi from '@/api/system/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 员工调动记录 表单 */
defineOptions({ name: 'TransferRecordsForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<any>([]) // 用户列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  sortNo: undefined,
  title: undefined,
  userId: undefined,
  transferType: undefined,
  transferTime: undefined,
  compName: undefined,
  transferComp: undefined,
  deptName: undefined,
  transferDept: undefined,
  levelName: undefined,
  transferLevel: undefined,
  transferCondition: undefined,
  attachId: undefined,
  remark: undefined,
  userName: undefined,
  startTime: undefined
})
const formRules = reactive({
  sortNo: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '调动标题不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  transferType: [{ required: true, message: '调动类型不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TransferRecordsApi.getTransferRecords(id)
    } finally {
      formLoading.value = false
    }
  }
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TransferRecordsVO
    if (formType.value === 'create') {
      await TransferRecordsApi.createTransferRecords(data)
      message.success(t('common.createSuccess'))
    } else {
      await TransferRecordsApi.updateTransferRecords(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeUserName = (val) => {
  formData.value.userName = userList.value.find((item) => item.id === val).nickname
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    sortNo: undefined,
    title: undefined,
    userId: undefined,
    transferType: undefined,
    transferTime: undefined,
    compName: undefined,
    transferComp: undefined,
    deptName: undefined,
    transferDept: undefined,
    levelName: undefined,
    transferLevel: undefined,
    transferCondition: undefined,
    attachId: undefined,
    remark: undefined,
    userName: undefined,
    startTime: undefined
  }
  formRef.value?.resetFields()
}
</script>
