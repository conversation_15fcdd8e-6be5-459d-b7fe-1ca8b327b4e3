<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-08-28 10:44:36
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-20 17:51:29
 * @FilePath: \bkehs3.0-web\src\views\checkManagement\addEdit.vue
-->
<template>
  <ContentWrap>
    <el-page-header @back="goBack" :content="pageTitle"></el-page-header>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      style="margin-top: 40px"
    >
      <el-form-item label="检查企业" prop="companyId">
        <el-select v-model="formData.companyId" placeholder="请选择检查企业">
          <el-option
            v-for="row in inspectCompanyList"
            :key="row.id"
            :label="row.name"
            :value="row.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="检查类型" prop="inspectType">
        <el-select v-model="formData.inspectType" placeholder="请选择检查类型" multiple>
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INSPECT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否关联项目" prop="projectRelate" required>
        <el-radio-group v-model="formData.projectRelate" class="!w-100%">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="所属项目" prop="projectId" v-if="formData.projectRelate === '1'">
        <el-select
          class="!w-100%"
          ref="projectRef"
          filterable
          v-model="formData.projectId"
          placeholder="请选择所属项目"
          @change="projectChange"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="`【${item.contractCode}】${item.customerName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检查日期" prop="inspectTime">
        <el-date-picker
          v-model="formData.inspectTime"
          type="date"
          value-format="x"
          placeholder="请选择检查日期"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </el-form-item>

      <!-- <el-form-item label="隐患位置" prop="location">
        <el-input v-model="formData.location" placeholder="请输入隐患位置" />
      </el-form-item>
      <el-form-item label="隐患内容" prop="hideDangerContent">
        <el-input type="textarea" v-model="formData.hideDangerContent" placeholder="请输入隐患内容" />
      </el-form-item>
      <el-form-item label="隐患等级" prop="hideDangerLevel">
        <el-select v-model="formData.hideDangerLevel" placeholder="请选择隐患等级"  class="!w-100%">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.HIDE_DANGER_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="隐患涉及" prop="hideDangerCategory">
        <el-input type="textarea" v-model="formData.hideDangerCategory" placeholder="请输入隐患涉及" />
      </el-form-item>
      <el-form-item label="隐患图片" prop="hideDangerImages">
        <UploadImg v-model="formData.hideDangerImages" placeholder="请上传隐患图片" />
      </el-form-item>

      <el-form-item label="整改建议" prop="rectificationSuggestion">
        <el-input type="textarea" v-model="formData.rectificationSuggestion" placeholder="请输入整改建议" />
      </el-form-item>

      <el-form-item label="整改图片" prop="rectificationImages">
        <UploadImg v-model="formData.rectificationImages" placeholder="请上传隐患图片" />
      </el-form-item>

      <el-form-item label="法规条款	" prop="lawTerm">
        <el-input type="textarea" v-model="formData.lawTerm" placeholder="请输入法规条款" />
      </el-form-item>
       -->
      <el-form-item label="现场图片" prop="sitePhoto">
        <UploadFile v-model="formData.sitePhoto" :file-size="20" :limit="5" />
        <!-- <UploadImgs v-model="formData.sitePhoto" placeholder="请上传隐患图片" /> -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="检查项" name="inspectItem">
        <InspectItemForm ref="inspectItemFormRef" :task-id="formData.id" />
      </el-tab-pane>
    </el-tabs>

    <el-row style="justify-content: center; margin: 20px 0">
      <el-button @click="goBack">关 闭</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">提 交</el-button>
    </el-row>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { InspectApi, InpectActVO } from '@/api/inspect/index'
import { DICT_TYPE, getStrDictOptions, getDictLabel } from '@/utils/dict'
import { useRoute } from 'vue-router'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { InspectCompanyApi, InspectCompanyVO } from '@/api/pms/inspect'
import { useUserStore } from '@/store/modules/user'
import { getMyProjectPage } from '@/api/bpm/task'
import InspectItemForm from './components/InspectItemForm.vue'

const formLoading = ref(false)
const pageTitle = ref('')
const formRef = ref() // 表单 Ref
const formType = ref()
const subTabsName = ref('inspectItem')
const formData = ref({
  id: undefined,
  projectRelate: '0',
  companyId: undefined,
  projectId: undefined,
  projectName: undefined,
  inspectTime: undefined,
  inspectType: [],
  sitePhoto: undefined
})
const formRules = reactive({
  companyId: [{ required: true, message: '请选择检查企业', trigger: 'change' }],
  projectId: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  inspectTime: [{ required: true, message: '请选择检查时间', trigger: 'change' }],
  inspectType: [{ required: true, message: '请选择检查类型', trigger: 'change' }],
  sitePhoto: [{ required: true, message: '请上传现场照片', trigger: 'change' }]
  // location: [{ required: true, message: '请输入检查位置', trigger: 'blur' }],
  // hideDangerContent: [{ required: true, message: '请输入隐患内容', trigger: 'blur' }],
  // rectificationSuggestion: [{ required: true, message: '选择整改建议	', trigger: 'blur' }],
  // hideDangerLevel: [{ required: true, message: '请选择隐患等级', trigger: 'change' }],
  // hideDangerCategory: [{ required: true, message: '请输入隐患涉及	', trigger: 'blur' }],
  // hideDangerImages:[{ required: true, message: '请上传隐患图片', trigger: 'change' }],
  // rectificationImages:[{ required: true, message: '请上传整改图片', trigger: 'change' }],
  // lawTerm:[{ required: true, message: '请输入法规条款', trigger: 'blur' }]
})
const { push, currentRoute } = useRouter()
const { delView } = useTagsViewStore() // 视图操作
const goBack = () => {
  delView(unref(currentRoute))
  push('/inspect/list')
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InpectActVO
    data.projectRelate = data.projectRelate === '1' ? true : false
    if (data.projectRelate) {
      data.companyId = -1
    } else {
      data.projectId = -1
    }
    if (formType.value === 'create') {
      await InspectApi.createNewInspect(data)
      message.success(t('common.createSuccess'))
    } else {
      await InspectApi.updateInspect(data)
      message.success(t('common.updateSuccess'))
    }
    goBack()
    // 发送操作成功的事件
  } finally {
    formLoading.value = false
  }
}
const route = useRoute()
const getDetail = async () => {
  try {
    formData.value = await InspectApi.getInspectDetail(route?.query?.id)
    if (formData.value.projectRelate) {
      formData.value.projectRelate = '1'
      formData.value.companyId = null
    } else {
      formData.value.projectRelate = '0'
      formData.value.projectId = null
    }
  } finally {
    formLoading.value = false
  }
}
const inspectCompanyList = ref<any>([])
const userId = useUserStore().getUser.id // 当前登录的编号
const getInspectCompanyList = async () => {
  const param = {
    pageNum: 1,
    pageSize: 100
  }
  const data = await InspectCompanyApi.getInspectCompanyPage(param)
  inspectCompanyList.value = data.list
}
const projectList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await getMyProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2,
      userId: userId
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 86400000
}
const projectChange = (val: any) => {
  const selectedItem = projectList.value.find((item) => item.id === val)
  formData.value.projectName = selectedItem.projectName
}
onMounted(async () => {
  getInspectCompanyList()
  getProjectList()
  if (route?.query?.id) {
    formLoading.value = true
    pageTitle.value = '现场检查'
    getDetail()
  } else {
    formType.value = 'create'
    pageTitle.value = '新增现场检查'
  }
})
</script>
