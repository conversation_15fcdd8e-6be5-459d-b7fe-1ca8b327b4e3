<template>
<div class="containertt">
  <div class="topbar">现场检查</div>
  <div class="containerbox" v-if="currentTab===0">
    <div class="title">第一步：请输入您的姓名与手机号码</div>
    <div class="inputbox">
      <el-form :model="form" size="large">
        <el-form-item prop="name">
          <el-input placeholder="请输入姓名" v-model="form.name" />
        </el-form-item>
        <el-form-item  prop="name">
          <el-input placeholder="请输入手机号码" v-model="form.phone" />
        </el-form-item>
      </el-form>
      <div style="text-align:right">
        <el-button type="primary" @click="goCheckNext">
          下一步<Icon icon="ep:arrow-right"/>
        </el-button>
      </div>
    </div>
  </div>
  <div class="containerbox" v-if="currentTab===1">
    <div class="title">第二步：请填写检查内容</div>
    <div class="inputbox" style="background:#fff">
      <el-form :model="checkForm" size="large">
        <el-form-item prop="companyName" label="受检查企业：">
          {{ checkForm.companyName }}
        </el-form-item>
        <el-form-item prop="location" label="检查地址：">
          {{ checkForm.location }}
        </el-form-item>
        <el-form-item prop="inspectType" label="检查类型：">
          <el-checkbox-group v-model="checkForm.inspectItemsList" @change="inspectChange" :disabled="checkForm.status===0 || checkForm.status===2 ? false : true ">
            <el-checkbox v-for="row in inspectData" :label="row.value" :key="row.value">{{row.label}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeName"  type="border-card"  size="large">
        <el-tab-pane v-for="row in tabs" :label="row.label" :name="row.value" :key="row.value">
          <el-button type="primary" @click="addCheck" v-if="checkForm.status===0 || checkForm.status===2">
            <Icon icon="ep:plus" class="mr-5px" />添加检查
          </el-button>
          <el-table :data="row.inspectList" style="margin-top:10px" border>
            <el-table-column label="序号" align="center" type="index" width="60"/>
            <el-table-column label="检查内容" align="center" prop="inspectContent" min-width="120"/>
            <el-table-column label="隐患图片" align="center" min-width="120">
              <template #default="scope">
                <el-image 
                  v-for="item in scope.row.inspectImages && scope.row.inspectImages.split(',')" :key="item"
                  style="width: 60px; height: 60px"
                  :src="item" 
                  z-index="999"
                  :preview-src-list="item">
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="位置" align="center" prop="remark" min-width="120"/>
            <el-table-column label="备注图片" align="center" width="120">
              <template #default="scope">
                <el-image
                  v-for="item in scope.row.remarkImages && scope.row.remarkImages.split(',')" :key="item"
                  style="width: 60px; height: 60px"
                  :src="item" 
                  :preview-src-list="item">
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="62" fixed="right" v-if="checkForm.status===0 || checkForm.status===2">
              <template #default="scope">
                <el-button link type="danger" @click="onDelete(scope.row,scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div class="fixedBar" v-if="checkForm.status===0 || checkForm.status===2">
        <el-row :gutter="16">
          <el-col :span="10">
            <el-button type="primary" style="width:100%" @click="goBack"><Icon icon="ep:arrow-left"/>上一步</el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="success"  style="width:100%"  @click="handleSubmit"><Icon icon="ep:check"/>提交</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
  <div class="containerbox" v-if="currentTab===2">
    <div class="title">检查信息</div>
    <div class="inputbox">
      <el-form :model="inspectForm" label-width="100px" size="large">
        <el-form-item prop="inspectContent" label="隐患内容">
          <el-input type="textarea" v-model="inspectForm.inspectContent" placeholder="请输入隐患内容"></el-input>
        </el-form-item>
        <el-form-item prop="inspectImagesList" label="隐患图片" required>
          <upload-imgs ref="uploadRef1" v-model="inspectForm.inspectImagesList" :fileSize="100" width="100px" height="100px"/>
        </el-form-item>
        <el-form-item prop="remark" label="隐患位置">
          <el-input  type="textarea"  v-model="inspectForm.remark" placeholder="请输入隐患位置"></el-input>
        </el-form-item>
        <el-form-item prop="remarkImagesList" label="备注图片">
          <upload-imgs ref="uploadRef2" v-model="inspectForm.remarkImagesList" :fileSize="100" width="100px" height="100px" @click="isRemarkImagesUpload"/>
        </el-form-item>
      </el-form>
      <div style="text-align:center;margin:10px 0">
        <el-button @click="close">
          <Icon icon="ep:close" class="mr-5px" />
          关闭
        </el-button>
        <el-button type="primary" @click="save"><Icon icon="ep:check" class="mr-5px" />保存</el-button>
      </div>
    </div>
  </div>
</div>
</template>
<script setup>
import { InspectApi } from '@/api/inspect'
import * as DictDataApi from '@/api/system/dict/dict.data'
const message = useMessage() // 消息弹窗
const activeName = ref()
const { currentRoute } = useRouter()

const form = ref({
  name:undefined,
  phone:undefined
})
const checkForm = ref({})
const inspectForm = ref({})
const currentTab = ref()
onMounted(()=>{
  if(currentRoute.value.query.id){
    currentTab.value = 0
    if(sessionStorage.getItem('checkerInfo')){
      form.value = JSON.parse(sessionStorage.getItem('checkerInfo'))
      getTask()
    }
  }else{
    message.warning('参数错误，请联系管理员！') 
  }
})
const inspectData = ref([])
const getDictData = async() => {
  inspectData.value=[]
  tabs.value= []
  const params={
    pageNo:1,
    pageSize:10,
    dictType:'inspect_type'
  }
  const data = await DictDataApi.getDictDataPage(params)
  checkForm.value.inspectItemsList = []
  data.list.forEach(row=>{
    inspectData.value.unshift({
      label:row.label,
      value:row.value
    })
    if(checkForm.value.inspectItems.length===0){
      if(row.value==='daily'){
        checkForm.value.inspectItemsList.push(row.value)
        tabs.value.unshift(
          {
            label:row.label,
            value:row.value,
            inspectList:checkForm.value.inspectItems.find(item=>item.inspectType=='daily')?.inspectList || []
          }
        )
        activeName.value = row.value
      }
    }else{
      checkForm.value.inspectItems.forEach((row2,index)=>{
        checkForm.value.inspectItemsList.push(row2.inspectType)
        if(row2.inspectType==row.value){
          activeName.value = row.value
          tabs.value.unshift(
            {
              label:row.label,
              value:row.value,
              inspectList:row2.inspectList
            }
          )
        }
      })
    }
  })
  checkForm.value.inspectItemsList = [...new Set(checkForm.value.inspectItemsList)]
}
const isRemarkImagesUploaded = ref(true) 
const isRemarkImagesUpload = ()=>{
  isRemarkImagesUploaded.value = false
}
const goCheckNext = () => {
  if(!form.value.name){
    return message.error('请输入姓名')
  }if(!form.value.phone){
    return message.error('请输入手机号码')
  }
  getTask()
}
const getTask = async ()=>{
  const params={
    taskId:currentRoute.value.query.id,
    inspectorContact:form.value.phone
  }
  const data = await InspectApi.reviewInspectCheck(params)
  if(data){
    if(data){
      checkForm.value = data
      sessionStorage.setItem('checkerInfo',JSON.stringify(form.value))
      currentTab.value = 1
      getDictData()
    }else{
      return message.error('检查任务已提交！')
    }
  }else{
    return message.error('检查任务不存在！')
  }
}
const tabs = ref([])
const oldTabs = ref([])
const inspectChange = (e)=>{
  if (checkForm.value.inspectItemsList.length) {
    oldTabs.value = checkForm.value.inspectItemsList
  }
  tabs.value = []
  let tempArray = [...new Set(e)];
  tempArray.forEach(value=>{
    const label = inspectData.value.find(item=>item.value==value).label
    activeName.value = value
    const tempinspectList = checkForm.value.inspectItems.filter(item=>item.inspectType==value)[0]?.inspectList
    tabs.value.push({
      label:label,
      value:value,
      inspectList:tempinspectList
    })
  })  
}
const addCheck = ()=>{
  currentTab.value=2
  inspectForm.value.inspectType = activeName.value
  inspectForm.value.inspectorContact = form.value.phone
  inspectForm.value.inspector = form.value.name
  inspectForm.value.taskId = checkForm.value.id
}
const close = ()=>{
  currentTab.value=1
}
const goBack = ()=>{
  currentTab.value=0
}
const uploadRef1 = ref()
const uploadRef2 = ref()

const save = async()=>{
  if(!inspectForm.value?.inspectImagesList){
    return message.error('请上传检查图片')
  }else{
    if(!isRemarkImagesUploaded.value){
      if(!inspectForm.value?.remarkImagesList?.toString()){
        return message.error('备注图片正在上传中，请稍后提交')
      }
    }
    inspectForm.value.inspectImages = inspectForm.value?.inspectImagesList?.toString()
    inspectForm.value.remarkImages = inspectForm.value?.remarkImagesList?.toString()
    await InspectApi.saveInspectItem(inspectForm.value)
    message.success('保存成功')
    currentTab.value=1
    inspectForm.value={}
    getTask()
  }
}
const onDelete = async(row,index)=>{
  try {
    await message.delConfirm()
    // 发起删除
    await InspectApi.deleteInspectItem(row.id)
    message.success('删除成功')
    tabs.value.forEach(item=>{
      if(item.value==activeName.value){
        item.inspectList.splice(index,1)
      }
    })
  } catch {}
}


const handleSubmit = async()=>{
  
  if(!checkForm.value.inspectItemsList.length){
    return message.warning('请选择检查类型！')
  }else{
    checkForm.value.inspectItems.forEach((row,index)=>{
      if(!checkForm.value.inspectItemsList.includes(row.inspectType)){
        checkForm.value.inspectItems.splice(index,1)
      }else{
        row.inspector = form.value.name
        row.inspectorContact = form.value.phone
      }
    })
  }
  const data = {
    ...checkForm.value,
    status:1
  }
  await message.confirm('是否确定提交数据，提交后检查任务将结束！')  
  await InspectApi.submitTask(data)
  message.success('提交成功！');
  currentTab.value=1
  getTask()
}

</script>

<style lang="scss" scoped>
  .containertt{
    width:100%;
  .topbar{
    width: 100%;
    margin: 0 auto;
    background: repeating-linear-gradient(78deg,#536dfe,#0421c2);
    height: auto;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    padding:15px 0;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
  }
  .containerbox{
    background: repeating-linear-gradient(90deg, #d8e5ff, #d6f4fa);
    height: auto;
    padding:20px;
    margin:20px 0;
    font-size: 12px;
    .title{
      font-size: 16px;
      font-weight:bold;
      color:rgb(13, 128, 232);
    }
    .inputbox{
      margin: 20px 0;
      padding:10px;
      border-radius: 10px;
      height: 68vh;
      overflow-y:scroll;  
    }
  }
}
.fixedBar{
  position: fixed;
  width:100%;
  bottom:0;
  left:0;
  padding:10px;
  text-align: center;
  z-index: 999;
}
</style>