<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="检查内容" min-width="400">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.inspectContent`" :rules="formRules.inspectContent" class="mb-0px!">
            <el-input v-model="row.inspectContent"  placeholder="请输入检查内容" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="检查依据" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.inspectBase`" :rules="formRules.inspectBase" class="mb-0px!">
            <el-input v-model="row.inspectBase" placeholder="请输入检查依据" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="检查图片" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.inspectImages`" :rules="formRules.inspectImages" class="mb-0px!">
            <!-- <el-input v-model="row.inspectImages" placeholder="请输入检查图片" /> -->
            <UploadFile v-model="row.inspectImages" :file-size="20" :limit="5" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="隐患分类" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.hideDangerType`" :rules="formRules.hideDangerType" class="mb-0px!">
            <el-select v-model="row.hideDangerType" placeholder="请选择隐患分类">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.HIDE_DANGER_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="隐患等级" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.hideDangerLevel`" :rules="formRules.hideDangerLevel" class="mb-0px!">
            <el-select v-model="row.hideDangerLevel" placeholder="请选择隐患等级">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.HIDE_DANGER_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="隐患描述" min-width="400">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.hideDangerContent`" :rules="formRules.hideDangerContent" class="mb-0px!">
            <el-input v-model="row.hideDangerContent" placeholder="请输入隐患描述" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="整改建议" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.rectificationSuggestion`" :rules="formRules.rectificationSuggestion" class="mb-0px!">
            <el-input v-model="row.rectificationSuggestion" placeholder="请输入整改建议" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加检查项</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { InspectApi } from '@/api/inspect'
import { DICT_TYPE, getStrDictOptions,getDictLabel } from '@/utils/dict'

const props = defineProps<{
  taskId: undefined // 检查任务编号（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.taskId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await InspectApi.getInspectItemListByTaskId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    taskId: undefined,
    inspectType: undefined,
    inspectContent: undefined,
    inspectBase: undefined,
    inspectImages: undefined,
    remark: undefined,
    hideDangerType: undefined,
    hideDangerLevel: undefined,
    hideDangerContent: undefined,
    rectificationSuggestion: undefined,
  }
  row.taskId = props.taskId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>