<!--
 * @Description: 现场检查详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-10 15:11:12
 * @LastEditTime: 2025-01-08 15:42:01
-->

<template>
  <ContentWrap>
    <el-button type="primary" class="mb-4" @click="goBack">
      <el-icon class="mr-2"><Back /></el-icon> 返 回
    </el-button>
    <el-tabs v-model="subTabsName" type="border-card" v-if="inspectorCheckData.length">
      <block v-for="(row, index) in inspectorCheckData">
        <el-tab-pane :label="row.inspector" :key="index" :name="row.inspector + '_' + index">
          <el-table
            :data="row.list"
            :row-key="row.inspectType"
            border
            :span-method="objectSpanMethod"
          >
            <el-table-column label="#" align="center" type="index" width="50"></el-table-column>
            <el-table-column label="模块" align="center" prop="inspectTypeName" width="120" />
            <el-table-column label="检查内容" align="center" prop="inspectContent" width="300" />
            <el-table-column label="检查依据" align="center" prop="inspectBase" width="300" />
            <el-table-column label="检查方法" align="center" prop="inspectMethod" width="210" />
            <el-table-column label="检查结果" align="center" prop="checkResult" width="120">
              <template #default="scope">
                <el-radio-group v-model="scope.row.checkResult" v-if="scope.row.isEdit">
                  <el-radio label="符合">符合</el-radio>
                  <el-radio label="不符合">不符合</el-radio>
                  <el-radio label="不涉及">不涉及</el-radio>
                </el-radio-group>
                <div v-else>{{ scope.row.checkResult }}</div>
              </template>
            </el-table-column>
            <el-table-column label="检查描述" align="center" prop="remark" width="200">
              <template #default="scope">
                <el-input
                  type="textarea"
                  v-model="scope.row.remark"
                  placeholder="请输入检查描述"
                  v-if="scope.row.isEdit"
                ></el-input>
                <div v-else>{{ scope.row.remark }}</div>
              </template>
            </el-table-column>
            <el-table-column label="隐患图片" align="center" prop="inspectImages" width="180">
              <template #default="scope">
                <UploadFile
                  v-if="scope.row.isEdit"
                  :isShowTip="false"
                  v-model="scope.row.inspectImages"
                  :fileType="['png', 'jpg', 'jpeg']"
                />
                <FileListPreview v-else :fileUrl="scope.row.inspectImages" />
              </template>
            </el-table-column>
            <!-- <el-table-column label="整改前照片" align="center" prop="remarkImages" width="180">
              <template #default="scope">
                <UploadFile
                  v-if="scope.row.isEdit"
                  :isShowTip="false"
                  v-model="scope.row.remarkImages"
                  :fileType="['png', 'jpg', 'jpeg']"
                />
                <FileListPreview v-else :fileUrl="scope.row.remarkImages" />
              </template>
            </el-table-column> -->
            <el-table-column
              label="整改后照片"
              align="center"
              prop="rectificationImages"
              width="180"
            >
              <template #default="scope">
                <UploadFile
                  v-if="scope.row.isEdit"
                  :isShowTip="false"
                  v-model="scope.row.rectificationImages"
                  :fileType="['png', 'jpg', 'jpeg']"
                />
                <FileListPreview v-else :fileUrl="scope.row.rectificationImages" />
              </template>
            </el-table-column>
            <el-table-column
              label="整改建议"
              align="center"
              prop="rectificationSuggestion"
              width="200"
            >
              <template #default="scope">
                <el-input
                  type="textarea"
                  v-model="scope.row.rectificationSuggestion"
                  placeholder="请输入整改建议"
                  v-if="scope.row.isEdit"
                ></el-input>
                <div v-else>{{ scope.row.rectificationSuggestion }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="160" fixed="right">
              <template #default="scope">
                <el-button type="primary" @click="onEdit(scope.row)" v-if="!scope.row.isEdit">
                  编辑
                </el-button>
                <el-button type="success" @click="onSave(scope.row)" v-else>保存</el-button>
                <el-button type="danger" @click="onDelete(scope.row, index, scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </block>
    </el-tabs>
    <el-empty v-else />
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getDetail"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { InspectApi } from '@/api/inspect/index'
import { useRoute } from 'vue-router'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep } from 'lodash-es'

const message = useMessage() // 消息弹窗
const formLoading = ref(false)
const subTabsName = ref('')
const formData: any = ref({
  id: undefined,
  projectRelate: '0',
  companyId: undefined,
  projectId: undefined,
  projectName: undefined,
  inspectTime: undefined,
  inspectType: [],
  sitePhoto: undefined
})
const { push, currentRoute } = useRouter()
const { delView } = useTagsViewStore() // 视图操作
const goBack = () => {
  delView(unref(currentRoute))
  push('/inspect/list')
}
const route = useRoute()
const inspectorCheckData = ref<any>([])

const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  companyId: undefined
})
const total = ref(0)

const getDetail = async () => {
  try {
    const data = await InspectApi.getInspectOnsiteDetail(queryParams)
    formData.value = data.list
    total.value = data.total
    nextTick(() => {
      formData.value.forEach((row, index) => {
        if (!inspectorCheckData.value.map((v) => v.inspector).includes(row.inspector)) {
          inspectorCheckData.value.push({
            inspector: row.inspector,
            list: []
          })
          if (index === 0) {
            subTabsName.value = row.inspector + '_' + index
          }
        }
      })
      inspectTypeFormat()
    })
  } finally {
    formLoading.value = false
  }
}

// const inspectTypeFormat = () => {
//   inspectorCheckData.value.forEach((row) => {
//     const inspector = row.inspector
//     formData.value.forEach((ele) => {
//       ele.isEdit = false
//       if (!ele.checkResult) {
//         ele.checkResult = ''
//       }

//       if (ele.inspector === inspector) {
//         row.list.push(ele)
//       }
//     })
//   })
//   inspectorCheckData.value.forEach((row) => {
//     rowspan(row.list)
//   })
// }
const inspectTypeFormat = () => {
  inspectorCheckData.value.forEach((row) => {
    row.list = formData.value.filter((ele) => ele.inspector === row.inspector)
    row.list.forEach((ele) => {
      ele.isEdit = false
      if (!ele.checkResult) {
        ele.checkResult = ''
      }
    })
  })

  inspectorCheckData.value.forEach((row) => {
    rowspan(row.list)
  })
}
const spanArr: any = ref([])
const position = ref()
// const rowspan = (data) => {
//   //每次调用清空数据
//   spanArr.value = []
//   position.value = 0
//   data.forEach((item, index) => {
//     if (index === 0) {
//       spanArr.value.push(1)
//       position.value = 0
//     } else {
//       if (data[index].inspectType === data[index - 1].inspectType) {
//         spanArr.value[position.value] += 1
//         spanArr.value.push(0)
//       } else {
//         spanArr.value.push(1)
//         position.value = index
//       }
//     }
//   })
// }

const rowspan = (data) => {
  spanArr.value = data.map(() => 1) // 初始化为每个元素一行
  let count = 1
  for (let i = 1; i < data.length; i++) {
    if (data[i].inspectType === data[i - 1].inspectType) {
      spanArr.value[i - 1] += 1
      spanArr.value[i] = 0
      count++
    } else {
      count = 1
    }
  }
}

// const objectSpanMethod: any = ({ row, column, rowIndex, columnIndex }) => {
//   if (columnIndex === 1) {
//     if (spanArr.value && spanArr.value.length > 0) {
//       const _row = spanArr.value[rowIndex]
//       const _col = _row > 0 ? 1 : 0
//       return {
//         rowspan: _row,
//         colspan: _col
//       }
//     }
//   }
// }

const objectSpanMethod: any = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 1 && spanArr.value[rowIndex] !== undefined) {
    const _row = spanArr.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  }
}
// 编辑
const onEdit = (row) => {
  row.isEdit = true
  // 确保checkResult有初始值
  if (!row.checkResult) {
    row.checkResult = ''
  }
}
// 保存
const onSave = async (row) => {
  // 构建保存的数据
  const saveData = cloneDeep(row)
  await InspectApi.saveInspectItem(saveData)
  row.isEdit = false
  message.success('保存成功')
}
// 删除
const onDelete = async (row, index, rowIndex) => {
  try {
    await message.delConfirm()
    await InspectApi.deleteInspectItem(row.id)
    message.success('删除成功')
    inspectorCheckData.value[index].list.splice(rowIndex, 1)
  } catch {}
}
onMounted(async () => {
  if (route.query.id) {
    queryParams.companyId = route.query.id
    formLoading.value = true
    getDetail()
  }
})
</script>
