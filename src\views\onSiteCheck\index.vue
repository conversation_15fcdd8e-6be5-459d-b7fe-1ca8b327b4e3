<!--
 * @Author: bwhb
 * @Date: 2024-08-28 09:09:20
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-01-07 16:37:40
 * @FilePath: \bkehs3.0-web\src\views\checkManagement\index.vue
-->
<template>
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="检查企业" prop="companyId">
        <el-select v-model="queryParams.companyId" placeholder="请选择检查企业" class="!w-240px">
          <el-option
            v-for="row in inspectCompanyList"
            :key="row.id"
            :label="row.name"
            :value="row.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="所属项目" prop="projectId">
          <el-select
            class="!w-240px"
            ref="projectRef"
            filterable
            v-model="queryParams.projectId"
            placeholder="请选择所属项目"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="`【${item.contractCode}】${item.customerName}`"
              :value="item.id"
            />
          </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="检查人" prop="userId">
        <el-select
          filterable
          v-model="queryParams.userId"
          placeholder="请选择检查人"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inspect:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <!-- <el-button
          type="primary"
          @click="handleImport"
          v-hasPermi="['inspect:create']"
        >
          <Icon icon="ep:upload" class="mr-5px" /> 导入
        </el-button> -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inspect:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="检查编号" align="center" prop="id" /> -->
      <el-table-column label="检查企业" align="center" prop="name"></el-table-column>
      <el-table-column label="关联项目" align="center" prop="projectName"></el-table-column>
      <!-- <el-table-column label="负责人" align="center" prop="userName"></el-table-column>
      <el-table-column
        label="检查开始时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter2"
      />
      <el-table-column
        label="检查结束时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter2"
      />
      <el-table-column
        label="实际检查时间"
        align="center"
        prop="inspectTime"
        :formatter="dateFormatter"
      />
      <el-table-column label="状态">
        <template #default="scope">
          {{
            scope.row.status === 0
              ? '未检查'
              : scope.row.status === 1
                ? '已检查'
                : scope.row.status === 2
                  ? '检查中'
                  : ''
          }}
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="180px" fixed="right">
        <template #default="scope">
          <!-- <el-button link type="primary" v-if="scope.row.status !== 1" @click="shareTo(scope.row)">
            分享
          </el-button> -->

          <el-button link type="primary" @click="openForm('detail', scope.row.id)">
            查看检查详情
          </el-button>
          <!-- :disabled="scope.row.status ===0 ? true : false" -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <shareInspectTo ref="shareInspectToRef" />
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { InspectApi } from '@/api/inspect/index'
import * as UserApi from '@/api/system/user'
import { InspectCompanyApi } from '@/api/pms/inspect'
import shareInspectTo from './shareInspectTo.vue'

const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  projectId: undefined,
  companyId: undefined,
  createTimes: []
})
const loading = ref(false)
const list = ref<any>([])
const total = ref(0)
const { currentRoute, push } = useRouter()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const queryFormRef = ref<any>()
const exportLoading = ref(false)
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const getList = async () => {
  loading.value = true
  try {
    queryParams.createTime = queryParams.createTimes.toString()
    const data = await InspectApi.getInspectPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const shareInspectToRef = ref()
const shareTo = (row) => {
  shareInspectToRef.value.open(row)
  //https://teamwork.bkehs.com/onSiteCheck?id=111111
}

const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InspectApi.exportInspect(queryParams)
    download.excel(data, '现场检查清单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const openForm = (type: string, id?: number) => {
  if (type === 'create') {
    push('/inspect/addEdit')
  } else if (type === 'detail') {
    push('/inspect/detail?id=' + id)
  }
}

const inspectCompanyList = ref<any>([])
// const userId = useUserStore().getUser.id // 当前登录的编号
const getInspectCompanyList = async () => {
  const param = {
    pageNum: 1,
    pageSize: 100
  }
  const data = await InspectCompanyApi.getInspectCompanyPage(param)
  inspectCompanyList.value = data.list
}
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
onMounted(async () => {
  if (currentRoute.value?.query) {
    if (currentRoute.value?.query.companyId) {
      queryParams.companyId = Number(currentRoute.value?.query.companyId)
    }
  }
  getList().then(() => {
    getInspectCompanyList()
  })
  userList.value = await UserApi.getSimpleUserList()
})
</script>
<style lang="scss" scoped></style>
