<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-11-15 13:46:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-18 11:17:08
 * @FilePath: \teamwork-web\src\views\onSiteCheck\shareInspectTo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-input v-model="shareUrl">
      <template #append>
        <el-button type="primary" plain @click="copyTo">复制链接</el-button>
      </template>
    </el-input>
    <el-card class="mb-10px text-center" shadow="hover">
      <Qrcode :text="shareUrl" />
    </el-card>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
const { copy } = useClipboard() // 初始化 copy 到粘贴板

const dialogTitle = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const message = useMessage() // 消息弹窗
const shareUrl = ref()
const open = async (row) => {
  shareUrl.value = 'https://teamwork.bkehs.com/onSiteCheck?id=' + row.id
  dialogTitle.value = '复制链接'
  dialogVisible.value = true
}
const copyTo = async () => {
  await copy(shareUrl.value)
  message.success('复制成功！')
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
