<!--
 * @Description: 检查项drawer
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-01-03 15:19:26
 * @LastEditTime: 2025-01-03 16:17:11
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    :title="title"
    :size="1200"
    append-to-body
    destroy-on-close
    @close="close"
  >
    <el-row class="mb4">
      <el-button type="primary" plain @click="openForm('create')">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <!-- <el-button type="danger" plain @click="handleExport" :loading="exportLoading">
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button> -->
    </el-row>

    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60px" />
      <el-table-column label="检查内容" align="center" prop="content" />
      <el-table-column label="检查依据" align="center" prop="basis" />
      <el-table-column label="检查方法" align="center" prop="method" />
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      append-to-body
      destroy-on-close
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="formLoading"
      >
        <el-form-item label="检查内容" prop="content">
          <el-input v-model="formData.content" type="textarea" placeholder="请输入检查内容" />
        </el-form-item>
        <el-form-item label="检查依据" prop="basis">
          <el-input v-model="formData.basis" type="textarea" placeholder="请输入检查依据" />
        </el-form-item>
        <el-form-item label="检查方法" prop="method">
          <el-input v-model="formData.method" type="textarea" placeholder="请输入检查方法" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            class="!w-100%"
            placeholder="请输入排序"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import { ItemContentDetailApi, ItemContentDetailVO } from '@/api/inspect/inspectItem'
import download from '@/utils/download'

const props = defineProps({
  itemContentId: {
    type: Number,
    required: true
  },
  module: {
    type: String,
    required: true
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const drawerVisible = ref(false)
const title = computed(() => `${props.module} - 检查项管理`)

const loading = ref(true) // 列表的加载中
const list = ref<ItemContentDetailVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  itemContentId: undefined as undefined | number
})

// 弹窗表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const formData = ref<any>({
  id: undefined,
  itemContentId: undefined,
  content: '',
  basis: '',
  method: '',
  sort: 0
})
const formRules = reactive({
  content: [{ required: true, message: '检查内容不能为空', trigger: 'blur' }],
  basis: [{ required: true, message: '检查依据不能为空', trigger: 'blur' }],
  method: [{ required: true, message: '检查方法不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
})
const formRef = ref()

/** 打开抽屉 */
const open = () => {
  drawerVisible.value = true
  queryParams.itemContentId = props.itemContentId
  getList()
}

/** 关闭抽屉 */
const close = () => {
  drawerVisible.value = false
  reset()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ItemContentDetailApi.getItemContentDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 打开表单弹窗 */
const openForm = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.itemContentId = props.itemContentId
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ItemContentDetailApi.getItemContentDetail(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await ItemContentDetailApi.createItemContentDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await ItemContentDetailApi.updateItemContentDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 刷新列表
    await getList()
  } finally {
    formLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ItemContentDetailApi.deleteItemContentDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const exportLoading = ref(false) // 导出的加载中
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ItemContentDetailApi.exportItemContentDetail(queryParams)
    download.excel(data, '检查项明细.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 重置查询条件 */
const reset = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.itemContentId = undefined
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    itemContentId: undefined,
    content: '',
    basis: '',
    method: '',
    sort: 0
  }
  formRef.value?.resetFields()
}

defineExpose({
  open
})
</script>
