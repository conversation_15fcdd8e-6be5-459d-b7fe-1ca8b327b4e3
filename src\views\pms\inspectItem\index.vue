<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="模块" prop="module">
        <el-input
          v-model="queryParams.module"
          placeholder="请输入模块"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb4">
      <el-button type="primary" plain @click="openForm('create')">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button type="warning" plain @click="handleImport">
        <Icon icon="ep:upload" class="mr-5px" /> 导入
      </el-button>
      <!-- <el-button type="success" plain @click="handleExport" :loading="exportLoading">
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button> -->
    </el-row>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      :span-method="spanMethod"
    >
      <el-table-column label="检查表名" align="center" prop="checkTableName" />
      <el-table-column label="模块" align="center" prop="module" />
      <el-table-column label="排序" align="center" prop="sort" width="80px" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            <!-- v-hasPermi="['inspect:item-content:update']" -->
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            <!-- v-hasPermi="['inspect:item-content:delete']" -->
            删除
          </el-button>
          <el-button link type="primary" @click="handleManageDetail(scope.row)">
            管理检查项
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ItemContentForm ref="formRef" @success="getList" />
  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="ItemContentApi.upUrl"
    :fieldNames="{
      createField: 'createInspectCompany',
      updateField: 'updateInspectCompany',
      failureField: 'failureInspectCompany'
    }"
  />
  <!-- 检查项明细管理抽屉 -->
  <ItemContentDetailDrawer
    ref="detailDrawerRef"
    :itemContentId="selectedId"
    :module="selectedModule"
  />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ItemContentApi, ItemContentVO } from '@/api/inspect/inspectItem'
import ItemContentForm from './ItemContentForm.vue'
import ItemContentDetailDrawer from './ItemContentDetailDrawer.vue'

/** 检查项主 列表 */
defineOptions({ name: 'ItemContent' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ItemContentVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedId = ref<any>() // 选中的检查项ID
const selectedModule = ref<string>('') // 选中的模块名称
const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  module: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ItemContentApi.getItemContentPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 合并单元格的方法 */
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    // 只对第一列（检查表名）进行合并
    const checkTableName = row.checkTableName
    const prevRow = list.value[rowIndex - 1]
    const nextRow = list.value[rowIndex + 1]

    if (prevRow && prevRow.checkTableName === checkTableName) {
      return { rowspan: 0, colspan: 0 } // 如果与前一行相同，则隐藏当前单元格
    }

    let rowspan = 1
    for (let i = rowIndex + 1; i < list.value.length; i++) {
      if (list.value[i].checkTableName === checkTableName) {
        rowspan++
      } else {
        break
      }
    }

    return { rowspan, colspan: 1 }
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ItemContentApi.deleteItemContent(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
const importTemplate = async () => {
  try {
    const data = await ItemContentApi.getImportTemplate()
    download.excel(data, '检查项模板.xls')
  } catch {
  } finally {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ItemContentApi.exportItemContent(queryParams)
    download.excel(data, '检查项.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 管理检查项明细 */
const detailDrawerRef = ref()
const handleManageDetail = async (row: ItemContentVO) => {
  selectedId.value = row.id
  selectedModule.value = row.module
  await nextTick()
  detailDrawerRef.value.open()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
