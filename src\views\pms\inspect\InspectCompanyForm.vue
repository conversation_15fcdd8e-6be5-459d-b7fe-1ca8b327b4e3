<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="108px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属项目" prop="projectId">
            <el-select
              class="!w-100%"
              ref="projectRef"
              filterable
              v-model="formData.projectId"
              placeholder="请选择所属项目"
              @change="projectChange"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="`【${item.contractCode}】${item.customerName}`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="creditCode" label-width="165px">
            <el-input v-model="formData.creditCode" placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入企业地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industry">
            <el-select v-model="formData.industry" placeholder="请选择所属行业" class="!w-100%">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.INDUSTRY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规模大小" prop="unitSize">
            <el-input v-model="formData.unitSize" placeholder="请输入规模大小" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车间数量" prop="workshopNum">
            <el-input-number
              class="!w-100%"
              v-model="formData.workshopNum"
              :min="0"
              placeholder="请输入车间数量"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="员工数" prop="employeeNum">
            <el-input-number
              class="!w-100%"
              v-model="formData.employeeNum"
              :min="0"
              placeholder="请输入员工数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经营范围" prop="businessScope">
            <el-input v-model="formData.businessScope" placeholder="请输入经营范围" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入企业邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业LOGO" prop="logo">
            <upload-img v-model="formData.logo" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业描述" prop="description">
            <Editor v-model="formData.description" height="150px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="主要负责人" prop="contacts">
            <el-input v-model="formData.contacts" placeholder="请输入主要负责人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主要负责人联系电话" prop="phone" label-width="165px">
            <el-input v-model="formData.phone" placeholder="请输入主要负责人联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="安全管理人员" prop="safetyPerson">
            <el-input v-model="formData.safetyPerson" placeholder="请输入安全管理人员" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安全管理人员联系电话" prop="contactPhone" label-width="165px">
            <el-input v-model="formData.contactPhone" placeholder="请输入安全管理人员联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InspectCompanyApi, InspectCompanyVO } from '@/api/pms/inspect'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getMyProjectPage } from '@/api/bpm/task'
import { useUserStore } from '@/store/modules/user'

/** 检查企业 表单 */
defineOptions({ name: 'InspectCompanyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userId = useUserStore().getUser.id // 当前登录的编号

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: any = ref({
  id: undefined,
  name: undefined,
  creditCode: undefined,
  address: undefined,
  industry: undefined,
  unitSize: undefined,
  workshopNum: undefined,
  employeeNum: undefined,
  logo: undefined,
  description: undefined,
  contacts: undefined,
  phone: undefined,
  safetyPerson: undefined,
  contactPhone: undefined,
  email: undefined,
  projectId: undefined,
  projectName: undefined,
  moduleIds: undefined,
  moduleNames: undefined,
  businessScope: undefined
})

const projectList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await getMyProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2,
      userId
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}

const projectChange = (val: any) => {
  const selectedItem = projectList.value.find((item) => item.id === val)
  if (!selectedItem.moduleIds) {
    formData.value.projectId = undefined
    message.warning('该项目还没有关联检查项，请前往项目计划列表进行关联')
    return
  }
  formData.value.projectName = selectedItem.projectName
  formData.value.moduleIds = selectedItem.moduleIds
  formData.value.moduleNames = selectedItem.moduleNames
}

const formRules = reactive({
  projectId: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  creditCode: [{ required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '企业地址不能为空', trigger: 'blur' }],
  industry: [{ required: true, message: '所属行业不能为空', trigger: 'blur' }],
  description: [{ required: true, message: '企业描述不能为空', trigger: 'blur' }],
  contacts: [{ required: true, message: '主要负责人不能为空', trigger: 'blur' }],
  phone: [{ required: true, message: '主要负责人联系电话不能为空', trigger: 'blur' }],
  safetyPerson: [{ required: true, message: '安全管理人员不能为空', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '安全管理人员联系电话不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 获取项目列表
  await getProjectList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InspectCompanyApi.getInspectCompany(id)
      formData.value.industry = Number(formData.value.industry)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InspectCompanyVO
    if (formType.value === 'create') {
      await InspectCompanyApi.createInspectCompany(data)
      message.success(t('common.createSuccess'))
    } else {
      await InspectCompanyApi.updateInspectCompany(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    creditCode: undefined,
    address: undefined,
    industry: undefined,
    unitSize: undefined,
    workshopNum: undefined,
    employeeNum: undefined,
    logo: undefined,
    description: undefined,
    contacts: undefined,
    phone: undefined,
    safetyPerson: undefined,
    contactPhone: undefined,
    email: undefined,
    projectId: undefined,
    projectName: undefined,
    moduleIds: undefined,
    moduleNames: undefined,
    businessScope: undefined
  }
  formRef.value?.resetFields()
}
</script>
