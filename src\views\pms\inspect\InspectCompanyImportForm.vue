<!--
 * @Description: 检查企业导入
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-01-06 16:14:35
 * @LastEditTime: 2025-01-06 16:49:49
-->
<template>
  <Dialog v-model="dialogVisible" title="检查企业导入" width="600">
    <div class="flex items-center my-10px">
      <span class="mr-10px">所属项目</span>
      <el-select
        class="!w-450px"
        ref="projectRef"
        filterable
        v-model="formData.projectId"
        placeholder="请选择所属项目"
        @change="projectChange"
      >
        <el-option
          v-for="item in projectList"
          :key="item.id"
          :label="`【${item.contractCode}】${item.customerName}`"
          :value="item.id"
        />
      </el-select>
    </div>
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :auto-upload="false"
      :disabled="formLoading || !formData.projectId"
      :limit="1"
      :on-exceed="handleExceed"
      :on-change="handleFileChange"
      accept=".xlsx, .xls"
      action="none"
      drag
    >
      <Icon icon="ep:upload" />
      <div class="el-upload__text">
        <template v-if="formData.projectId"> 将文件拖到此处，或<em>点击上传</em> </template>
        <template v-else> 请先选择所属项目 </template>
      </div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <div class="el-upload__tip">
            <el-checkbox v-model="updateSupport" />
            是否更新已经存在的检查企业数据（"检查企业名称"重复）
          </div>
          <span>仅允许导入 xls、xlsx 格式文件。</span>
          <el-link
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            type="primary"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { InspectCompanyApi } from '@/api/pms/inspect'
import download from '@/utils/download'
import type { UploadUserFile } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getMyProjectPage } from '@/api/bpm/task'

defineOptions({ name: 'InspectCompanyImportForm' })

const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const uploadRef = ref()
const fileList = ref<UploadUserFile[]>([]) // 文件列表
const updateSupport = ref(false) // 是否更新已经存在的客户数据
const userId = useUserStore().getUser.id // 当前登录的编号
const formData: any = ref({
  projectId: undefined,
  projectName: undefined,
  moduleIds: undefined,
  moduleNames: undefined
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  await resetForm()
  await getProjectList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const submitForm = async () => {
  if (!formData.value.projectId) {
    message.error('请先选择所属项目')
    return
  }
  if (fileList.value.length == 0) {
    message.error('请上传文件')
    return
  }

  formLoading.value = true
  try {
    const submitData = new FormData()
    submitData.append('updateSupport', String(updateSupport.value))
    submitData.append('file', fileList.value[0].raw as Blob)
    submitData.append('projectId', String(formData.value.projectId))
    submitData.append('projectName', String(formData.value.projectName))
    if (formData.value.moduleIds) {
      submitData.append('moduleIds', String(formData.value.moduleIds))
      submitData.append('moduleNames', String(formData.value.moduleNames))
    }
    const res = await InspectCompanyApi.handleImport(submitData)
    submitFormSuccess(res)
  } catch {
    submitFormError()
  } finally {
    formLoading.value = false
  }
}

/** 文件上传成功 */
const emits = defineEmits(['success'])
const submitFormSuccess = (response: any) => {
  if (response.code !== 0) {
    message.error(response.msg)
    formLoading.value = false
    return
  }
  // 拼接提示语
  const data = response.data
  let text = '上传成功数量：' + data.createInspectCompany.length + ';'
  for (let customerName of data.createInspectCompany) {
    text += '< ' + customerName + ' >'
  }
  text += '更新成功数量：' + data.updateInspectCompany.length + ';'
  for (const customerName of data.updateInspectCompany) {
    text += '< ' + customerName + ' >'
  }
  text += '更新失败数量：' + Object.keys(data.failureInspectCompany).length + ';'
  for (const customerName in data.failureInspectCompany) {
    text += '< ' + customerName + ': ' + data.failureInspectCompany[customerName] + ' >'
  }
  message.alert(text)
  formLoading.value = false
  dialogVisible.value = false
  // 发送操作成功的事件
  emits('success')
}

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}

/** 重置表单 */
const resetForm = async () => {
  // 重置上传状态和文件
  fileList.value = []
  updateSupport.value = false
  formData.value.projectId = undefined
  formData.value.projectName = undefined
  formData.value.moduleIds = undefined
  formData.value.moduleNames = undefined
  await nextTick()
  uploadRef.value?.clearFiles()
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个文件！')
}

/** 下载模板操作 */
const importTemplate = async () => {
  const res = await InspectCompanyApi.getImportTemplate()
  download.excel(res, '检查企业导入模版.xls')
}

const projectList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await getMyProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2,
      userId
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}

const projectChange = (val: any) => {
  const selectedItem = projectList.value.find((item) => item.id === val)
  if (!selectedItem.moduleIds) {
    formData.value.projectId = undefined
    message.warning('该项目还没有关联检查项，请前往项目计划列表进行关联')
    return
  }
  formData.value.projectName = selectedItem.projectName
  formData.value.moduleIds = selectedItem.moduleIds
  formData.value.moduleNames = selectedItem.moduleNames
}

/** 文件变更时的处理 */
const handleFileChange = (file: UploadUserFile) => {
  if (!formData.value.projectId) {
    message.error('请先选择所属项目')
    fileList.value = []
    return false
  }
}
</script>
