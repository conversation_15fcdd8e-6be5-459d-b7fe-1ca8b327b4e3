<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="负责人" prop="userId">
        <el-select
          v-model="formData.userId"
          filterable
          multiple
          placeholder="请选择负责人"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检查日期" prop="inspectTime">
        <el-date-picker
          v-model="formData.inspectTime"
          type="daterange"
          value-format="x"
          style="width: 100%"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :disabled-date="disabledDate"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as UserApi from '@/api/system/user'
import { InspectCompanyApi } from '@/api/pms/inspect'

interface InspectItemSaveReqVO {
  inspectType: string
  projectId: string | number
  companyId: number
}

defineOptions({ name: 'SetInspectCheck' })

const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData: any = ref({
  userId: undefined,
  inspectTime: undefined,
  startTime: undefined,
  endTime: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '请选择检查人', trigger: 'blur' }],
  inspectTime: [{ required: true, message: '请选择检查日期', trigger: 'blur' }]
})
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 86400000
}
const formRef = ref() // 表单 Ref
const companyId = ref()
const batchData = ref<Array<{id: number, inspectType: string, projectId: string | number}>>([])
const extraParams = ref<{
  inspectType?: string | undefined
  projectId?: string | number | undefined
}>({})

const open = async (type: string, data?: any, params?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'single' ? '指派检查任务' : '批量指派检查任务'
  if (type === 'single') {
    companyId.value = data
    extraParams.value = params || {}
  } else {
    companyId.value = ''
    batchData.value = data || []
  }
  getUsetList()
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const resetForm = () => {
  formData.value = {
    userId: undefined,
    inspectTime: undefined,
    startTime: undefined,
    endTime: undefined
  }
}
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const getUsetList = async () => {
  userList.value = await UserApi.getSimpleUserList()
}
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const baseData = {
      startTime: formData.value.inspectTime[0],
      endTime: formData.value.inspectTime[1],
      userIds: formData.value.userId.toString(),
      status: 0
    }

    // 构建inspectItems数组
    let inspectItems: InspectItemSaveReqVO[] = []
    if (companyId.value) {
      // 单个指派
      inspectItems = [{
        inspectType: extraParams.value.inspectType || '',
        projectId: extraParams.value.projectId || '',
        companyId: companyId.value
      }]
    } else if (batchData.value.length) {
      // 批量指派
      inspectItems = batchData.value.map(item => ({
        inspectType: item.inspectType || '',
        projectId: item.projectId || '',
        companyId: item.id
      }))
    }

    const data = {
      ...baseData,
      inspectItems
    }

    await InspectCompanyApi.createInspect(data)
    message.success('提交成功')
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
