<!--
 * @Description: 现场检查-检查企业管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-24 13:38:10
 * @LastEditTime: 2025-01-10 10:34:07
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="企业名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb4">
      <el-button
        type="primary"
        plain
        @click="openForm('create')"
        v-hasPermi="['pms:inspect-company:create']"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button
        type="warning"
        plain
        @click="handleImport"
        v-hasPermi="['pms:inspect-company:create']"
      >
        <Icon icon="ep:upload" class="mr-5px" /> 导入
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['pms:inspect-company:export']"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>

      <el-button
        type="primary"
        @click="batchSetInspect"
        :disabled="!selectedRows.length"
        v-hasPermi="['pms:inspect-company:designate']"
      >
        批量指派现场检查
      </el-button>
    </el-row>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="selectChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="60px" />
      <el-table-column label="企业名称" align="center" prop="name" />
      <el-table-column label="所属行业" align="center" prop="industry">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.INDUSTRY, scope.row.industry) }}
        </template>
      </el-table-column>
      <el-table-column label="所属项目" align="center" prop="projectName" min-width="150" />
      <el-table-column label="联系人" align="center" prop="contacts" width="100" />
      <el-table-column label="联系电话" align="center" prop="phone" width="120" />
      <el-table-column label="企业邮箱" align="center" prop="email" />
      <el-table-column
        label="最近指派时间"
        align="center"
        prop="recentAssignTime"
        :formatter="dateFormatter"
      />
      <!-- <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.INSPECT_STATUS, scope.row.status) }}
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="360px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="searchList(scope.row.id)">
            查看检查任务
          </el-button>

          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['pms:inspect-company:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['pms:inspect-company:delete']"
          >
            删除
          </el-button>

          <el-button
            link
            type="primary"
            @click="setInspect(scope.row.id, scope.row)"
            v-hasPermi="['pms:inspect-company:designate']"
          >
            指派现场检查
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 导入弹窗 -->
  <InspectCompanyImportForm ref="importFormRef" @success="getList" />
  <!-- 表单弹窗：添加/修改 -->
  <InspectCompanyForm ref="formRef" @success="getList" />
  <SetInspectCheck ref="inspectCheckRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { InspectCompanyApi, InspectCompanyVO } from '@/api/pms/inspect'
import InspectCompanyForm from './InspectCompanyForm.vue'
import InspectCompanyImportForm from './InspectCompanyImportForm.vue'
import SetInspectCheck from './SetInspectCheck.vue'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'

// 扩展 InspectCompanyVO 类型
interface ExtendedInspectCompanyVO extends InspectCompanyVO {
  moduleIds?: string
  projectId?: string | number
}

/** 检查企业 列表 */
defineOptions({ name: 'InspectCompany' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const selectedRows = ref<ExtendedInspectCompanyVO[]>([])
const loading = ref(true) // 列表的加载中
const list = ref<ExtendedInspectCompanyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  address: undefined,
  industry: undefined,
  moduleIds: undefined,
  projectId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const { push } = useRouter()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InspectCompanyApi.getInspectCompanyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const selectChange = (ele) => {
  selectedRows.value = ele
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
const searchList = (id) => {
  push('/inspect/list?companyId=' + id)
}
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

const inspectCheckRef = ref()
const setInspect = (id, row) => {
  inspectCheckRef.value.open('single', id, {
    inspectType: row.moduleIds,
    projectId: row.projectId
  })
}
const batchSetInspect = () => {
  // 构建每行数据的完整信息
  const selectedData = selectedRows.value.map(row => ({
    id: row.id,
    inspectType: row.moduleIds,
    projectId: row.projectId
  }))
  
  inspectCheckRef.value.open('batch', selectedData)
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InspectCompanyApi.deleteInspectCompany(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InspectCompanyApi.exportInspectCompany(queryParams)
    download.excel(data, '检查企业.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
