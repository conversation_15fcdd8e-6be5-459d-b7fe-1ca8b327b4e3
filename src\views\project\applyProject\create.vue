<template>
  <div class="project-create-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <Icon icon="ep:folder-add" />
        </div>
        <div class="header-text">
          <h1 class="page-title">项目申请创建</h1>
          <p class="page-subtitle">创建新的项目申请，填写相关信息</p>
        </div>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="158px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document" />
            </div>
            <h3 class="section-title">基本信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="立项合同（立项内容）" prop="contractName">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.contractName"
                    disabled
                    style="width: 400px"
                    placeholder="请输入立项合同（立项内容）"
                    class="modern-input"
                  />
                  <el-button
                    v-if="!query.projectSpecialId"
                    type="primary"
                    @click="chooseDialog('contract')"
                    class="action-button"
                  >
                    <Icon icon="ep:folder-opened" />
                    选择合同
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目内容" prop="projectName">
                <el-input
                  :disabled="query.projectSpecialId"
                  v-model="formData.projectName"
                  placeholder="请输入项目内容"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  disabled
                  placeholder="请选择客户名称"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目模板" prop="projectTemplateName">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.projectTemplateName"
                    disabled
                    placeholder="请输入项目模板"
                    class="modern-input"
                  />
                  <el-button type="primary" @click="chooseDialog('template')" class="action-button">
                    <Icon icon="ep:files" />
                    选择模板
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 项目详情卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:setting" />
            </div>
            <h3 class="section-title">项目详情</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="人工天(天)" prop="estimateCost">
                <el-input
                  @input="(v) => (formData.estimateCost = v.replace(/[^\d.]/g, ''))"
                  v-model="formData.estimateCost"
                  placeholder="请输入人工天(天)"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="立项类别" prop="businessType">
                <el-select
                  v-model="formData.businessType"
                  placeholder="请选择立项类别"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.BUSINESS_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="立项日期" prop="projectApprovalTime">
                <el-date-picker
                  v-model="formData.projectApprovalTime"
                  type="date"
                  value-format="x"
                  placeholder="选择立项日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划启动日期" prop="plannedStartDate">
                <el-date-picker
                  v-model="formData.plannedStartDate"
                  type="date"
                  value-format="x"
                  placeholder="选择计划启动日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划完成日期" prop="mustBeCompletedDate">
                <el-date-picker
                  v-model="formData.mustBeCompletedDate"
                  type="date"
                  value-format="x"
                  placeholder="选择计划完成日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目地址" prop="projectLocation">
                <el-input
                  v-model="formData.projectLocation"
                  placeholder="请输入项目地址"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 人员信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:user" />
            </div>
            <h3 class="section-title">人员信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目经理（咨询组长）" prop="projectManagerId">
                <el-select
                  ref="projectManagerRef"
                  filterable
                  v-model="formData.projectManagerId"
                  placeholder="请输入项目经理（咨询组长）"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目总监" prop="projectDirectorId">
                <el-select
                  ref="projectDirectorRef"
                  filterable
                  v-model="formData.projectDirectorId"
                  placeholder="请输入项目总监"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:office-building" />
            </div>
            <h3 class="section-title">客户信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户部门" prop="partnerName">
                <el-input
                  v-model="formData.partnerName"
                  placeholder="请输入客户部门"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户联系人" prop="legalOwnerName">
                <el-input
                  v-model="formData.legalOwnerName"
                  placeholder="请输入客户联系人"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户电话/手机" prop="projectConsultantName">
                <el-input
                  v-model="formData.projectConsultantName"
                  placeholder="请输入客户电话/手机"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划交通费" prop="planTravelExpense">
                <el-input
                  v-model="formData.planTravelExpense"
                  type="number"
                  placeholder="请输入计划交通费"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 备注和附件卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document-add" />
            </div>
            <h3 class="section-title">备注和附件</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="备注" prop="details">
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="formData.details"
                  placeholder="请输入备注信息"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="立项附件" prop="attachment">
                <UploadFile
                  v-model="formData.attachment"
                  :file-size="5120"
                  :limit="5"
                  @uploading="handleUploading"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            @click="submitForm"
            type="primary"
            :disabled="formLoading || isUploading"
            class="submit-button"
          >
            <Icon icon="ep:check" />
            确认提交
          </el-button>
          <el-button
            @click="handleClose"
            type="warning"
            :disabled="formLoading"
            class="cancel-button"
          >
            <Icon icon="ep:close" />
            取消关闭
          </el-button>
        </div>
      </el-form>
    </div>
  </div>

  <customerDialog ref="customerDialogRef" @fetch-data="chooseCustomerDone" />
  <templateDialog ref="templateDialogRef" @fetch-data="chooseTemplateDone" />
  <contractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
</template>
<script setup lang="ts">
import { ProjectApi, ProjectVO } from '@/api/project/applyProject'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { nameValueProps, handleTree } from '@/utils/tree'
import customerDialog from '@/components/CustomerDialog/index.vue'
import templateDialog from '@/components/TemplateDialog/index.vue'
import contractDialog from '@/components/ContractDialog/index.vue'
import { isArray } from '@/utils/is'
import { ProjectSpecialApi, ProjectSpecialVO } from '@/api/project/projectSpecial'

const { query } = useRoute() // 查询参数
const contractId = query.contractId as unknown as number // 从 URL 传递过来的 id 编号
const contractName = query.contractName as unknown as number // 从 URL 传递过来的 id 编号
const customerName = query.customerName as unknown as number // 从 URL 传递过来的 id 编号

const customerDialogRef = ref()
const templateDialogRef = ref()
const contractDialogRef = ref()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const formLoading = ref(false)
const formData = ref<any>({
  id: undefined,
  projectName: contractName,
  deptId: undefined,
  deptName: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  customerGroup: undefined,
  customerId: undefined,
  specialCode: undefined,
  customerName: customerName,
  projectTemplateId: undefined,
  projectTemplateName: undefined,
  contractId: contractId,
  contractName: contractName,
  currencyId: undefined,
  businessType: undefined,
  projectApprovalTime: undefined,
  plannedStartDate: undefined,
  mustBeCompletedDate: undefined,
  projectManagerId: undefined,
  projectManagerName: undefined,
  projectDirectorId: undefined,
  projectDirectorName: undefined,
  legalOwnerId: undefined,
  legalOwnerName: undefined,
  projectConsultantId: undefined,
  projectConsultantName: undefined,
  partnerId: undefined,
  partnerName: undefined,
  projectLocation: undefined,
  planTravelExpense: undefined,
  estimatePrice: undefined,
  estimateCost: undefined,
  estimateGrossProfit: undefined,
  estimateGrossProfitMargin: undefined,
  attachment: undefined,
  details: undefined
})

const deptList = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const formRules = reactive({
  contractName: [{ required: true, message: '立项合同（立项内容）不能为空', trigger: 'blur' }],
  projectName: [{ required: true, message: '项目内容不能为空', trigger: 'blur' }],
  estimateCost: [{ required: true, message: '人工天(天)不能为空', trigger: 'blur' }],
  projectApprovalTime: [{ required: true, message: '立项日期不能为空', trigger: 'blur' }],
  plannedStartDate: [{ required: true, message: '计划启动日期不能为空', trigger: 'blur' }],
  mustBeCompletedDate: [{ required: true, message: '完成申报日期不能为空', trigger: 'blur' }],
  projectDirectorId: [{ required: true, message: '项目总监不能为空', trigger: 'blur' }],
  projectLocation: [{ required: true, message: '项目地址不能为空', trigger: 'blur' }],
  planTravelExpense: [{ required: true, message: '计划交通费不能为空', trigger: 'blur' }],
  legalOwnerName: [{ required: true, message: '客户联系人不能为空', trigger: 'blur' }],
  projectConsultantName: [{ required: true, message: '客户电话/手机不能为空', trigger: 'blur' }],
  partnerName: [{ required: true, message: '客户部门不能为空', trigger: 'blur' }],
  businessType: [{ required: true, message: '立项类别不能为空', trigger: 'blur' }],
  projectTemplateName: [{ required: true, message: '模板不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const projectManagerRef = ref()
const projectDirectorRef = ref()
const legalOwnerRef = ref()
const projectConsultantRef = ref()
const partnerRef = ref()

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    formData.value.projectManagerName = projectManagerRef.value.states.selectedLabel
    formData.value.projectDirectorName = projectDirectorRef.value.states.selectedLabel
    const data = formData.value as any
    if (isArray(data.attachment) && data.attachment.length > 0) {
      data.attachment = data.attachment.join()
    }
    data.estimateCost = Number(data.estimateCost) * 8
    delete data.id
    let res = await ProjectApi.createProject(data)
    if (res === 10000) {
      message.error('当前合同未到账不能立项，即将跳转到特批申请')
      setTimeout(() => {
        push({
          name: 'ProjectSpecialCreate',
          query: {
            contractId: data.contractId,
            contractName: data.contractName,
            customerName: data.customerName,
            customerId: data.customerId,
            contractCode: data.contractCode,
            projectName: data.projectName
          }
        })
      }, 1000)
    } else {
      message.success(t('common.createSuccess'))
      // 关闭当前 Tab
      handleClose()
    }
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/project/applyProject')
}

const chooseDialog = (type) => {
  if (type == 'customer') {
    customerDialogRef.value.openDialog()
  } else if (type == 'template') {
    templateDialogRef.value.openDialog()
  } else {
    contractDialogRef.value.openDialog()
  }
}

const chooseCustomerDone = (item) => {
  formData.value.customerId = item.id
  formData.value.customerName = item.customerName
}
const chooseTemplateDone = (item) => {
  let idList: any = []
  let nameList: any = []
  item.forEach((el: any) => {
    idList.push(el.id)
    nameList.push(el.name)
  })
  formData.value.projectTemplateId = idList.join()
  formData.value.projectTemplateName = nameList.join()
}
const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractName = item.contractName
  formData.value.projectName = item.contractName
  formData.value.customerName = item.customerName
  formData.value.customerId = item.customerId
  formData.value.contractCode = item.contractCode
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())

  if (query.id) {
    formData.value = await ProjectApi.getProject(query.id as any)
    formData.value.estimateCost =
      formData.value.estimateCost && Number(formData.value.estimateCost) / 8
  }
  if (query.projectSpecialId) {
    let data = await ProjectSpecialApi.getProjectSpecial(query.projectSpecialId as any)
    formData.value.contractId = data.contractId
    formData.value.contractName = data.contractName
    formData.value.projectName = data.projectName
    formData.value.customerName = data.customerName
    formData.value.customerId = data.customerId
    formData.value.specialCode = data.specialCode
  }
})
</script>

<style lang="scss" scoped>
.project-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

/* 页面头部 */
.page-header {
  background: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 10px 40px rgba(79, 70, 229, 0.08),
    0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .header-content {
    display: flex;
    align-items: center;

    .header-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-right: 20px;
      box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    }

    .header-text {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        letter-spacing: -0.5px;
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

/* 表单容器 */
.form-container {
  .modern-form {
    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        line-height: 1.5;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-picker {
          .el-input__wrapper {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        .el-textarea {
          .el-textarea__inner {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            font-size: 14px;
            color: #374151;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &:focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

/* 表单分区 */
.form-section {
  background: #ffffff;
  border-radius: 18px;
  padding: 28px;
  margin-bottom: 20px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &:nth-child(1) {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &:nth-child(2) {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      &:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &:nth-child(4) {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
    }

    .section-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }
}

/* 输入框带按钮 */
.input-with-button {
  display: flex;
  gap: 12px;
  align-items: flex-end;

  .modern-input {
    flex: 1;
  }

  .action-button {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
    }

    .el-icon {
      margin-right: 6px;
    }
  }
}

/* 操作按钮区域 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }

  .cancel-button {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .project-create-container {
    padding: 20px;
  }

  .page-header {
    padding: 24px;

    .header-content {
      .header-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-right: 16px;
      }

      .header-text {
        .page-title {
          font-size: 24px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }
    }
  }

  .form-section {
    padding: 24px;

    .section-header {
      .section-title {
        font-size: 18px;
      }
    }
  }
}

@media (max-width: 768px) {
  .project-create-container {
    padding: 16px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 16px;

    .header-content {
      flex-direction: column;
      text-align: center;

      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
    }
  }

  .form-section {
    padding: 20px;
    margin-bottom: 16px;

    .section-header {
      .section-title {
        font-size: 16px;
      }
    }
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .project-create-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;

    .header-content {
      .header-text {
        .page-title {
          font-size: 20px;
        }

        .page-subtitle {
          font-size: 13px;
        }
      }
    }
  }

  .form-section {
    padding: 16px;

    .section-header {
      .section-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-right: 12px;
      }

      .section-title {
        font-size: 15px;
      }
    }
  }
}
</style>
