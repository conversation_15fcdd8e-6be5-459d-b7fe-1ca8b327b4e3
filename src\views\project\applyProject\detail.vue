<!--
 * @Description: 开票申请详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2024-11-21 14:57:18
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="项目内容">
        {{ detailData.projectName }}
      </el-descriptions-item>
      <el-descriptions-item label="合同编号">
        {{ detailData.contractCode }}
      </el-descriptions-item>
      <el-descriptions-item label="客户名称">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="立项申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="申请人所属部门">
        {{ detailData.deptN<PERSON> }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="客户类别">
        <dict-tag :type="DICT_TYPE.CUSTOMER_GROUP" :value="detailData.customerGroup" />
      </el-descriptions-item> -->
      <el-descriptions-item label="项目模板">
        {{ detailData.projectTemplateName }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="币种">
        <dict-tag :type="DICT_TYPE.CURRENCY" :value="detailData.currencyId" />
      </el-descriptions-item> -->
      <el-descriptions-item label="立项类别">
        <dict-tag :type="DICT_TYPE.BUSINESS_TYPE" :value="detailData.businessType" />
      </el-descriptions-item>
      <el-descriptions-item label="立项日期">
        {{ formatDate(detailData.projectApprovalTime, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="计划启动日期">
        {{ formatDate(detailData.plannedStartDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="计划完成日期">
        {{ formatDate(detailData.mustBeCompletedDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>

      <el-descriptions-item label="项目经理（咨询组长）">
        {{ detailData.projectManagerName }}
      </el-descriptions-item>
      <el-descriptions-item label="项目总监">
        {{ detailData.projectDirectorName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户联系人">
        {{ detailData.legalOwnerName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户电话/手机">
        {{ detailData.projectConsultantName }}
      </el-descriptions-item>
      <el-descriptions-item label="项目地址">
        {{ detailData.projectLocation }}
      </el-descriptions-item>
      <el-descriptions-item label="计划交通费">
        {{ detailData.planTravelExpense }}
      </el-descriptions-item>
      <el-descriptions-item label="客户部门">
        {{ detailData.partnerName }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="估计价格">
        {{ detailData.estimatePrice }}
      </el-descriptions-item> -->
      <el-descriptions-item label="人工天(小时)">
        {{ detailData.estimateCost }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="估计毛利">
        {{ detailData.estimateGrossProfit }}
      </el-descriptions-item>
      <el-descriptions-item label="估计毛利率">
        {{ detailData.estimateGrossProfitMargin }}
      </el-descriptions-item> -->
      <el-descriptions-item label="备注">
        {{ detailData.details }}
      </el-descriptions-item>
      <el-descriptions-item label="立项附件">
        <FileListPreview :fileUrl="detailData.attachment" />
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="detailData.result" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ProjectApi } from '@/api/project/applyProject'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ProjectDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ProjectApi.getProject(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/project/applyProject')
}
/** 初始化 **/
onMounted(() => {
  getInfo()
})
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
