<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="项目内容" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="handleCreate()">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="项目内容" align="center" prop="projectName" min-width="300" />
      <!-- <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="审核状态" align="center" prop="result" />
      <el-table-column label="申请人的用户编号" align="center" prop="userId" />
      <el-table-column label="流程实例的编号" align="center" prop="processInstanceId" />
      <el-table-column label="客户类别" align="center" prop="customerGroup">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CUSTOMER_GROUP" :value="scope.row.customerGroup" />
        </template>
      </el-table-column> -->

      <el-table-column label="审核状态" align="center" prop="result" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>

      <el-table-column label="客户名" align="center" prop="customerName" width="280" />
      <!-- <el-table-column label="项目模板" align="center" prop="projectTemplateName">
        <template #default="scope">
          <el-tag
            v-if="scope.row.projectTemplateName"
            v-for="item in scope.row.projectTemplateName.split(',')"
            :key="item.id"
            class="mr-5px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column> -->
      <el-table-column label="合同编号" align="center" prop="contractCode" width="200" />
      <el-table-column label="立项合同" align="center" prop="contractName" width="250" />

      <!-- <el-table-column label="币种" align="center" prop="currencyId" />
      <el-table-column label="业务分类" align="center" prop="businessType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BUSINESS_TYPE" :value="scope.row.businessType" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="立项日期"
        align="center"
        prop="projectApprovalTime"
        :formatter="dateFormatter2"
        width="130px"
      />
      <el-table-column
        label="计划启动日期"
        align="center"
        prop="plannedStartDate"
        :formatter="dateFormatter2"
        width="130px"
      />
      <el-table-column
        label="计划完成日期"
        align="center"
        prop="mustBeCompletedDate"
        :formatter="dateFormatter2"
        width="130px"
      />

      <el-table-column label="项目经理" align="center" prop="projectManagerName" width="100" />

      <el-table-column label="项目总监" align="center" prop="projectDirectorName" width="100" />

      <!-- <el-table-column label="项目联系人" align="center" prop="legalOwnerName" />

      <el-table-column label="项目联系人电话" align="center" prop="projectConsultantName" />
      <el-table-column label="合伙人" align="center" prop="partnerName" />

      <el-table-column label="项目所在地" align="center" prop="projectLocation" />
      <el-table-column label="估计价格" align="center" prop="estimatePrice" />
      <el-table-column label="人工天" align="center" prop="estimateCost" />
      <el-table-column label="估计毛利" align="center" prop="estimateGrossProfit" />
      <el-table-column label="估计毛利率" align="center" prop="estimateGrossProfitMargin" />
      <el-table-column label="附件" align="center" prop="attachment" />
      <el-table-column label="明细信息" align="center" prop="details" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="280" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" v-if="onlySeeSelf" @click="openDialog(scope.row)">
            修改日期
          </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <ProjectForm ref="projectFormRef" @success="getList" />
</template>

<script setup lang="ts">
import ProjectForm from './projectForm.vue'
import { ElMessageBox } from 'element-plus'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProjectApi, ProjectVO } from '@/api/project/applyProject'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { useUserStore } from '@/store/modules/user'

/** 立项申请信息 列表 */
defineOptions({ name: 'Project' })

const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userStore = useUserStore()

let projectFormRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<ProjectVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectName: undefined,
  customerName: undefined,
  contractCode: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const router = useRouter() // 路由
const roles = userStore.getRoles // 当前登录人角色

// 只看自己的日历的角色
const onlySeeSelf = computed(() => {
  return roles.includes('super_admin') || roles.includes('pms')
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectApi.getProjectPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const handleCreate = () => {
  router.push({ name: 'ProjectCreate' })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ProjectDetail',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjectApi.deleteProject(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProjectApi.exportProject(queryParams)
    download.excel(data, '立项申请信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

// 重新发起
const reissue = (row) => {
  router.push({
    name: 'ProjectCreate',
    query: {
      id: row.id
    }
  })
}

const openDialog = (row) => {
  projectFormRef.value.open(row)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
