<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="合同ID" prop="contractId">
        <el-input v-model="formData.contractId" placeholder="请输入合同ID" />
      </el-form-item>
      <el-form-item label="原合同编码" prop="originalContractCode">
        <el-input v-model="formData.originalContractCode" placeholder="请输入原合同编码" />
      </el-form-item>
      <el-form-item label="原合同名称" prop="originalContractName">
        <el-input v-model="formData.originalContractName" placeholder="请输入原合同名称" />
      </el-form-item>
      <el-form-item label="原合同金额" prop="originalContractAmount">
        <el-input v-model="formData.originalContractAmount" placeholder="请输入原合同金额" />
      </el-form-item>
      <el-form-item label="变更后合同编码" prop="modifiedContractCode">
        <el-input v-model="formData.modifiedContractCode" placeholder="请输入变更后合同编码" />
      </el-form-item>
      <el-form-item label="变更后合同名称" prop="modifiedContractName">
        <el-input v-model="formData.modifiedContractName" placeholder="请输入变更后合同名称" />
      </el-form-item>
      <el-form-item label="变更后合同金额" prop="modifiedContractAmount">
        <el-input v-model="formData.modifiedContractAmount" placeholder="请输入变更后合同金额" />
      </el-form-item>
      <el-form-item label="合同说明" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入合同说明" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ContractChangeApi, ContractChangeVO } from '@/api/bpm/contractchange'

/** 合同变更 表单 */
defineOptions({ name: 'ContractChangeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  contractId: undefined,
  originalContractCode: undefined,
  originalContractName: undefined,
  originalContractAmount: undefined,
  modifiedContractCode: undefined,
  modifiedContractName: undefined,
  modifiedContractAmount: undefined,
  remark: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ContractChangeApi.getContractChange(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ContractChangeVO
    if (formType.value === 'create') {
      await ContractChangeApi.createContractChange(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractChangeApi.updateContractChange(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    contractId: undefined,
    originalContractCode: undefined,
    originalContractName: undefined,
    originalContractAmount: undefined,
    modifiedContractCode: undefined,
    modifiedContractName: undefined,
    modifiedContractAmount: undefined,
    remark: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
  }
  formRef.value?.resetFields()
}
</script>
