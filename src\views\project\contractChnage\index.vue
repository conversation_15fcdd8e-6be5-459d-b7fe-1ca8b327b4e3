<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="原合同编码" prop="originalContractCode">
        <el-input
          v-model="queryParams.originalContractCode"
          placeholder="请输入原合同编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="原合同名称" prop="originalContractName">
        <el-input
          v-model="queryParams.originalContractName"
          placeholder="请输入原合同名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="原合同金额" prop="originalContractAmount">
        <el-input
          v-model="queryParams.originalContractAmount"
          placeholder="请输入原合同金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="变更后合同编码" prop="modifiedContractCode">
        <el-input
          v-model="queryParams.modifiedContractCode"
          placeholder="请输入变更后合同编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb2">
      <el-button
        type="primary"
        plain
        @click="openForm('create')"
        v-hasPermi="['bpm:contract-change:create']"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['bpm:contract-change:export']"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="原合同编码" align="center" prop="originalContractCode" />
      <el-table-column label="原合同名称" align="center" prop="originalContractName" />
      <el-table-column label="原合同金额" align="center" prop="originalContractAmount" />
      <el-table-column label="变更后合同编码" align="center" prop="modifiedContractCode" />
      <el-table-column label="变更后合同名称" align="center" prop="modifiedContractName" />
      <el-table-column label="变更后合同金额" align="center" prop="modifiedContractAmount" />
      <el-table-column label="合同说明" align="center" prop="remark" />
      <el-table-column label="审核状态" align="center" prop="result" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['bpm:contract-change:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['bpm:contract-change:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractChangeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ContractChangeApi, ContractChangeVO } from '@/api/project/contractChange'
import ContractChangeForm from './ContractChangeForm.vue'

/** 合同变更 列表 */
defineOptions({ name: 'ContractChange' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ContractChangeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractId: undefined,
  originalContractCode: undefined,
  originalContractName: undefined,
  originalContractAmount: undefined,
  modifiedContractCode: undefined,
  modifiedContractName: undefined,
  modifiedContractAmount: undefined,
  remark: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractChangeApi.getContractChangePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractChangeApi.deleteContractChange(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ContractChangeApi.exportContractChange(queryParams)
    download.excel(data, '合同变更.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
