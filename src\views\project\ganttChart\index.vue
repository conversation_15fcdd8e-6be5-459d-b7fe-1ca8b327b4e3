<!--
 * @Description: ganttChartIndex
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-17 13:41:37
 * @LastEditTime: 2024-05-17 15:10:13
-->

<template>
  <section class="index-container">
    <component :is="currentView" :row="row" @updateCurrentView="updateCurrentView"></component>
  </section>
</template>

<script lang="ts" setup>
import UserGanttList from './userGanttList.vue'
import UserGanttChart from './userGanttChart.vue'

defineOptions({ name: 'Gantt<PERSON>hart' })
const row = ref({})
const currentView = shallowRef<any>(UserGanttList)

const updateCurrentView = (view, item) => {
  row.value = item
  if (view === 'UserGanttChart') {
    currentView.value = UserGanttChart
  } else if (view === 'UserGanttList') {
    currentView.value = UserGanttL<PERSON>
  }
}
</script>
