<!--
 * @Description: 甘特图
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-17 13:41:37
 * @LastEditTime: 2025-04-21 13:48:18
-->
<template>
  <ContentWrap>
    <div class="gantt-header">
      <el-button type="primary" @click="handleBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>

      <div class="user-name"> {{ row.nickname }} 的项目/任务甘特图 </div>

      <el-button class="refresh-btn" type="primary" @click="refresh">
        <el-icon><Refresh /></el-icon> &nbsp;刷 新
      </el-button>
    </div>
    <section class="my-gantt">
      <div id="gantt_here" class="gantt-container"></div>
    </section>
  </ContentWrap>
</template>

<script setup lang="ts">
import { getPersonGanttData } from '@/api/project/ganttChart'
import { gantt } from 'dhtmlx-gantt'
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'
import demoData from './ganttData.json'
import dayjs from 'dayjs'

defineOptions({ name: 'UserGanttChart' })
const props = defineProps({
  row: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['updateCurrentView'])
const ganttData = ref<any>({})
//初始化甘特图
const initGantt = () => {
  gantt.config.grid_width = 350
  gantt.config.add_column = false // 添加符号

  // 时间轴图表中，如果不设置，只有行边框，区分上下的任务，设置之后带有列的边框，整个时间轴变成格子状。
  gantt.config.autofit = false
  gantt.config.row_height = 60
  gantt.config.bar_height = 34
  gantt.config.fit_tasks = true // 自动延长时间刻度，以适应所有显示的任务
  gantt.config.auto_types = true // 将包含子任务的任务转换为项目，将没有子任务的项目转换回任务
  gantt.config.xml_date = '%Y-%m-%d' // 甘特图时间格式
  gantt.config.readonly = true // 是否只读
  gantt.config.scales = [
    { unit: 'month', step: 1, format: '%Y年%F' },
    { unit: 'week', step: 1, format: weekScaleTemplate },
    { unit: 'day', step: 1, format: '%D' }
  ]
  gantt.config.scale_height = 90
  gantt.i18n.setLocale('cn') // 设置语言
  gantt.plugins({
    tooltip: true
  })
  // 周末加背景色
  gantt.templates.scale_cell_class = function (date): any {
    if (date.getDay() == 0 || date.getDay() == 6) {
      return 'weekend'
    }
  }
  gantt.templates.timeline_cell_class = function (item, date): any {
    if (date.getDay() == 0 || date.getDay() == 6) {
      return 'weekend'
    }
  }

  gantt.init('gantt_here') //初始化
  if (!ganttData.value || ganttData.value.length === 0) return
  gantt.parse(ganttData.value) //填充数据

  // 自定义tooltip内容
  gantt.templates.tooltip_text = function (start: Date, end: Date, task: any) {
    const t = gantt
    const output = `
    <b>任务名：</b>${task.text}<br/>
    <b>开始时间：</b>${t.templates.tooltip_date_format(start)}<br/>
    <b>结束时间：</b>${dayjs(end).subtract(1, 'day').format('YYYY-MM-DD')}<br/>
    <b>计划开始时间：</b>${task.planStartTime}<br/>
    <b>计划结束时间：</b>${task.planEndTime}<br/>
    `

    return output
  }
  // 添加tooltip
  gantt.attachEvent('onGanttReady', function () {
    const tooltips = gantt.ext.tooltips
    tooltips.tooltip.setViewport((gantt as any).$task_data)
  })

  // 计算甘特图的时间范围(包含基线)
  const dateRange = calculateDateRange(ganttData.value.data)
  // 设置新的开始和结束日期
  gantt.config.start_date = new Date(dateRange.start_date)
  gantt.config.end_date = new Date(dateRange.end_date)

  // 重新渲染甘特图以应用更改
  gantt.render()

  // 绘制基线的事件监听
  const eventIds: any[] = [
    'onAfterTaskUpdate',
    'onAfterTaskAdd',
    'onParse',
    'onTaskSelected',
    'onGanttRender',
    'onAfterTaskDrag',
    'onDataRender',
    'onRowDragEnd'
  ]

  eventIds.forEach((eventId) => {
    gantt.attachEvent(eventId, drawBaselines)
  })

  drawBaselines()
}

// 周展示
const weekScaleTemplate = (date) => {
  const dateToStr = gantt.date.date_to_str('%M%d日')
  const endDate = gantt.date.add(gantt.date.add(date, 1, 'week'), -1, 'day')
  return dateToStr(date) + ' - ' + dateToStr(endDate)
}

// 画自定义计划开始/结束时间基线
const drawBaselines = () => {
  // 清除先前基线
  document.querySelectorAll('.baseline').forEach((el) => el.remove())

  gantt.eachTask((task) => {
    if (!task.planStartTime || !task.planEndTime) return
    const sizes = gantt.getTaskPosition(
      task,
      gantt.date.parseDate(task.planStartTime, 'xml_date'),
      gantt.date.parseDate(dayjs(task.planEndTime).add(1, 'day').format('YYYY-MM-DD'), 'xml_date')
    )

    const el = document.createElement('div')
    el.className = 'baseline'
    el.style.left = sizes.left + 'px'
    el.style.width = sizes.width - 4 + 'px'
    el.style.top = sizes.top + gantt.config.row_height - 20 + 'px'
    el.style.height = '10px'
    el.style.backgroundColor = '#ffe3b3'
    el.style.position = 'absolute'
    el.style.border = '2px solid #ffc266'
    ;(document.querySelector('.gantt_task_bg') as any).appendChild(el)
  })
}

const fetchData = async () => {
  const data = await getPersonGanttData({ userId: props.row.id })
  ganttData.value = {
    data: data.map((item) => ({
      ...item,
      start_date: item.startDate
    }))
  }
  // ganttData.value = demoData
}

const handleBack = () => {
  // 手动隐藏工具提示
  gantt.ext.tooltips?.tooltip?.hide()
  gantt.clearAll()
  emit('updateCurrentView', 'UserGanttList', { queryParams: props.row.queryParams })
}

const refresh = async () => {
  gantt.clearAll()
  await fetchData()
  initGantt()
}

// 计算甘特图图例展示时间范围
const calculateDateRange = (tasks) => {
  let minDate: any = null
  let maxDate: any = null

  tasks.forEach((task) => {
    if (!task.start_date) return
    const taskStart = gantt.date.parseDate(task.start_date, 'xml_date')
    const taskEnd = gantt.date.add(taskStart, task.duration, 'day')
    const plannedStart = gantt.date.parseDate(task.planStartTime, 'xml_date')
    const plannedEnd = gantt.date.parseDate(task.planEndTime, 'xml_date')

    if (!minDate || plannedStart < minDate) minDate = plannedStart
    if (!maxDate || plannedEnd > maxDate) maxDate = plannedEnd
    if (!minDate || taskStart < minDate) minDate = taskStart
    if (!maxDate || taskEnd > maxDate) maxDate = taskEnd
  })

  return {
    start_date: minDate && gantt.date.add(minDate, -2, 'day'), // 提前一天以使视图更清晰
    end_date: maxDate && gantt.date.add(maxDate, 2, 'day') // 延后一天以使视图更清晰
  }
}

onMounted(async () => {
  refresh()
})
onBeforeUnmount(() => {
  gantt.ext.tooltips?.tooltip?.hide()
})
</script>
<style scoped lang="scss">
.gantt-header {
  position: relative;
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  .user-name {
    margin-left: 20px;
    font-size: 18px;
    font-weight: 700;
  }

  .refresh-btn {
    position: absolute;
    right: 30px;
  }
}
.my-gantt {
  height: 750px;
  width: calc(100vw - 150px);
  .gantt-container {
    width: 100%;
    height: 100%;
  }
}

/* 调整任务条的高度和位置 */
:deep(.gantt_task_line) {
  height: 18px !important; /* 更细的任务条 */
  margin-bottom: 15px !important;
}
:deep(.gantt_task_content) {
  line-height: 18px !important;
}

:deep(.weekend) {
  background: #f4f7f4 !important;
}
</style>
