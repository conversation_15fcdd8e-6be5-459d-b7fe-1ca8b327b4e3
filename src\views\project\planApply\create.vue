<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :inline="true" label-width="120px">
      <el-form-item label="计划时间" prop="receiptTime">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery" :loading="loading"
          ><Icon icon="ep:search" class="mr-5px" /> 生成</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-form
      v-loading="formLoading"
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="10">
        <el-col :span="12">
        <el-form-item label="文档" prop="fileUrl">
          <el-input
            v-if="!formData.fileUrl"
            :disabled="true"
            placeholder="请选择计划时间导出对应的计划报表"
          ></el-input>
          <FileListPreview v-else :fileUrl="formData.fileUrl" />
        </el-form-item>
      </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="formData.planName" />
        </el-form-item>
      </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" :rows="4" v-model="formData.remark" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- <el-form-item label="文档" prop="fileUrl">
            <el-input
              v-if="!formData.fileUrl"
              :disabled="true"
              placeholder="请选择计划时间导出对应的计划报表"
            ></el-input>
            <FileListPreview v-else :fileUrl="formData.fileUrl" />
          </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>
  <el-row :gutter="10">
    <div class="ml-auto mr-auto mt-30px">
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="handleClose" type="warning" :disabled="formLoading">关 闭</el-button>
    </div>
  </el-row>
</template>
<script setup lang="ts">
import { PlanApplyApi, PlanApplyVO } from '@/api/project/planApply'
import { useTagsViewStore } from '@/store/modules/tagsView'

const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const formRef = ref() // 表单 Ref
const loading = ref<boolean>(false)
let dateRange = ref()
let formData = ref<any>({
  fileUrl: undefined,
  planName: undefined,
  outStartDate: undefined,
  outEndDate: undefined,
  remark: undefined
})
let formType = ref('create')
const formLoading = ref(false)

let fileUrl = ref()
const formRules = reactive({
  fileUrl: [{ required: true, message: '请导出文档', trigger: 'blur' }],
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }]
})
/** 搜索按钮操作 */
const handleQuery = async () => {
  if (!formData.value.outStartDate || !formData.value.outEndDate) return
  loading.value = true
  formData.value.fileUrl = await PlanApplyApi.planExport({
    queryTimeFrom: formData.value.outStartDate,
    queryTimeTo: formData.value.outEndDate
  })
  message.success('计划已生成，请查看下面文档')
  loading.value = false
}

const selectDate = () => {
  formData.value.outStartDate = dateRange.value[0]
  formData.value.outEndDate = dateRange.value[1]
}

/** 重置按钮操作 */
const resetQuery = () => {}

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PlanApplyVO as any
    // 正常新增/修改
    if (formType.value === 'create') {
      await PlanApplyApi.createPlanApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await PlanApplyApi.updatePlanApply(fileUrl.value)
      message.success(t('common.updateSuccess'))
    }
  } finally {
    formLoading.value = false
    handleClose()
  }
}
const handleClose = () => {
  delView(unref(currentRoute))
  push('/project/planApply')
}
</script>
