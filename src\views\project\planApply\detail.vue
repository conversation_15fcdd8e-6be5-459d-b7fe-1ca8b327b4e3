<!--
 * @Description: 开票申请详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2024-10-10 16:16:51
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="计划名称">
        {{ detailData.planName }}
      </el-descriptions-item>
      <el-descriptions-item label="开始日期">
        {{ formatDate(detailData.outStartDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="结束日期">
        {{ formatDate(detailData.outEndDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="文档">
        <FileListPreview :fileUrl="detailData.fileUrl" />
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        {{ getDictLabel(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT, detailData.result) }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { PlanApplyApi, PlanApplyVO } from '@/api/project/planApply'

defineOptions({ name: 'PlanApplyDetail' })

const { query } = useRoute() // 查询参数
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await PlanApplyApi.getPlanApply(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push({
    path: '/project/planApply',
    query: { ...query, id: undefined }
  })
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped></style>
