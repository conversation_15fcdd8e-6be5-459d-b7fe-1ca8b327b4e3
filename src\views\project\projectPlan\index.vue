<!--
 * @Description: 项目计划
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-10 17:16:06
 * @LastEditTime: 2025-01-03 16:43:24
-->
<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目经理" prop="projectManagerId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.projectManagerId"
          placeholder="请先选项目经理"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      row-key="id"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="客户" align="center" prop="customerName" width="280" />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="300" />
      <el-table-column label="项目经理" align="center" prop="projectManagerName" width="100" />
      <!-- <el-table-column label="部门名称" align="center" prop="deptName" width="150" /> -->
      <!-- <el-table-column label="客户类别" align="center" prop="customerGroup" /> -->
      <el-table-column label="项目模板" align="center" prop="projectTemplateName" width="350" />
      <!-- <el-table-column label="币种" align="center" prop="currencyId" />
      <el-table-column label="业务分类" align="center" prop="businessType" width="100" /> -->
      <el-table-column label="合同编号" align="center" prop="contractCode" width="200" />
      <el-table-column label="合同" align="center" prop="contractName" width="250" />
      <el-table-column
        label="立项日期"
        align="center"
        prop="projectApprovalTime"
        :formatter="dateFormatter2"
        width="130px"
      />
      <el-table-column
        label="计划开工日期"
        align="center"
        prop="plannedStartDate"
        :formatter="dateFormatter2"
        width="130px"
      />
      <el-table-column
        label="计划完成日期"
        align="center"
        prop="mustBeCompletedDate"
        :formatter="dateFormatter2"
        width="130px"
      />

      <!-- <el-table-column label="项目总监" align="center" prop="projectDirectorName" width="100" /> -->
      <!-- <el-table-column label="项目联系人" align="center" prop="legalOwnerName" width="100" /> -->
      <!-- <el-table-column label="项目联系人电话" align="center" prop="projectConsultantName" width="100" /> -->
      <!-- <el-table-column label="项目所在地" align="center" prop="projectLocationId" width="150" /> -->
      <!-- <el-table-column label="估计价格" align="center" prop="estimatePrice" width="100" />
      <el-table-column label="人工天" align="center" prop="estimateCost" width="100" />
      <el-table-column label="估计毛利" align="center" prop="estimateGrossProfit" width="100" /> -->
      <!-- <el-table-column
        label="估计毛利率"
        align="center"
        prop="estimateGrossProfitMargin"
        width="100"
      /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="350">
        <template #default="scope">
          <el-button
            v-if="scope.row.customerName === '上海临时项目'"
            link
            type="warning"
            @click="handleChange(scope.row)"
          >
            变更
          </el-button>
          <el-button link type="primary" @click="handleTarget(scope.row)"> 任务 </el-button>
          <el-button link type="primary" @click="handleUser(scope.row)"> 成员 </el-button>
          <el-button link type="primary" @click="handleFile(scope.row)"> 文件 </el-button>
          <el-button link type="primary" @click="handleResult(scope.row)">
            成果
            <i v-if="scope.row.hasResult" class="text-20px text-red-500">*</i>
          </el-button>
          <el-button
            v-if="userId === scope.row.projectManagerId || onlySeeSelf"
            link
            type="primary"
            @click="handleSetItems(scope.row)"
          >
            设置检查项
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 设置检查项弹窗 -->
  <el-dialog
    v-model="setItemsVisible"
    title="设置检查项"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-table
      ref="setItemsTableRef"
      v-loading="setItemsLoading"
      :data="itemsList"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column label="模块" prop="module" />
    </el-table>
    <template #footer>
      <el-button @click="setItemsVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitSetItems" :loading="setItemsSubmitLoading"
        >确 定</el-button
      >
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ItemContentApi, ItemContentVO } from '@/api/inspect/inspectItem'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { ProjectApi, ProjectVO } from '@/api/project/applyProject'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'SystemMenu' })
const userStore = useUserStore()
const roles = userStore.getRoles // 当前登录人角色
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const userId = useUserStore().getUser.id // 当前登录的编号
const loading = ref(true) // 列表的加载中
const list = ref<ProjectVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  result: 2,
  projectName: undefined,
  customerName: undefined,
  projectManagerId: undefined,
  contractCode: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const router = useRouter() // 路由
const route = useRoute()

const onlySeeSelf = computed(() => {
  return roles.includes('super_admin') || roles.includes('pms')
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectApi.getProjectPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  if (route.query.projectName) {
    queryParams.projectName = route.query.projectName
  }
  getList()
  userList.value = await UserApi.getSimpleUserList()
})
/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

const handleChange = (row) => {
  router.push({
    path: '/project/changeRecord/create',
    query: {
      row: JSON.stringify(row)
    }
  })
}

const handleTarget = (row) => {
  router.push({
    path: '/project/projectTask',
    query: {
      projectId: row.id
    }
  })
}
const handleFile = (row) => {
  router.push({
    path: '/project/projectTaskFile',
    query: {
      projectId: row.id,
      projectName: row.projectName,
      type: 1
    }
  })
}
const handleResult = (row) => {
  router.push({
    path: '/project/projectTaskResult',
    query: {
      projectId: row.id,
      projectName: row.projectName,
      type: 1
    }
  })
}

const handleUser = (row) => {
  router.push({
    path: '/project/projectTaskUser',
    query: {
      projectId: row.id,
      projectName: row.projectName,
      type: 1
    }
  })
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, parentId?: number) => {
  formRef.value.open(type, id, parentId)
}
/** 设置检查项相关变量 */
const setItemsVisible = ref(false)
const setItemsLoading = ref(false)
const setItemsSubmitLoading = ref(false)
const itemsList = ref<any>([])
const selectedItems = ref<any>([])
const currentRow = ref<any>()
const setItemsTableRef = ref() // 添加表格ref

/** 获取检查项列表 */
const getItemsList = async () => {
  setItemsLoading.value = true
  try {
    const data = await ItemContentApi.getItemContentPage({
      pageNo: 1,
      pageSize: 999
    })
    itemsList.value = data.list
  } finally {
    setItemsLoading.value = false
  }
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: any[]) => {
  selectedItems.value = selection
}

/** 提交设置检查项 */
const submitSetItems = async () => {
  if (!currentRow.value) return
  setItemsSubmitLoading.value = true
  try {
    const moduleIds = selectedItems.value.map((item) => item.id).join(',')
    const moduleNames = selectedItems.value.map((item) => item.module).join(',')
    await ProjectApi.updateProject({ id: currentRow.value.id, moduleIds, moduleNames } as any)
    message.success(t('common.updateSuccess'))
    setItemsVisible.value = false
    // 刷新列表
    await getList()
  } finally {
    setItemsSubmitLoading.value = false
  }
}

/** 打开设置检查项弹窗 */
const handleSetItems = async (row: any) => {
  setItemsVisible.value = true
  currentRow.value = row
  await getItemsList()
  // 设置已选中的项
  if (row.moduleIds) {
    const selectedIds = row.moduleIds.split(',').map(Number)
    const rows = itemsList.value.filter((item) => selectedIds.includes(item.id))
    nextTick(() => {
      // 使用ref获取表格实例
      const table = setItemsTableRef.value
      // 先清除所有选中状态
      table?.clearSelection()
      // 然后设置选中状态
      rows.forEach((row) => {
        table?.toggleRowSelection(row, true)
      })
    })
  }
}
</script>
