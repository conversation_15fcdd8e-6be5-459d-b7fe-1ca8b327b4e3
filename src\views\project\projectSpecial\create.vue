<template>
  <div class="project-create-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <Icon icon="ep:folder-add" />
        </div>
        <div class="header-text">
          <h1 class="page-title">立项特批创建</h1>
          <p class="page-subtitle">创建新的立项特批创建，填写相关信息</p>
        </div>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="158px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 项目信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document" />
            </div>
            <h3 class="section-title">项目信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同名称" prop="contractName">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.contractName"
                    disabled
                    placeholder="请输入合同名称"
                    class="modern-input"
                  />
                  <el-button type="primary" @click="chooseDialog('contract')" class="action-button">
                    <Icon icon="ep:folder-opened" />
                    选择合同
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称" prop="projectName">
                <el-input
                  v-model="formData.projectName"
                  placeholder="请输入项目内容"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  disabled
                  placeholder="请选择客户名称"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document" />
            </div>
            <h3 class="section-title">基本信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="特批说明" prop="specialDetails">
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="formData.specialDetails"
                  placeholder="请输入特批说明"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="到账时间" prop="paymentDate">
                <el-date-picker
                  v-model="formData.paymentDate"
                  type="date"
                  value-format="x"
                  placeholder="选择到账时间"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="特批附件" prop="specialAttachment">
                <UploadFile v-model="formData.specialAttachment" :file-size="255" :limit="5" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            @click="submitForm"
            type="primary"
            :disabled="formLoading || isUploading"
            class="submit-button"
          >
            <Icon icon="ep:check" />
            确认提交
          </el-button>
          <el-button
            @click="handleClose"
            type="warning"
            :disabled="formLoading"
            class="cancel-button"
          >
            <Icon icon="ep:close" />
            取消关闭
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
  <contractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
</template>
<script setup lang="ts">
import { ProjectSpecialApi, ProjectSpecialVO } from '@/api/project/projectSpecial'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { isArray, isString, isNumber, isBoolean } from '@/utils/is'
import contractDialog from '@/components/ContractDialog/index.vue'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const { query } = useRoute() // 查询参数
const formLoading = ref(false)
const formData = ref<any>({
  id: undefined,
  specialCode: undefined,
  specialAttachment: undefined,
  specialDetails: undefined,
  paymentDate: undefined,
  result: undefined,
  userId: undefined,
  userName: undefined,
  processInstanceId: undefined,
  projectName: undefined,
  customerId: undefined,
  customerName: undefined,
  contractId: undefined,
  contractName: undefined,
  contractCode: undefined
})
const contractDialogRef = ref()

const formRules = reactive({
  specialDetails: [{ required: true, message: '特批说明不能为空', trigger: 'blur' }],
  paymentDate: [{ required: true, message: '到账时间不能为空', trigger: 'blur' }],
  contractName: [{ required: true, message: '合同名称不能为空', trigger: 'blur' }],
  customerName: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
  projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const chooseDialog = (type) => {
  contractDialogRef.value.openDialog()
}

const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractCode = item.contractCode
  formData.value.contractName = item.contractName
  formData.value.projectName = item.contractName
  formData.value.customerName = item.customerName
  formData.value.customerId = item.customerId
}

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as any
    if (isArray(data.specialAttachment) && data.specialAttachment.length > 0) {
      data.specialAttachment = data.specialAttachment.join()
    }
    delete data.id
    await ProjectSpecialApi.createProjectSpecial(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/project/project-special')
}

onMounted(async () => {
  if (query.id) {
    formData.value = await ProjectSpecialApi.getProjectSpecial(query.id as any)
  }
  if (query.contractId) {
    formData.value.contractId = query.contractId
    formData.value.contractName = query.contractName
    formData.value.customerName = query.customerName
    formData.value.customerId = query.customerId
    formData.value.contractCode = query.contractCode
    formData.value.projectName = query.projectName
  }
})
</script>

<style lang="scss" scoped>
.project-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

/* 页面头部 */
.page-header {
  background: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 10px 40px rgba(79, 70, 229, 0.08),
    0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .header-content {
    display: flex;
    align-items: center;

    .header-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-right: 20px;
      box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    }

    .header-text {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        letter-spacing: -0.5px;
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

/* 表单容器 */
.form-container {
  .modern-form {
    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        line-height: 1.5;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-picker {
          .el-input__wrapper {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        .el-textarea {
          .el-textarea__inner {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            font-size: 14px;
            color: #374151;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &:focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

/* 表单分区 */
.form-section {
  background: #ffffff;
  border-radius: 18px;
  padding: 28px;
  margin-bottom: 20px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &:nth-child(1) {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &:nth-child(2) {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      &:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &:nth-child(4) {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
    }

    .section-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }
}

/* 输入框带按钮 */
.input-with-button {
  display: flex;
  gap: 12px;
  align-items: flex-end;

  .modern-input {
    flex: 1;
  }

  .action-button {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
    }

    .el-icon {
      margin-right: 6px;
    }
  }
}

/* 操作按钮区域 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }

  .cancel-button {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .project-create-container {
    padding: 20px;
  }

  .page-header {
    padding: 24px;

    .header-content {
      .header-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-right: 16px;
      }

      .header-text {
        .page-title {
          font-size: 24px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }
    }
  }

  .form-section {
    padding: 24px;

    .section-header {
      .section-title {
        font-size: 18px;
      }
    }
  }
}

@media (max-width: 768px) {
  .project-create-container {
    padding: 16px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 16px;

    .header-content {
      flex-direction: column;
      text-align: center;

      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
    }
  }

  .form-section {
    padding: 20px;
    margin-bottom: 16px;

    .section-header {
      .section-title {
        font-size: 16px;
      }
    }
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .project-create-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;

    .header-content {
      .header-text {
        .page-title {
          font-size: 20px;
        }

        .page-subtitle {
          font-size: 13px;
        }
      }
    }
  }

  .form-section {
    padding: 16px;

    .section-header {
      .section-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-right: 12px;
      }

      .section-title {
        font-size: 15px;
      }
    }
  }
}
</style>
