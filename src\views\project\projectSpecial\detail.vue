<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="特批编号">
        {{ detailData.specialCode }}
      </el-descriptions-item>
      <el-descriptions-item label="合同编号">
        {{ detailData.contractCode }}
      </el-descriptions-item>
      <el-descriptions-item label="合同名称">
        {{ detailData.contractName }}
      </el-descriptions-item>
      <el-descriptions-item label="项目名称">
        {{ detailData.projectName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户名称">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="特批附件">
        <FileListPreview :fileUrl="detailData.specialAttachment" />
      </el-descriptions-item>
      <el-descriptions-item label="特批说明">
        {{ detailData.specialDetails }}
      </el-descriptions-item>
      <el-descriptions-item label="到账时间">
        {{ formatDate(detailData.paymentDate) }}
      </el-descriptions-item>
      <el-descriptions-item label="申请人">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="detailData.result" />
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ProjectSpecialApi, ProjectSpecialVO } from '@/api/project/projectSpecial'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ProjectSpecialDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ProjectSpecialApi.getProjectSpecial(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/project/project-special')
}
/** 初始化 **/
onMounted(() => {
  getInfo()
})
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
