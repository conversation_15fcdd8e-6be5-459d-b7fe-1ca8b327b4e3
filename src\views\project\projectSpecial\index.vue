<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="申请人" prop="userId">
        <el-select
          filterable
          v-model="queryParams.userId"
          placeholder="请先选择申请人"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="特批编号" prop="specialCode">
        <el-input
          v-model="queryParams.specialCode"
          placeholder="请输入特批编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" plain @click="handleCreate">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="合同名称" align="center" prop="contractName" />
      <el-table-column label="合同编号" align="center" prop="contractCode" />
      <el-table-column label="特批编号" align="center" prop="specialCode" />
      <el-table-column label="特批附件" align="center" prop="specialAttachment">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.specialAttachment" />
        </template>
      </el-table-column>
      <el-table-column label="特批说明" align="center" prop="specialDetails" />
      <el-table-column
        label="到账时间"
        align="center"
        prop="paymentDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审核状态" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="userName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200px">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="primary"
            @click="handleApply(scope.row)"
            v-if="scope.row.result === 2"
          >
            去立项
          </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProjectSpecialForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProjectSpecialApi, ProjectSpecialVO } from '@/api/project/projectSpecial'
import ProjectSpecialForm from './ProjectSpecialForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getSimpleUserList } from '@/api/system/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'

/** 立项特批申请 列表 */
defineOptions({ name: 'ProjectSpecial' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const userList = ref<UserVO[]>([]) // 用户列表
const loading = ref(true) // 列表的加载中
const list = ref<ProjectSpecialVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectName: undefined,
  customerId: undefined,
  customerName: undefined,
  contractId: undefined,
  contractName: undefined,
  contractCode: undefined,
  specialCode: undefined,
  specialAttachment: undefined,
  specialDetails: undefined,
  paymentDate: [],
  result: undefined,
  userId: undefined,
  userName: undefined,
  processInstanceId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const handleCreate = () => {
  router.push({ name: 'ProjectSpecialCreate' })
}

// 重新发起
const reissue = (row) => {
  router.push({
    name: 'ProjectSpecialCreate',
    query: {
      id: row.id
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ProjectSpecialDetail',
    query: {
      id: row.id
    }
  })
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const handleApply = (row) => {
  router.push({
    name: 'ProjectCreate',
    query: {
      projectSpecialId: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectSpecialApi.getProjectSpecialPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjectSpecialApi.deleteProjectSpecial(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProjectSpecialApi.exportProjectSpecial(queryParams)
    download.excel(data, '立项特批申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await getSimpleUserList()
})
</script>
