<template>
  <ContentWrap>
    <div class="flex items-center mb-4" v-if="typeId">
      <el-button type="primary" @click="handleBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>
      <div class="font-bold text-xl ml-20px"> 项目名称：{{ route.query.projectName }} </div>
    </div>
    <!-- 搜索工作栏 -->
    <el-form
      class="mb-10px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['pms:project-task-file:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['pms:project-task-file:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="文件" align="center" prop="fileUrl" width="350">
        <template #default="scope">
          <div class="listLink" @click="handlePreview(scope.row.fileUrl)">
            {{ getFileName(scope.row.fileUrl) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="上传人" align="center" prop="userId" width="300">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.userId)?.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['pms:project-task-file:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['pms:project-task-file:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProjectTaskFileForm ref="formRef" :taskId="props.taskId" @success="getList" />
  <!-- 文件预览 -->
  <PreviewDialog ref="previewDialogRef" />
</template>

<script setup lang="ts">
import { getFileName } from '@/utils'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProjectTaskFileApi, ProjectTaskFileVO } from '@/api/project/projectTaskFile'
import ProjectTaskFileForm from './ProjectTaskFileForm.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'

const { delView } = useTagsViewStore() // 视图操作
const router = useRouter() // 路由
const route = useRoute()
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const { query } = useRoute() // 查询参数
const projectId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号
const typeId = query.type as unknown as number // 从 URL 传递过来的 id 编号
const props = defineProps({
  taskId: {
    type: Number
  }
})

/** 项目或任务文件 列表 */
defineOptions({ name: 'ProjectTaskFile' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ProjectTaskFileVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectId: projectId,
  taskId: undefined,
  fileUrl: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let queryData = {
      ...queryParams,
      taskId: props.taskId
    }
    const data = await ProjectTaskFileApi.getProjectTaskFilePage(queryData)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const previewDialogRef = ref()
const handlePreview = (url) => {
  previewDialogRef.value.fileLoad(url)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjectTaskFileApi.deleteProjectTaskFile(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProjectTaskFileApi.exportProjectTaskFile(queryParams)
    download.excel(data, '项目或任务文件.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleBack = () => {
  // 关闭当前 Tab
  delView(unref(router.currentRoute))
  router.push({
    path: (route.query.backPath as string) || '/project/projectPlan'
  })
}

/** 初始化 **/
onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()
  if (typeId) {
    getList()
  }
})
defineExpose({ getList })
</script>
