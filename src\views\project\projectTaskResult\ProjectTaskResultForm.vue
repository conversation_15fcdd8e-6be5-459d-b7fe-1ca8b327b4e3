<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" label-width="100px" v-loading="formLoading">
      <!-- <el-form-item label="项目ID" prop="projectId">
        <el-input v-model="formData.projectId" disabled placeholder="请输入项目ID" />
      </el-form-item>
      <el-form-item v-if="props.taskId" label="任务ID" prop="taskId">
        <el-input v-model="formData.taskId" disabled placeholder="请输入任务ID" />
      </el-form-item> -->
      <el-form-item label="成果" prop="resultUrl">
        <UploadFile
          v-model="formData.resultUrl"
          :fileType="['doc', 'docx', 'xls', 'xlsx', 'pdf', 'zip', 'png', 'jpg', 'svg']"
          placeholder="请上传成果"
          :file-size="5120"
          :limit="20"
          @uploading="handleUploading"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading || isUploading"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProjectTaskResultApi, ProjectTaskResultVO } from '@/api/project/projectTaskResult'

/** 项目或任务成果 表单 */
defineOptions({ name: 'ProjectTaskResultForm' })

const { query } = useRoute() // 查询参数
const projectId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps({
  taskId: {
    type: Number
  }
})

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  projectId: projectId,
  taskId: undefined,
  resultUrl: undefined,
  remark: undefined
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  nextTick(() => {
    formData.value.taskId = props.taskId as any
  })

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProjectTaskResultApi.getProjectTaskResult(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as any
    // if (data.resultUrl && data.resultUrl.length > 0) {
    //   data.resultUrl = data.resultUrl.join()
    // }
    if (formType.value === 'create') {
      await ProjectTaskResultApi.createProjectTaskResult(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProjectTaskResultApi.updateProjectTaskResult(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    projectId: projectId,
    taskId: undefined,
    resultUrl: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
</script>
