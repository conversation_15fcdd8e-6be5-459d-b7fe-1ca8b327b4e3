<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :rules="formRules"
      :model="formData"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="项目ID" prop="projectId">
        <el-input v-model="formData.projectId" disabled placeholder="请输入项目ID" />
      </el-form-item>
      <el-form-item v-if="props.taskId" label="任务ID" prop="taskId">
        <el-input v-model="formData.taskId" disabled placeholder="请输入任务ID" />
      </el-form-item> -->
      <el-form-item label="人员" prop="userId">
        <el-select
          v-if="typeId"
          filterable
          v-model="formData.userId"
          placeholder="请输入人员"
          style="width: 100%"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
        <el-select v-else v-model="formData.userId" placeholder="请输入人员" style="width: 100%">
          <el-option
            :key="item.id"
            v-for="item in userTaskList"
            :label="item.userName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预计工时" prop="planCostTime">
        <el-input-number
          style="width: 240px"
          v-model="formData.planCostTime"
          placeholder="请输入预计工时"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProjectTaskUserApi, ProjectTaskUserVO } from '@/api/project/projectTaskUser'
import * as UserApi from '@/api/system/user'

/** 项目或任务人员 表单 */
defineOptions({ name: 'ProjectTaskUserForm' })

const { query } = useRoute() // 查询参数
const projectId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号
const typeId = query.type as unknown as number // 从 URL 传递过来的 id 编号

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps({
  taskId: {
    type: Number
  }
})

const formRules = reactive({
  userId: [{ required: true, message: '人员不能为空', trigger: 'blur' }],
  planCostTime: [{ required: true, message: '预计工时不能为空', trigger: 'blur' }]
})

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  projectId: projectId,
  taskId: undefined,
  userId: undefined,
  planCostTime: undefined,
  remark: undefined
})

const userTaskList = ref()

const formRef = ref() // 表单 Ref

const fetchUserList = async () => {
  let queryData = {
    pageNo: 1,
    pageSize: 999,
    projectId: projectId,
    queryFlag: true
  }
  const data = await ProjectTaskUserApi.getProjectTaskUserPage(queryData)
  userTaskList.value = data.list
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  if (!props.taskId) {
    userList.value = await UserApi.getSimpleUserList()
  } else {
    fetchUserList()
  }
  dialogTitle.value = t('action.' + type)
  formType.value = type
  nextTick(() => {
    formData.value.taskId = props.taskId as any
  })

  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProjectTaskUserApi.getProjectTaskUser(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProjectTaskUserVO
    if (formType.value === 'create') {
      await ProjectTaskUserApi.createProjectTaskUser(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProjectTaskUserApi.updateProjectTaskUser(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    projectId: projectId,
    taskId: undefined,
    userId: undefined,
    planCostTime: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
