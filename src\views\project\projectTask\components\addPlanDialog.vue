<!--
 * @Description: 添加计划弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-09 17:09:20
 * @LastEditTime: 2024-11-12 16:04:15
-->

<template>
  <Dialog title="添加计划" v-model="dialogVisible" center width="1080">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="所属项目" prop="projectName">
            {{ formData.projectName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="客户名称" prop="customerName">
            {{ formData.customerName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="所属任务" prop="describeVal">
            {{ formData.describeVal }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="负责人" prop="userId">
        {{ user.nickname }}
      </el-form-item>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="日期" prop="consumeDate">
            <el-date-picker
              clearable
              type="date"
              v-model="formData.consumeDate"
              value-format="x"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="计划工作内容" prop="planContent">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.planContent"
          placeholder="请输入计划工作内容"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.remark"
          placeholder="请输入备注(现场第几次咨询?)"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TaskApi } from '@/api/project/projectTask'
import { useUserStore } from '@/store/modules/user'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'

dayjs.extend(advancedFormat)
/** 任务信息 表单 */
defineOptions({ name: 'AddPlanDialog' })

const props = defineProps({
  projectDetailInfo: {
    type: Object,
    default: () => ({})
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const user = useUserStore().getUser
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  customerName: undefined,
  describeVal: undefined,
  userId: undefined,
  userName: undefined,
  taskType: undefined,
  plannedDuration: undefined,
  actualStartDate: undefined,
  actualEndDate: undefined,
  completeBase: undefined,
  specifyBasis: undefined,
  planContent: undefined,
  remark: undefined,
  mustBeCompletedDate: undefined,
  taskId: undefined,
  consumeDate: undefined
})
const formRules = reactive({
  consumeDate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  planContent: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async (row) => {
  resetForm()
  if (row) {
    formData.value = {
      customerName: props.projectDetailInfo.customerName,
      projectId: row.projectId,
      projectName: props.projectDetailInfo.projectName,
      taskId: row.id,
      describeVal: row.describeVal
    }
  }
  dialogVisible.value = true
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    formData.value.userId = user.id
    formData.value.userName = user.nickname
    const data = cloneDeep(formData.value)
    delete data.id
    await TaskApi.updateTaskPlan(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    projectId: undefined,
    projectName: undefined,
    customerName: undefined,
    describeVal: undefined,
    userId: undefined,
    userName: undefined,
    taskType: undefined,
    plannedDuration: undefined,
    actualStartDate: undefined,
    actualEndDate: undefined,
    completeBase: undefined,
    specifyBasis: undefined,
    planContent: undefined,
    remark: undefined,
    mustBeCompletedDate: undefined,
    taskId: undefined,
    consumeDate: undefined
  }
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
