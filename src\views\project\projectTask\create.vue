<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    v-loading="formLoading"
  >
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="任务名称" prop="describeVal">
          <el-input v-model="formData.describeVal" placeholder="请输入任务名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="任务阶段" prop="taskPhases">
          <el-select
            v-model="formData.taskPhases"
            filterable
            placeholder="请输入任务阶段"
            style="width: 100%"
          >
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="负责人" prop="responsibleId">
          <el-select
            ref="responsibleRef"
            v-model="formData.responsibleId"
            filterable
            placeholder="请输入负责人"
            style="width: 100%"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.userName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <!--<el-col :span="12">
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="formData.taskType" placeholder="请选择任务类型" style="width: 100%">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TASK_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

       <el-col :span="12">
        <el-form-item label="计划工期 天" prop="plannedDuration">
          <el-input v-model="formData.plannedDuration" placeholder="请输入计划工期 天" />
        </el-form-item>
      </el-col> -->

      <el-col :span="12">
        <el-form-item label="计划开工日期" prop="plannedStartDate">
          <el-date-picker
            v-model="formData.plannedStartDate"
            type="date"
            value-format="x"
            placeholder="选择计划开工日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划完工日期" prop="plannedEndDate">
          <el-date-picker
            v-model="formData.plannedEndDate"
            type="date"
            value-format="x"
            placeholder="选择计划完工日期"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="12">
        <el-form-item label="完工比基础" prop="completeBase">
          <el-input v-model="formData.completeBase" placeholder="请输入完工比基础" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="指定依据" prop="specifyBasis">
          <el-input v-model="formData.specifyBasis" placeholder="请输入制定依据" />
        </el-form-item>
      </el-col> -->

      <el-col :span="12">
        <el-form-item label="完工条件" prop="completionConditions">
          <el-input v-model="formData.completionConditions" placeholder="请输入完工条件" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="优先级" prop="taskPriority">
          <el-select
            v-model="formData.taskPriority"
            placeholder="请选择优先级"
            clearable
            class="!w-full"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TASK_PRIORITY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="总工时" prop="taskWorkHours">
          <el-input-number
            class="!w-full"
            :min="0"
            v-model="formData.taskWorkHours"
            placeholder="请输入任务总工时"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
            v-model="formData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-form-item>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="handleClose" type="warning" :disabled="formLoading">关 闭</el-button>
      </el-form-item>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import { TaskApi, TaskVO } from '@/api/project/projectTask'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { ProjectTemplateTypeApi, ProjectTemplateTypeVO } from '@/api/project/projectTemplateType'
import { ProjectTaskUserApi, ProjectTaskUserVO } from '@/api/project/projectTaskUser'

const { query, meta } = useRoute() // 查询参数
const queryId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const formLoading = ref(false)
const formType = ref<any>('create') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  projectId: queryId,
  customerName: 0,
  describeVal: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  taskType: undefined,
  plannedDuration: undefined,
  plannedStartDate: undefined,
  plannedEndDate: undefined,
  actualStartDate: undefined,
  actualEndDate: undefined,
  completeBase: undefined,
  specifyBasis: undefined,
  completionConditions: undefined,
  remark: undefined,
  taskPhases: undefined,
  mustBeCompletedDate: undefined,
  taskPriority: undefined,
  taskWorkHours: undefined
})
const formRules = reactive({
  describeVal: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  responsibleId: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  plannedStartDate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  plannedEndDate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

let responsibleRef = ref()

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    formData.value.responsibleName = responsibleRef.value.states.selectedLabel
    const data = formData.value as unknown as TaskVO
    if (formType.value === 'create') {
      await TaskApi.createTask(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskApi.updateTask(data)
      message.success(t('common.updateSuccess'))
    }
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push({
    path: '/project/projectTask',
    query: {
      projectId: query.projectId
    }
  })
}

const fetchUserList = async () => {
  let queryData = {
    pageNo: 1,
    pageSize: 999,
    projectId: queryId,
    queryFlag: true
  }
  const data = await ProjectTaskUserApi.getProjectTaskUserPage(queryData)
  userList.value = data.list
}

const userList = ref() // 用户列表
const templateList = ref<ProjectTemplateTypeVO[]>([]) // 用户列表

const disabledDate = (time) => {
  if (!query.mustBeCompletedDate) return
  return time.getTime() > query.mustBeCompletedDate
}

onMounted(async () => {
  formType.value = query.flag
  if (formType.value === 'update') meta.title = '任务编辑'
  fetchUserList()
  // 修改时，设置数据
  if (query.id) {
    formLoading.value = true
    try {
      formData.value = await TaskApi.getTask(query.id as any)
      formData.value.customerName = Number(formData.value.customerName) as any
    } finally {
      formLoading.value = false
    }
  }
  let data = await ProjectTemplateTypeApi.getTemplateByProjectId(queryId)
  templateList.value = data
})
</script>
