<!--
 * @Description: 开票申请详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2024-10-10 16:16:51
-->

<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="任务名称">
        {{ detailData.describeVal }}
      </el-descriptions-item>
      <el-descriptions-item label="任务阶段">
        {{ detailData.taskPhases }}
      </el-descriptions-item>
      <el-descriptions-item label="负责人名称">
        {{ detailData.responsibleName }}
      </el-descriptions-item>

      <!-- <el-descriptions-item label="任务类型">
        {{ detailData.taskType }}
      </el-descriptions-item>
      <el-descriptions-item label="计划工期 天">
        {{ detailData.plannedDuration }}
      </el-descriptions-item> -->
      <el-descriptions-item label="计划开工日期">
        {{ formatDate(detailData.plannedStartDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="计划完工日期">
        {{ formatDate(detailData.plannedEndDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="实际开工日期">
        {{ formatDate(detailData.actualStartDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="实际完工日期">
        {{ formatDate(detailData.actualEndDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="完工条件">
        {{ detailData.completionConditions }}
      </el-descriptions-item>
      <el-descriptions-item label="任务优先级">
        <dict-tag :type="DICT_TYPE.TASK_PRIORITY" :value="detailData.taskPriority" />
      </el-descriptions-item>
      <el-descriptions-item label="任务总工时">
        {{ detailData.taskWorkHours ? detailData.taskWorkHours + 'H' : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="已消耗工时">
        {{ detailData.consumedWorkHours ? detailData.consumedWorkHours + 'H' : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="剩余工时">
        {{ detailData.remainWorkHours ? detailData.remainWorkHours + 'H' : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="任务状态">
        {{ getDictLabel(DICT_TYPE.TASK_STATUS, detailData.customerName) }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { TaskApi } from '@/api/project/projectTask'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ProjectDetail' })

const { query } = useRoute() // 查询参数
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await TaskApi.getTask(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push({
    path: '/project/projectTask',
    query: { ...query, id: undefined }
  })
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped></style>
