<template>
  <ContentWrap>
    <div class="flex items-center mb-20px">
      <el-button type="primary" @click="handleBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>
      <div class="font-bold text-18px mx-20px"> 项目名称：{{ projectDetailInfo.projectName }} </div>
      <div class="font-bold text-18px mx-20px" v-if="projectDetailInfo.customerName">
        客户名称：{{ projectDetailInfo.customerName }}
      </div>
      <div class="font-bold text-18px ml-20px" v-if="projectDetailInfo.contractCode">
        合同编号：{{ projectDetailInfo.contractCode }}
      </div>
    </div>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="负责人" prop="responsibleName">
        <el-input
          v-model="queryParams.responsibleName"
          placeholder="请输入负责人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="优先级" prop="taskPriority">
        <el-select
          v-model="queryParams.taskPriority"
          placeholder="请选择优先级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TASK_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="customerName">
        <el-select
          v-model="queryParams.customerName"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb-10px">
      <el-button
        type="primary"
        plain
        @click="handleAddEdit('create')"
        v-hasPermi="['pms:task:create']"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['pms:task:export']"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>

    <el-table
      height="650"
      ref="tableRef"
      v-loading="loading"
      :span-method="objectSpanMethod"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="任务阶段" align="center" prop="taskPhases" width="150" />
      <el-table-column label="任务名称" align="center" prop="describeVal">
        <template #default="scope">
          <div class="listLink" @click="openDrawer(scope.row)">
            {{ scope.row.describeVal }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="任务优先级" align="center" prop="taskPriority" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TASK_PRIORITY" :value="scope.row.taskPriority" />
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="customerName" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TASK_STATUS" :value="scope.row.customerName" />
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="responsibleName" width="120" />
      <el-table-column label="项目总工时" align="center" prop="taskWorkHours" width="100">
        <template #default="{ row }">
          {{ row.taskWorkHours ? row.taskWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="已消耗工时" align="center" prop="consumedWorkHours" width="100">
        <template #default="{ row }">
          {{ row.consumedWorkHours ? row.consumedWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="剩余工时" align="center" prop="remainWorkHours" width="100">
        <template #default="{ row }">
          {{ row.remainWorkHours ? row.remainWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="计划开工日期"
        align="center"
        prop="plannedStartDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="计划完工日期"
        align="center"
        prop="plannedEndDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="实际开工日期"
        align="center"
        prop="actualStartDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="实际完工日期"
        align="center"
        prop="actualEndDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column label="操作" align="center" width="270" fixed="right">
        <template #default="scope">
          <el-button @click="addPlan(scope.row)" type="warning" link> 添加计划 </el-button>
          <el-button @click="fillHour(scope.row.id)" type="success" link style="margin-left: 8px">
            工时
          </el-button>
          <el-button
            v-if="nickname === scope.row.responsibleName || onlySeeSelf"
            @click="handleAddEdit('update', scope.row.id)"
            type="warning"
            link
            style="margin-left: 8px"
          >
            编辑
          </el-button>

          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button
            v-if="nickname === scope.row.responsibleName || onlySeeSelf"
            @click="handleRemove(scope.row)"
            type="danger"
            link
            style="margin-left: 8px"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <!-- <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      /> -->
  </ContentWrap>

  <el-drawer class="drawer" v-model="drawerVisible" size="50%" :close-on-click-modal="true">
    <template #header>
      <div class="font-bold text-18px text-stone-950">任务名称：{{ currentRow.describeVal }} </div>
    </template>
    <el-tabs v-model="activeTab" @tab-change="tabChange">
      <el-tab-pane label="成员" name="1">
        <ProjectTaskUser ref="userRef" :taskId="currentRow.id" />
      </el-tab-pane>
      <el-tab-pane label="文件" name="2">
        <ProjectTaskFile ref="fileRef" :taskId="currentRow.id" />
      </el-tab-pane>
      <el-tab-pane label="成果" name="3">
        <ProjectTaskResult ref="resultRef" :taskId="currentRow.id" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
  <!-- 添加计划弹窗 -->
  <AddPlanDialog ref="addPlanDialogRef" :projectDetailInfo="projectDetailInfo" @success="getList" />
</template>

<script setup lang="ts">
import AddPlanDialog from './components/addPlanDialog.vue'
import { ProjectApi } from '@/api/project/applyProject'
import { getDictLabel, DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskApi, TaskVO } from '@/api/project/projectTask'
import ProjectTaskUser from '../projectTaskUser/index.vue'
import ProjectTaskFile from '../projectTaskFile/index.vue'
import ProjectTaskResult from '../projectTaskResult/index.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'

const { query } = useRoute() // 查询参数
const projectId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号

/** 任务信息 列表 */
defineOptions({ name: 'Task' })
const userStore = useUserStore()
const nickname = userStore.getUser.nickname // 当前登录的编号

const { proxy } = getCurrentInstance()!
let activeTab = ref('1')
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const drawerVisible = ref(false)

let tableRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<TaskVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 1000,
  projectId,
  customerName: undefined,
  describeVal: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  taskType: undefined,
  plannedDuration: undefined,
  plannedStartDate: [],
  plannedEndDate: [],
  actualStartDate: [],
  actualEndDate: [],
  completeBase: undefined,
  // completeBase: [],
  specifyBasis: undefined,
  // specifyBasis: [],
  completionConditions: undefined,
  remark: undefined,
  createTime: [],
  taskPriority: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const router = useRouter() // 路由
const route = useRoute()
const { delView } = useTagsViewStore() // 视图操作
const roles = userStore.getRoles // 当前登录人角色

// 只看自己的日历的角色
const onlySeeSelf = computed(() => {
  return roles.includes('super_admin') || roles.includes('project_manager') || roles.includes('pms')
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskPage(queryParams)
    list.value = data.list
    total.value = data.total
    rowspan()
  } finally {
    loading.value = false
  }
}

const currentRow = ref<any>({})
const openDrawer = (val) => {
  if (val) {
    activeTab.value = '1'
    drawerVisible.value = true
    currentRow.value = val
    const str = activeTab.value === '1' ? 'user' : val === '2' ? 'file' : 'result'
    nextTick(() => {
      ;(proxy!.$refs[`${str}Ref`] as any).getList()
    })
  }
}

const handleRemove = async (row) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskApi.deleteTask(row.id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
const handleAddEdit = (flag, id?) => {
  router.push({
    name: 'ProjectTaskCreate',
    query: {
      flag,
      id,
      projectId: route.query.projectId,
      mustBeCompletedDate: projectDetailInfo.value.mustBeCompletedDate
    }
  })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ProjectTaskDetail',
    query: {
      id: row.id,
      projectId: route.query.projectId
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskApi.exportTask(queryParams)
    download.excel(data, '任务信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const tabChange = (val) => {
  const str = val === '1' ? 'user' : val === '2' ? 'file' : 'result'
  ;(proxy!.$refs[`${str}Ref`] as any).getList()
}

let spanArr = ref()
let position = ref()
const rowspan = () => {
  //每次调用清空数据
  spanArr.value = []
  position.value = 0
  list.value.forEach((item, index) => {
    if (index === 0) {
      spanArr.value.push(1)
      position.value = 0
    } else {
      if (list.value[index].taskPhases === list.value[index - 1].taskPhases) {
        spanArr.value[position.value] += 1
        spanArr.value.push(0)
      } else {
        spanArr.value.push(1)
        position.value = index
      }
    }
  })
}

const objectSpanMethod: any = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    if (spanArr.value && spanArr.value.length > 0) {
      const _row = spanArr.value[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }
}

// 填报工时
const fillHour = (id) => {
  router.push({
    path: '/user/taskDetail',
    query: {
      taskId: id,
      backPath: route.path,
      backQuery: JSON.stringify(route.query)
    }
  })
}

const handleBack = () => {
  // 关闭当前 Tab
  delView(unref(router.currentRoute))
  router.push({
    path: '/project/projectPlan'
  })
}

const projectDetailInfo = ref<any>({})
const getProjectDetailInfo = async () => {
  const res = await ProjectApi.getProject(route.query.projectId as any)
  projectDetailInfo.value = res
}

// 添加计划
const addPlanDialogRef = ref()
const addPlan = (row) => {
  addPlanDialogRef.value.open(row)
}

/** 初始化 **/
onMounted(async () => {
  getProjectDetailInfo()
  await getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-drawer__title) {
  font-size: 56px !important;
  font-weight: bold !important;
}
.proDetail {
  padding: 0 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
