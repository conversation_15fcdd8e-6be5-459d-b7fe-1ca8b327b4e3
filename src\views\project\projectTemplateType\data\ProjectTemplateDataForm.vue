<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="模板排序" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入模板排序" />
      </el-form-item>
      <el-form-item label="模板标签" prop="label">
        <el-input v-model="formData.label" placeholder="请输入模板标签" />
      </el-form-item>
      <el-form-item label="模板键值" prop="value">
        <el-input v-model="formData.value" placeholder="请输入模板键值" />
      </el-form-item>
      <!-- <el-form-item label="模板类型" prop="dictType">
        {{  formData.dictType}}
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <!-- <el-radio label="1">请选择字典生成</el-radio> -->
          <el-radio v-for="item in getIntDictOptions('template_status')" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProjectTemplateDataApi, ProjectTemplateDataVO } from '@/api/project/projectTemplateData'
import {getIntDictOptions } from '@/utils/dict'

/** 项目模板子 表单 */
defineOptions({ name: 'ProjectTemplateDataForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  sort: undefined,
  label: undefined,
  value: undefined,
  dictType: undefined,
  status: undefined
})
const formRules = reactive({
  sort: [{ required: true, message: '模板排序不能为空', trigger: 'blur' }],
  label: [{ required: true, message: '模板标签不能为空', trigger: 'blur' }],
  value: [{ required: true, message: '模板键值不能为空', trigger: 'blur' }],
  // dictType: [{ required: true, message: '模板类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, dictType?: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  if (dictType) {
    formData.value.dictType = dictType
  }
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProjectTemplateDataApi.getProjectTemplateData(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProjectTemplateDataVO
    if (formType.value === 'create') {
      await ProjectTemplateDataApi.createProjectTemplateData(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProjectTemplateDataApi.updateProjectTemplateData(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    sort: undefined,
    label: undefined,
    value: undefined,
    dictType: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>