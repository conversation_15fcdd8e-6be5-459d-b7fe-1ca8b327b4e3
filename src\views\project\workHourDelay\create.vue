<!--
 * @Description: 工时延长新增流程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-11 13:58:23
 * @LastEditTime: 2024-07-11 10:55:34
-->

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="150px"
    v-loading="formLoading"
  >
    <el-row>
      <el-col :span="11">
        <el-form-item label="所属项目" prop="projectName">
          <div class="flex w-100%">
            <el-input
              class="mr-50px"
              v-model="formData.projectName"
              disabled
              placeholder="请选择项目"
            ></el-input>
            <el-button type="primary" @click="openProjectDialog">选择项目</el-button>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="所属任务" prop="taskId">
          <el-select
            ref="taskRef"
            filterable
            v-model="formData.taskId"
            placeholder="请选择所属任务"
            style="width: 380px"
          >
            <el-option
              v-for="item in taskList"
              :key="item.id"
              :label="item.describeVal"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="超时原因" prop="reason">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="formData.reason"
            placeholder="请输入超时原因"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="22">
        <div class="flex items-center">
          <el-form-item label="变更的工时" prop="increasedHours">
            <el-input-number v-model="formData.increasedHours" />
          </el-form-item>
          <span class="text-14px ml-15px pb-20px text-red-500">
            增加工时请填正数，减少工时请填负数。 例：如需减少 5H 工时，请填写
            <span class="font-extrabold">-5</span> 。
          </span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="变更后项目完工日期" prop="mustBeCompletedDate">
          <el-date-picker
            v-model="formData.mustBeCompletedDate"
            type="date"
            value-format="x"
            placeholder="选择变更后项目完工日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="变更后合同金额" prop="contractAmount">
          <el-input-number v-model="formData.contractAmount" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="附件" prop="attachment">
          <UploadFile
            v-model="formData.attachment"
            :file-size="5120"
            :limit="1"
            @uploading="handleUploading"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-form-item>
        <el-button @click="submitForm" type="primary" :disabled="formLoading || isUploading"
          >确 定</el-button
        >
        <el-button @click="handleClose" type="warning" :disabled="formLoading">关 闭</el-button>
      </el-form-item>
    </el-row>
    <projectDialog ref="projectDialogRef" @fetch-data="chooseProjectDone" />
  </el-form>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ProjectDelayApplicationApi } from '@/api/project/workHourDelay'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { ProjectApi } from '@/api/project/applyProject'
import { TaskApi } from '@/api/project/projectTask'
import { isArray } from '@/utils/is'
import dayjs from 'dayjs'

defineOptions({ name: 'WorkHourDelayCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  taskId: undefined,
  taskName: undefined,
  reason: undefined,
  increasedHours: undefined,
  attachment: undefined,
  remark: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  contractAmount: undefined
})
const formRules = reactive({
  projectId: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }]
  // increasedHours: [{ required: true, message: '增加的工时不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const projectRef = ref()
const taskRef = ref()

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  if (
    !formData.value.increasedHours &&
    !formData.value.mustBeCompletedDate &&
    !formData.value.contractAmount
  ) {
    return message.warning('变更的工时/变更后项目完工日期/变更后合同金额 至少填写一项')
  }
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    formData.value.taskName = taskRef.value.states.selectedLabel
    const data = formData.value
    if (isArray(data.attachment) && data.attachment.length > 0) {
      data.attachment = data.attachment.join()
    }
    delete data.id
    await ProjectDelayApplicationApi.createProjectDelayApplication(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/project/workHourDelay')
}

const projectList = ref<any>([])
const taskList = ref<any>([])
const getProjectList = async () => {
  formLoading.value = true
  try {
    const data = await ProjectApi.getProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2
    })
    projectList.value = data.list
  } finally {
    formLoading.value = false
  }
}
const getTaskList = async () => {
  formLoading.value = true
  try {
    const data = await TaskApi.getTaskPage({
      pageNo: 1,
      pageSize: 999,
      projectId: formData.value.projectId
    })
    taskList.value = data.list
  } finally {
    formLoading.value = false
  }
}

const projectDialogRef = ref()
const openProjectDialog = () => {
  projectDialogRef.value.openDialog()
}

const chooseProjectDone = (item) => {
  formData.value.taskId = undefined
  formData.value.mustBeCompletedDate = undefined
  formData.value.projectId = item.id
  formData.value.projectName = item.projectName
  getTaskList()
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
onMounted(async () => {
  getProjectList()
  if (route.query.id) {
    formData.value = await ProjectDelayApplicationApi.getProjectDelayApplication(
      route.query.id as any
    )
    getTaskList()
  }
})
</script>
