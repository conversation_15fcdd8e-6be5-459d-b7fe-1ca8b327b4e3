<!--
 * @Description: 工时延长
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-11 13:57:45
 * @LastEditTime: 2025-04-22 13:32:51
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="所属项目" prop="projectId">
        <el-select
          filterable
          v-model="queryParams.projectId"
          clearable
          placeholder="请选择所属项目"
          @keyup.enter="handleQuery"
          @change="projectChange"
          class="!w-240px"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属任务" prop="taskId">
        <el-select
          ref="taskRef"
          filterable
          v-model="queryParams.taskId"
          placeholder="请选择所属任务"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="item in taskList"
            :key="item.id"
            :label="item.describeVal"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb8">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleCreate()">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="所属项目" align="center" prop="projectName" width="250" />
      <el-table-column label="客户名" align="center" prop="customerName" width="280"/>
      <el-table-column label="合同编号" align="center" prop="contractCode" width="150" />
      <el-table-column label="所属任务" align="center" prop="taskName" width="280" />
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="超时原因" align="center" prop="reason" width="350" />
      <el-table-column label="变更的工时" align="center" prop="increasedHours" width="100">
        <template #default="scope">
          {{ scope.row.increasedHours ? `${scope.row.increasedHours}H` : '' }}
        </template>
      </el-table-column>
      <el-table-column
        label="变更后项目完工日期"
        align="center"
        prop="mustBeCompletedDate"
        :formatter="dateFormatter2"
        width="160"
      />
      <el-table-column label="变更后合同金额" align="center" prop="contractAmount" width="140" />
      <el-table-column label="备注" align="center" prop="remark" min-width="200" />
      <el-table-column label="申请人" align="center" prop="userName" width="130" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { ProjectDelayApplicationApi } from '@/api/project/workHourDelay'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ProjectApi } from '@/api/project/applyProject'
import { TaskApi } from '@/api/project/projectTask'

defineOptions({ name: 'WorkHourDelay' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const router = useRouter() // 路由
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  projectId: undefined,
  taskId: undefined,
  reason: undefined,
  increasedHours: undefined,
  attachment: undefined,
  remark: undefined,
  createTime: [],
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  customerName: undefined,
  contractCode: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectDelayApplicationApi.getProjectDelayApplicationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'WorkHourDelayCreate' })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'WorkHourDelayDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const projectList = ref<any>([])
const taskList = ref<any>([])
const getProjectList = async () => {
  loading.value = true
  try {
    const data = await ProjectApi.getProjectPage({
      pageNo: 1,
      pageSize: 999,
      result: 2
    })
    projectList.value = data.list
  } finally {
    loading.value = false
  }
}
const getTaskList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskPage({
      pageNo: 1,
      pageSize: 999,
      projectId: queryParams.projectId
    })
    taskList.value = data.list
  } finally {
    loading.value = false
  }
}

const projectChange = (val: any) => {
  queryParams.taskId = undefined
  getTaskList()
}

const reissue = (row) => {
  router.push({
    name: 'WorkHourDelayCreate',
    query: {
      id: row.id
    }
  })
}
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
  getProjectList()
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
