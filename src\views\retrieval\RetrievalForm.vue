<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="上级检索" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="categoryTree"
          :default-expanded-keys="[0]"
          :props="defaultProps"
          check-strictly
          placeholder="请选择上级检索"
        />
      </el-form-item>
      <el-form-item label="检索名称" prop="name">
        <el-input v-model="formData.name" clearable placeholder="请输入检索名称" />
      </el-form-item>
      <el-form-item label="显示排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import * as RetrievalApi from '@/api/system/retrieval'
import { defaultProps, handleTree } from '@/utils/tree'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  name: undefined,
  sort: undefined,
  parentId: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '检索名称不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const categoryTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  if (type === 'create' && id) {
    formData.value.parentId = id
  }
  // 修改时，设置数据
  if (type === 'update') {
    formLoading.value = true
    try {
      formData.value = await RetrievalApi.getCategory(id!)
    } finally {
      formLoading.value = false
    }
  }
  await getCategoryTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RetrievalApi.CategoryVO
    if (formType.value === 'create') {
      await RetrievalApi.createCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      await RetrievalApi.updateCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    parentId: undefined,
    sort: undefined
  }
  formRef.value?.resetFields()
}

/** 获得检索管理树 */
const getCategoryTree = async () => {
  categoryTree.value = []
  const data = await RetrievalApi.getCategoryList()
  const root: Tree = { id: 0, name: '顶级检索', children: [] }
  root.children = handleTree(data, 'id', 'parentId')
  categoryTree.value.push(root)
}
</script>
