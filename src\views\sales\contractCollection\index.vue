<!--
 * @Description: 应收应付列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-13 10:29:13
 * @LastEditTime: 2025-07-10 11:31:01
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <!-- <el-form-item label="开票内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入开票内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="发票类型" prop="invoiceType">
        <el-select
          v-model="queryParams.invoiceType"
          placeholder="请选择发票类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INVOICE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="申请日期" prop="applyDate">
        <el-date-picker
          v-model="queryParams.applyDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="发票号" prop="serialNumber">
        <el-input
          v-model="queryParams.serialNumber"
          placeholder="请输入发票号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同范围" prop="contractDate">
        <el-date-picker
          v-model="contractDate"
          value-format="YYYYMM"
          type="monthrange"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="签约人" prop="signedId">
        <el-select
          class="!w-240px"
          v-model="queryParams.signedId"
          filterable
          placeholder="请选择签约人"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="success"
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>

      <el-row class="statistic-row">
        <el-col :span="4" v-for="item in statisticList" :key="item.title">
          <div :class="['statistic-card', item.red ? 'red' : '']">
            <div class="statistic-title">{{ item.title }}</div>
            <div class="statistic-value">{{
              erpPriceInputFormatterWithComma(item.value.value)
            }}</div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :span-method="objectSpanMethod"
      :stripe="true"
      :show-overflow-tooltip="true"
      @sort-change="sortChange"
    >
      <el-table-column label="客户名称" prop="customerName" width="200" />
      <el-table-column label="合同编号" prop="contractCode" width="150" sortable="custom" />
      <el-table-column label="合同名称" prop="jointIssue" width="200" />
      <el-table-column
        label="合同总金额"
        prop="totalAmount"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column label="已开票总金额" prop="notInvoiceAmount" width="120">
        <!-- 2024/7/24需求：已开票总金额/已到账总金额/合同总金额 三个值相等时，蓝色字体展示已开票总金额和已到账总金额，不相等时绿色字体展示 -->
        <!-- 2024/7/25需求：已开票总金额/已到账总金额 其中有一个值为0时，两个值都红色字体展示 -->
        <template #default="scope">
          <div
            @click="handleInvoice(scope.row.contractCode)"
            style="cursor: pointer"
            :class="[
              scope.row.notInvoiceAmount === 0 || scope.row.notReceiveAmount === 0
                ? 'text-[#dc2626]'
                : scope.row.totalAmount === scope.row.notInvoiceAmount &&
                    scope.row.totalAmount === scope.row.notReceiveAmount
                  ? 'text-[#38bdf8]'
                  : 'text-green-400'
            ]"
          >
            {{ erpPriceInputFormatterWithComma(scope.row.notInvoiceAmount) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="已到账总金额" prop="notReceiveAmount" width="120">
        <template #default="scope">
          <div
            :class="[
              scope.row.notInvoiceAmount === 0 || scope.row.notReceiveAmount === 0
                ? 'text-[#dc2626]'
                : scope.row.totalAmount === scope.row.notInvoiceAmount &&
                    scope.row.totalAmount === scope.row.notReceiveAmount
                  ? 'text-[#38bdf8]'
                  : 'text-green-400'
            ]"
          >
            {{ erpPriceInputFormatterWithComma(scope.row.notReceiveAmount) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="签约人" prop="signUserName" width="120" />
      <el-table-column label="开票人" prop="invoiceUserName" width="120" />
      <el-table-column
        label="本次开票时间"
        prop="applyDate"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column
        label="下次开票日期"
        prop="nextDate"
        :formatter="dateFormatter2"
        width="130"
      />
      <el-table-column label="发票号" prop="serialNumber" width="200" />
      <el-table-column
        label="开票金额"
        prop="invoiceAmount"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column
        label="D+1到账"
        prop="receivedAmount1"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column
        label="到账时间"
        prop="receivedTime1"
        :formatter="dateFormatter2"
        width="150"
      />
      <el-table-column
        label="D+2到账"
        prop="receivedAmount2"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column
        label="到账时间"
        prop="receivedTime2"
        :formatter="dateFormatter2"
        width="150"
      />
      <el-table-column
        label="D+3到账"
        prop="receivedAmount3"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column
        label="到账时间"
        prop="receivedTime3"
        :formatter="dateFormatter2"
        width="150"
      />
      <el-table-column
        label="D+4到账"
        prop="receivedAmount4"
        width="120"
        :formatter="erpPriceTableColumnFormatterWithComma"
      />
      <el-table-column
        label="到账时间"
        prop="receivedTime4"
        :formatter="dateFormatter2"
        width="150"
      />

      <el-table-column label="开票内容" prop="content" min-width="180" />
      <el-table-column label="发票类型" prop="invoiceType" width="130">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVOICE_TYPE" :value="scope.row.invoiceType" />
        </template>
      </el-table-column>
      <el-table-column label="税率" prop="taxRate">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TAX_RATE" :value="scope.row.taxRate" />
        </template>
      </el-table-column>
      <el-table-column label="约定付款天数" prop="daysToPay" width="120" />
      <el-table-column label="开票依据" prop="invoiceBasis" min-width="300" />
      <el-table-column label="合同约定开票次数/比例" prop="billingFrequencyOrRatio" width="300" />
      <el-table-column label="纳税人编号" prop="taxpayerNumber" width="180" />
      <el-table-column label="跟进说明" prop="followUp" width="220" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="() => getList()"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { erpPriceTableColumnFormatterWithComma, erpPriceInputFormatterWithComma } from '@/utils'
import * as UserApi from '@/api/system/user'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { InvoiceApplyApi } from '@/api/sales/invoice'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 客户信息 列表 */
defineOptions({ name: 'ContractCollection' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const contractDate = ref([])

const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const totalAmount = ref(0) // 合同总金额
const invoicingTotalAmount = ref(0) // 开票总金额
const paymentTotalAmount = ref(0) // 收款总金额
const noInvoicingTotalAmount = ref(0) // 未开票金额
const noPaymentTotalAmount = ref(0) // 未支付总金额

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  content: undefined,
  customerName: undefined,
  invoiceType: undefined,
  contractCode: undefined,
  applyDate: [],
  acceptanceType: undefined,
  issued: undefined,
  serialNumber: undefined,
  abcDate: [],
  signedId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const statisticList = [
  { title: '合同总金额', value: totalAmount },
  { title: '已开票金额', value: invoicingTotalAmount },
  { title: '已到账金额', value: paymentTotalAmount },
  { title: '未开票金额', value: noInvoicingTotalAmount, red: true },
  { title: '未到账金额', value: noPaymentTotalAmount, red: true }
]

const handleInvoice = (contractCode) => {
  /** 详情操作 */
  router.push({
    path: '/crm/invoice',
    query: {
      contractCode
    }
  })
}

/** 查询列表 */
const getList = async (order?) => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams,
      sortOrder: order,
      contractQueryFrom: contractDate.value[0],
      contractQueryTo: contractDate.value[1]
    }
    const data = await InvoiceApplyApi.contractReceiptList(queryData)
    const statisticsData = await InvoiceApplyApi.contractReceiptStatistics(queryData)

    totalAmount.value = statisticsData.totalAmount
    invoicingTotalAmount.value = statisticsData.invoicingTotalAmount
    paymentTotalAmount.value = statisticsData.paymentTotalAmount
    noInvoicingTotalAmount.value = statisticsData.noInvoicingTotalAmount
    noPaymentTotalAmount.value = statisticsData.noPaymentTotalAmount

    list.value = data.list
    total.value = data.total
    getRowArr()
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  contractDate.value = []
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InvoiceApplyApi.exportInvoiceApply(queryParams)
    download.excel(data, '应收应付.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 表格列合并
const spanArr = ref()
const spanArr2 = ref()
const spanArr3 = ref()
const spanArr4 = ref()
const spanArr5 = ref()
const spanArr6 = ref()
const position = ref()
const position2 = ref()
const position3 = ref()
const position4 = ref()
const position5 = ref()
const position6 = ref()
const getRowArr = () => {
  // 每次调用清空数据
  spanArr.value = []
  spanArr2.value = []
  spanArr3.value = []
  spanArr4.value = []
  spanArr5.value = []
  spanArr6.value = []
  position.value = 0
  position2.value = 0
  position3.value = 0
  position4.value = 0
  position5.value = 0
  position6.value = 0
  list.value.forEach((item, index) => {
    if (index === 0) {
      spanArr.value.push(1)
      spanArr2.value.push(1)
      spanArr3.value.push(1)
      spanArr4.value.push(1)
      spanArr5.value.push(1)
      spanArr6.value.push(1)
      position.value = 0
      position2.value = 0
      position3.value = 0
      position4.value = 0
      position5.value = 0
      position6.value = 0
    } else {
      if (list.value[index].customerName === list.value[index - 1].customerName) {
        spanArr.value[position.value] += 1
        spanArr.value.push(0)
      } else {
        spanArr.value.push(1)
        position.value = index
      }
      if (list.value[index].contractCode === list.value[index - 1].contractCode) {
        spanArr2.value[position2.value] += 1
        spanArr2.value.push(0)
      } else {
        spanArr2.value.push(1)
        position2.value = index
      }
      if (list.value[index].jointIssue === list.value[index - 1].jointIssue) {
        spanArr3.value[position3.value] += 1
        spanArr3.value.push(0)
      } else {
        spanArr3.value.push(1)
        position3.value = index
      }
      // 合同名称相同时才合并合同总金额
      if (
        list.value[index].jointIssue === list.value[index - 1].jointIssue &&
        list.value[index].totalAmount === list.value[index - 1].totalAmount
      ) {
        spanArr4.value[position4.value] += 1
        spanArr4.value.push(0)
      } else {
        spanArr4.value.push(1)
        position4.value = index
      }
      // 合同名称相同时才合并已开票总金额
      if (
        list.value[index].jointIssue === list.value[index - 1].jointIssue &&
        list.value[index].invoicingTotalAmount === list.value[index - 1].invoicingTotalAmount
      ) {
        spanArr5.value[position5.value] += 1
        spanArr5.value.push(0)
      } else {
        spanArr5.value.push(1)
        position5.value = index
      }
      // 合同名称相同时才合并已到账总金额
      if (
        list.value[index].jointIssue === list.value[index - 1].jointIssue &&
        list.value[index].paymentTotalAmount === list.value[index - 1].paymentTotalAmount
      ) {
        spanArr6.value[position6.value] += 1
        spanArr6.value.push(0)
      } else {
        spanArr6.value.push(1)
        position6.value = index
      }
    }
  })
}

const objectSpanMethod: any = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    if (spanArr.value && spanArr.value.length > 0) {
      const _row = spanArr.value[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  } else if (columnIndex === 1) {
    const _row = spanArr2.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  } else if (columnIndex === 2) {
    const _row = spanArr3.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  } else if (columnIndex === 3) {
    const _row = spanArr4.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  } else if (columnIndex === 4) {
    const _row = spanArr5.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  } else if (columnIndex === 5) {
    const _row = spanArr6.value[rowIndex]
    const _col = _row > 0 ? 1 : 0
    return {
      rowspan: _row,
      colspan: _col
    }
  }
}

const sortChange = (column: any) => {
  const realOrder =
    column.order === 'ascending' ? 'asc' : column.order === 'descending' ? 'desc' : null
  getList(realOrder)
}

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
/** 初始化 **/
onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()
  getList()
})
</script>

<style lang="scss" scoped>
.statistic-row {
  margin-bottom: 35px;
  display: flex;
  gap: 24px;
  justify-content: space-between;
}
:deep(.el-col) {
  padding: 0 !important;
}
.statistic-card {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.12);
  padding: 24px 0 16px 0;
  min-width: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 4px 16px rgba(56, 189, 248, 0.18);
  }
}
.statistic-card.red {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}
.statistic-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 1px;
}
.statistic-value {
  font-size: 28px;
  font-weight: bold;
  color: #38bdf8;
  line-height: 1.2;
}
.statistic-card.red .statistic-value {
  color: #dc2626;
}
</style>
