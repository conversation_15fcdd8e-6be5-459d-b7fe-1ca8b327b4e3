<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="合同id" prop="contractId">
        <el-input v-model="formData.contractId" disabled />
      </el-form-item> -->
      <el-form-item label="结算方式" prop="contractCode">
        <el-input v-model="formData.contractCode" placeholder="请输入结算方式" />
      </el-form-item>
      <el-form-item label="收款约定" prop="contractName">
        <el-input v-model="formData.contractName" placeholder="请输入收款约定" />
      </el-form-item>
      <el-form-item label="结算日期" prop="settlementDate">
        <el-date-picker
          v-model="formData.settlementDate"
          type="date"
          value-format="x"
          placeholder="选择结算日期"
        />
      </el-form-item>
      <el-form-item label="收款条件" prop="paymentTerms">
        <el-input v-model="formData.paymentTerms" placeholder="请输入收款条件" />
      </el-form-item>
      <el-form-item label="百分比（%）" prop="percentage">
        <el-input v-model="formData.percentage" placeholder="请输入百分比（%）" />
      </el-form-item>
      <el-form-item label="申请金额" prop="applicationAmount">
        <el-input v-model="formData.applicationAmount" placeholder="请输入申请金额" />
      </el-form-item>
      <el-form-item label="约定金额" prop="agreedAmount">
        <el-input v-model="formData.agreedAmount" placeholder="请输入约定金额" />
      </el-form-item>
      <!-- <el-form-item label="项目" prop="project">
        <div style="display: flex; width: 100%"
          ><el-input
            style="margin-right: 50px"
            v-model="formData.project"
            disabled
            placeholder="请输入项目"
          ></el-input>
          <el-button type="primary" @click="chooseDialog">打开项目选择弹窗</el-button></div
        >
      </el-form-item> -->
      <el-form-item label="开票金额" prop="invoiceAmount">
        <el-input v-model="formData.invoiceAmount" placeholder="请输入开票金额" />
      </el-form-item>
      <el-form-item label="收款金额" prop="amountCollected">
        <el-input v-model="formData.amountCollected" placeholder="请输入收款金额" />
      </el-form-item>
      <el-form-item label="最近开票日期" prop="latestInvoicingDate">
        <el-date-picker
          v-model="formData.latestInvoicingDate"
          type="date"
          value-format="x"
          placeholder="选择最近开票日期"
        />
      </el-form-item>
      <el-form-item label="最近收款日期" prop="recentPaymentDate">
        <el-date-picker
          v-model="formData.recentPaymentDate"
          type="date"
          value-format="x"
          placeholder="选择最近收款日期"
        />
      </el-form-item>
      <!-- <el-form-item label="分组" prop="groupingVal">
        <el-input v-model="formData.groupingVal" placeholder="请输入分组" />
      </el-form-item>
      <el-form-item label="分组占比" prop="groupProportion">
        <el-input v-model="formData.groupProportion" placeholder="请输入分组占比" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  <projectDialog ref="projectDialogRef" @fetch-data="chooseProjectDone" />
</template>
<script setup lang="ts">
import projectDialog from '@/components/ProjectDialog/index.vue'
import { ContractPayStipulationApi, ContractPayStipulationVO } from '@/api/sales/contract'
const { query } = useRoute() // 查询参数
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 合同收款约定信息 表单 */
defineOptions({ name: 'ContractPayStipulationForm' })

let projectDialogRef = ref()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  contractId: queryId,
  contractCode: '固定价格',
  contractName: undefined,
  settlementDate: undefined,
  paymentTerms: undefined,
  percentage: undefined,
  applicationAmount: undefined,
  agreedAmount: undefined,
  project: undefined,
  invoiceAmount: undefined,
  amountCollected: undefined,
  latestInvoicingDate: undefined,
  recentPaymentDate: undefined,
  groupingVal: '默认分组',
  groupProportion: undefined
})
const formRules = reactive({
  contractId: [{ required: true, message: '合同id不能为空', trigger: 'blur' }],
  // contractCode: [{ required: true, message: '结算方式不能为空', trigger: 'blur' }],
  contractName: [{ required: true, message: '收款约定不能为空', trigger: 'blur' }],
  paymentTerms: [{ required: true, message: '收款条件不能为空', trigger: 'blur' }]
  // project: [{ required: true, message: '项目不能为空', trigger: 'blur' }],
  // groupingVal: [{ required: true, message: '分组不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ContractPayStipulationApi.getContractPayStipulation(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ContractPayStipulationVO
    if (formType.value === 'create') {
      await ContractPayStipulationApi.createContractPayStipulation(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractPayStipulationApi.updateContractPayStipulation(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    contractId: queryId,
    contractCode: '固定价格',
    contractName: undefined,
    settlementDate: undefined,
    paymentTerms: undefined,
    percentage: undefined,
    applicationAmount: undefined,
    agreedAmount: undefined,
    project: undefined,
    invoiceAmount: undefined,
    amountCollected: undefined,
    latestInvoicingDate: undefined,
    recentPaymentDate: undefined,
    groupingVal: '默认分组',
    groupProportion: undefined
  }
  formRef.value?.resetFields()
}

const chooseProjectDone = (item) => {
  formData.value.project = item.projectName
}

const chooseDialog = () => {
  projectDialogRef.value.openDialog()
}
</script>
