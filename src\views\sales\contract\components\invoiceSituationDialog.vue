<!--
 * @Description: 开票情况弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-25 11:14:56
 * @LastEditTime: 2024-06-26 14:03:06
-->

<template>
  <el-dialog title="开票情况" v-model="dialogVisible" width="680px" append-to-body center>
    <el-form label-width="90px">
      <el-col :span="22">
        <el-form-item label="开始时间" prop="startTime">
          {{ formatDate(formData.startTime, 'YYYY-MM-DD') }}
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          {{ formatDate(formData.endTime, 'YYYY-MM-DD') }}
        </el-form-item>
        <el-form-item label="付款方式" prop="details">
          {{ formData.details }}
        </el-form-item>
        <el-form-item label="已开票金额" prop="invoicedAmount">
          {{ invoicedAmountTotal }}
        </el-form-item>
        <div class="mb-20px pl-20px">
          <el-table :data="invoicedAmountTableData">
            <el-table-column label="序号" type="index" width="100"></el-table-column>
            <el-table-column label="申请人" prop="userName"></el-table-column>
            <el-table-column
              label="申请日期"
              align="center"
              prop="applyDate"
              :formatter="dateFormatter2"
              width="180"
            />
            <el-table-column label="税率" align="center" prop="taxRate">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.TAX_RATE" :value="scope.row.taxRate" />
              </template>
            </el-table-column>
            <el-table-column label="本次开票金额（万元）" prop="invoiceAmount"></el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="22">
        <el-form-item label="已到账金额" prop="receivedAmount">
          {{ receivedAmountTotal }}
        </el-form-item>
        <div class="mb-20px pl-20px">
          <el-table :data="receivedAmountTableData">
            <el-table-column label="序号" type="index" width="100"></el-table-column>
            <el-table-column label="客户名" prop="customerName"></el-table-column>
            <el-table-column label="收款人" prop="signedId">
              <template #default="scope">
                <span>{{ userList.find((item) => item.id === scope.row.signedId)?.nickname }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="收款日期"
              align="center"
              prop="receiveDate"
              :formatter="dateFormatter2"
              width="180px"
            />
            <el-table-column label="收款金额" align="center" prop="receiveAmount" />
          </el-table>
        </div>
      </el-col>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import * as UserApi from '@/api/system/user'
import { formatDate } from '@/utils/formatTime'
import { cloneDeep } from 'lodash-es'
import { dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'
import { InvoiceApplyApi } from '@/api/sales/invoice'
import { ContractReceiveAmountApi } from '@/api/sales/contractCollection'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const contractId = ref(null)
const formData = ref({})
const userList = ref([]) // 用户列表

//** 弹框打开事件 */
const openDialog = async (row) => {
  userList.value = await UserApi.getSimpleUserList()
  formData.value = cloneDeep(row)
  contractId.value = row.id
  getInvoicedAmount()
  getReceivedAmount()
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  dialogVisible.value = false
}

// 已开票金额
const invoicedAmountTotal = computed(() => {
  return invoicedAmountTableData.value?.reduce((sum, item) => sum + item.invoiceAmount, 0)
})
const invoicedAmountTableData = ref([])
const getInvoicedAmount = async () => {
  const data = await InvoiceApplyApi.getInvoiceApplyPage({
    contractId: contractId.value,
    queryValidData: '1',
    pageSize: 1000,
    pageNo: 1
  })
  invoicedAmountTableData.value = data.list
}

// 已到账金额
const receivedAmountTotal = computed(() => {
  return receivedAmountTableData.value?.reduce((sum, item) => sum + item.receiveAmount, 0)
})
const receivedAmountTableData = ref([])
const getReceivedAmount = async () => {
  const data = await ContractReceiveAmountApi.getContractReceiveAmountPage({
    contractId: contractId.value,
    queryValidData: '1',
    pageSize: 1000,
    pageNo: 1
  })
  receivedAmountTableData.value = data.list
}

defineExpose({
  openDialog
})
</script>
