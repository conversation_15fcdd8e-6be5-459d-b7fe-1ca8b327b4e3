<!--
 * @Description: 补充合同/追加附件弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-17 16:15:58
 * @LastEditTime: 2024-10-12 11:36:02
-->

<template>
  <el-dialog
    :title="flag === 'contract' ? '补充合同' : '追加附件'"
    v-model="dialogVisible"
    width="680px"
    append-to-body
    center
  >
    <el-form ref="formRef" label-width="90px" :model="formData">
      <el-form-item v-if="flag === 'contract'" label="合同文件" prop="contractAttachments">
        <UploadFile
          v-model="formData.contractAttachments"
          :file-size="5120"
          :limit="50"
          @uploading="handleUploading"
        />
      </el-form-item>
      <el-form-item v-if="flag === 'attachment'" label="附件" prop="attachment">
        <UploadFile
          v-model="formData.attachment"
          :file-size="5120"
          :limit="50"
          @uploading="handleUploading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="save" :disabled="isUploading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ContractApi } from '@/api/sales/contract'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash-es'
import { isArray } from '@/utils/is'

const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const formRef = ref()

const message = useMessage()
const dialogVisible = ref(false)
const formData: any = ref({
  id: undefined,
  projectId: undefined,
  contractCategoryId: undefined,
  deptId: undefined,
  deptName: undefined,
  customerId: undefined,
  customerName: undefined,
  contactsUserId: undefined,
  contactsUserName: undefined,
  collaboratorsId: undefined,
  collaboratorsName: undefined,
  contractCode: undefined,
  contractName: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  priority: undefined,
  businessClassification: undefined,
  currentContractAmount: undefined,
  currency: undefined,
  signingDate: undefined,
  signedId: undefined,
  signedUserName: undefined,
  startTime: undefined,
  endTime: undefined,
  salesManagerId: undefined,
  salesManagerName: undefined,
  contractManagerId: undefined,
  contractManagerName: undefined,
  attachment: undefined,
  details: undefined,
  contractAttachments: undefined
})
const flag = ref('')

//** 弹框打开事件 */
const openDialog = (category, row) => {
  flag.value = category
  formData.value = cloneDeep(row)
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  reset()
  dialogVisible.value = false
}
const save = () => {
  formRef.value.validate(async (valid) => {
    const data = cloneDeep(formData.value)
    if (flag.value === 'contract') {
      if (isArray(data.contractAttachments) && data.contractAttachments.length > 0) {
        data.contractAttachments = data.contractAttachments.join()
      }
    } else {
      if (isArray(data.attachment) && data.attachment.length > 0) {
        data.attachment = data.attachment.join()
      }
    }
    try {
      await ContractApi.updateContract(data)
      message.success(t('common.updateSuccess'))
      // 关闭当前 Tab
      emit('fetch-data')
      close()
    } finally {
    }
  })
}

const reset = () => {
  formRef.value.resetFields()
  formData.value = {
    id: undefined,
    projectId: undefined,
    contractCategoryId: undefined,
    deptId: undefined,
    deptName: undefined,
    customerId: undefined,
    customerName: undefined,
    contactsUserId: undefined,
    contactsUserName: undefined,
    collaboratorsId: undefined,
    collaboratorsName: undefined,
    contractCode: undefined,
    contractName: undefined,
    result: undefined,
    userId: undefined,
    processInstanceId: undefined,
    priority: undefined,
    businessClassification: undefined,
    currentContractAmount: undefined,
    currency: undefined,
    signingDate: undefined,
    signedId: undefined,
    signedUserName: undefined,
    startTime: undefined,
    endTime: undefined,
    salesManagerId: undefined,
    salesManagerName: undefined,
    contractManagerId: undefined,
    contractManagerName: undefined,
    attachment: undefined,
    details: undefined,
    contractAttachment: undefined
  }
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
defineExpose({
  openDialog
})
</script>
