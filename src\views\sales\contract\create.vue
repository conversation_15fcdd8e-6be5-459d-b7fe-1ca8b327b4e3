<template>
  <div class="contract-create-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <Icon icon="ep:document-add" />
        </div>
        <div class="header-text">
          <h1 class="page-title">合同创建</h1>
          <p class="page-subtitle">创建新的销售合同，填写相关信息</p>
        </div>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="110px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document" />
            </div>
            <h3 class="section-title">基本信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同名称" prop="contractName">
                <el-input
                  v-model="formData.contractName"
                  placeholder="请输入合同名称"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签约客户" prop="customerId">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.customerName"
                    disabled
                    placeholder="请选择签约客户"
                    class="modern-input"
                  />
                  <el-button type="primary" @click="chooseCustomer" class="action-button">
                    <Icon icon="ep:user" />
                    选择客户
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="业务种类" prop="businessCategory">
                <el-select
                  v-model="formData.businessCategory"
                  placeholder="请选择业务种类"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.BUSINESS_CATEGORY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同类别" prop="contractCategoryId">
                <el-select
                  placeholder="请选择合同类别"
                  v-model="formData.contractCategoryId"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CONTRACT_CATEGORY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同编号" prop="contractCode">
                <el-input
                  v-model="formData.contractCode"
                  placeholder="请输入合同编号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户部门" prop="customerDivision">
                <el-select
                  placeholder="请选择客户部门"
                  v-model="formData.customerDivision"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CUSTOMER_DEPT)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:office-building" />
            </div>
            <h3 class="section-title">客户信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户签约人" prop="customerContractor">
                <el-input
                  v-model="formData.customerContractor"
                  placeholder="请输入客户签约人"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签约人有效绩效" prop="signedCommissionRatio">
                <el-input
                  v-model="formData.signedCommissionRatio"
                  placeholder="请输入签约人有效绩效"
                  class="modern-input"
                  @input="(v) => (formData.signedCommissionRatio = v.replace(/[^\d.]/g, ''))"
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户电话" prop="customerPhone">
                <el-input
                  v-model="formData.customerPhone"
                  placeholder="请输入客户电话"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="签约日期" prop="signingDate">
                <el-date-picker
                  v-model="formData.signingDate"
                  type="date"
                  value-format="x"
                  placeholder="选择签约日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签约金额" prop="currentContractAmount">
                <el-input
                  @input="(v) => (formData.currentContractAmount = v.replace(/[^\d.]/g, ''))"
                  v-model="formData.currentContractAmount"
                  placeholder="请输入签约金额"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 合同详情卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:setting" />
            </div>
            <h3 class="section-title">合同详情</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="付款方式" prop="details">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  v-model="formData.details"
                  placeholder="请输入付款方式"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始日期" prop="startTime">
                <el-date-picker
                  v-model="formData.startTime"
                  type="date"
                  value-format="x"
                  placeholder="选择开始日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束日期" prop="endTime">
                <el-date-picker
                  v-model="formData.endTime"
                  type="date"
                  value-format="x"
                  placeholder="选择结束日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 组织信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:user" />
            </div>
            <h3 class="section-title">组织信息</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="所属部门" prop="deptId">
                <el-tree-select
                  v-model="formData.deptId"
                  :data="deptList"
                  default-expand-all
                  :props="defaultProps"
                  check-strictly
                  node-key="id"
                  placeholder="请选择所属部门"
                  @current-change="handleDeptChange"
                  class="modern-tree-select"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属签约人" prop="signedId">
                <el-select
                  ref="signRef"
                  filterable
                  v-model="formData.signedId"
                  placeholder="请选择所属签约人"
                  value-key="id"
                  lable-key="nickname"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属负责人" prop="salesManagerId">
                <el-select
                  ref="salesManagerRef"
                  filterable
                  v-model="formData.salesManagerId"
                  placeholder="请选择所属负责人"
                  value-key="id"
                  lable-key="nickname"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="优先级" prop="priority">
                <el-select
                  v-model="formData.priority"
                  placeholder="请选择优先级"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRIORITY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="协作人" prop="collaboratorsId">
                <el-select
                  ref="collaboratorRef"
                  filterable
                  v-model="formData.collaboratorsId"
                  placeholder="请选择协作人"
                  value-key="id"
                  lable-key="nickname"
                  class="modern-select"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="协作人有效绩效" prop="collaboratorsCommissionRatio">
                <el-input
                  v-model="formData.collaboratorsCommissionRatio"
                  placeholder="请输入协作人有效绩效"
                  class="modern-input"
                  @input="(v) => (formData.collaboratorsCommissionRatio = v.replace(/[^\d.]/g, ''))"
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 附件和备注卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document-add" />
            </div>
            <h3 class="section-title">附件和备注</h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="审批合同附件" prop="attachment">
                <UploadFile
                  v-model="formData.attachment"
                  :file-size="5120"
                  :limit="5"
                  @uploading="handleUploading"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="盖章合同附件" prop="contractAttachments">
                <UploadFile
                  v-model="formData.contractAttachments"
                  :file-size="5120"
                  :limit="5"
                  @uploading="handleUploading"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同保管" prop="jointStorage">
                <el-input
                  v-model="formData.jointStorage"
                  placeholder="请输入合同保管"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 占位，保持布局平衡 -->
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="合同说明" prop="remark">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  v-model="formData.remark"
                  placeholder="请输入合同说明"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            @click="submitForm"
            type="primary"
            :disabled="formLoading || isUploading"
            class="submit-button"
          >
            <Icon icon="ep:check" />
            确认提交
          </el-button>
          <el-button
            @click="handleClose"
            type="warning"
            :disabled="formLoading"
            class="cancel-button"
          >
            <Icon icon="ep:close" />
            取消关闭
          </el-button>
        </div>
      </el-form>
    </div>
  </div>

  <customerDialog ref="customerDialogRef" @fetch-data="chooseCustomerDone" />
</template>
<script lang="ts" setup>
import { ContractApi, ContractVO } from '@/api/sales/contract'
import { defaultProps, handleTree } from '@/utils/tree'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import customerDialog from '@/components/CustomerDialog/index.vue'
import { isArray } from '@/utils/is'

defineOptions({ name: 'ContractCreate' })

const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const route = useRoute()
const customerDialogRef = ref()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  projectId: undefined,
  contractCategoryId: undefined,
  deptId: undefined,
  deptName: undefined,
  customerId: undefined,
  customerName: undefined,
  contactsUserId: undefined,
  contactsUserName: undefined,
  collaboratorsId: undefined,
  collaboratorsCommissionRatio: undefined,
  signedCommissionRatio: undefined,
  collaboratorsName: undefined,
  contractCode: undefined,
  contractName: undefined,
  result: undefined,
  userId: undefined,
  processInstanceId: undefined,
  priority: undefined,
  businessClassification: undefined,
  currentContractAmount: undefined,
  currency: undefined,
  signingDate: undefined,
  signedId: undefined,
  signedUserName: undefined,
  startTime: undefined,
  endTime: undefined,
  salesManagerId: undefined,
  salesManagerName: undefined,
  contractManagerId: undefined,
  contractManagerName: undefined,
  attachment: undefined,
  details: undefined
})
const formRules = reactive({
  contractName: [{ required: true, message: '合同名称不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '签约客户不能为空', trigger: 'blur' }],
  contractCategoryId: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
  customerDivision: [{ required: true, message: '客户部门不能为空', trigger: 'blur' }],
  customerContractor: [{ required: true, message: '客户签约人不能为空', trigger: 'blur' }],
  signedCommissionRatio: [{ required: true, message: '签约人有效绩效不能为空', trigger: 'blur' }],
  customerPhone: [{ required: true, message: '客户电话不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '所属部门不能为空', trigger: 'blur' }],
  currentContractAmount: [{ required: true, message: '签约金额不能为空', trigger: 'blur' }],
  signingDate: [{ required: true, message: '签约日期不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '申请人不能为空', trigger: 'blur' }],
  signedId: [{ required: true, message: '所属签约人不能为空', trigger: 'blur' }],
  salesManagerId: [{ required: true, message: '所属负责人不能为空', trigger: 'blur' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
  details: [{ required: true, message: '付款方式不能为空', trigger: 'blur' }],
  attachment: [{ required: true, message: '审批合同附件不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const contactsUserRef = ref() // 表单 Ref
const signRef = ref()
const salesManagerRef = ref()
const collaboratorRef = ref()
const contractManagerRef = ref()
/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    formData.value.signedUserName = signRef.value.states.selectedLabel
    formData.value.salesManagerName = salesManagerRef.value.states.selectedLabel
    formData.value.collaboratorsName = collaboratorRef.value.states.selectedLabel
    const data = formData.value as any
    if (isArray(data.attachment) && data.attachment.length > 0) {
      data.attachment = data.attachment.join()
    }
    delete data.id
    await ContractApi.createContract(data)
    message.success('发起成功')
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  back()
}
// 部门树变化
const handleDeptChange = (data, node) => {
  formData.value.deptName = data.name
}

const deptList = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const chooseCustomer = () => {
  customerDialogRef.value.openDialog()
}

const chooseCustomerDone = (item) => {
  formData.value.customerId = item.id
  formData.value.customerName = item.customerName
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

onMounted(async () => {
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  userList.value = await UserApi.getSimpleUserList()
  if (route.query.id) {
    formData.value = await ContractApi.getContract(route.query.id as any)
  }
})
</script>

<style lang="scss" scoped>
.contract-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

/* 页面头部 */
.page-header {
  background: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 10px 40px rgba(79, 70, 229, 0.08),
    0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .header-content {
    display: flex;
    align-items: center;

    .header-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-right: 20px;
      box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    }

    .header-text {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        letter-spacing: -0.5px;
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

/* 表单容器 */
.form-container {
  .modern-form {
    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        line-height: 1.5;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-picker {
          .el-input__wrapper {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        .el-textarea {
          .el-textarea__inner {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            font-size: 14px;
            color: #374151;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &:focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        .el-tree-select {
          .el-input__wrapper {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }
        }
      }
    }
  }
}

/* 表单分区 */
.form-section {
  background: #ffffff;
  border-radius: 18px;
  padding: 28px;
  margin-bottom: 20px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &:nth-child(1) {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &:nth-child(2) {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      &:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &:nth-child(4) {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
    }

    .section-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }
}

/* 输入框带按钮 */
.input-with-button {
  display: flex;
  gap: 12px;
  align-items: flex-end;

  .modern-input {
    flex: 1;
  }

  .action-button {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
    }

    .el-icon {
      margin-right: 6px;
    }
  }
}

/* 操作按钮区域 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }

  .cancel-button {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .contract-create-container {
    padding: 20px;
  }

  .page-header {
    padding: 24px;

    .header-content {
      .header-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-right: 16px;
      }

      .header-text {
        .page-title {
          font-size: 24px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }
    }
  }

  .form-section {
    padding: 24px;

    .section-header {
      .section-title {
        font-size: 18px;
      }
    }
  }
}

@media (max-width: 768px) {
  .contract-create-container {
    padding: 16px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 16px;

    .header-content {
      flex-direction: column;
      text-align: center;

      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
    }
  }

  .form-section {
    padding: 20px;
    margin-bottom: 16px;

    .section-header {
      .section-title {
        font-size: 16px;
      }
    }
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .contract-create-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;

    .header-content {
      .header-text {
        .page-title {
          font-size: 20px;
        }

        .page-subtitle {
          font-size: 13px;
        }
      }
    }
  }

  .form-section {
    padding: 16px;

    .section-header {
      .section-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-right: 12px;
      }

      .section-title {
        font-size: 15px;
      }
    }
  }
}
</style>
