<!--
 * @Description: 详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-20 16:49:24
 * @LastEditTime: 2025-07-10 11:45:31
-->
<template>
  <div class="contract-detail-wrap">
    <el-button v-if="showBack" type="primary" class="cd-back-btn" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <div class="cd-card-grid">
      <!-- 基本信息 -->
      <div class="cd-card">
        <div class="cd-card-title">基本信息</div>
        <div class="cd-info-row"><span>申请人：</span><b>{{ detailData.userName }}</b></div>
        <div class="cd-info-row"><span>合同名称：</span><b>{{ detailData.contractName }}</b></div>
        <div class="cd-info-row"><span>合同编号：</span><b>{{ detailData.contractCode }}</b></div>
        <div class="cd-info-row"><span>优先级：</span><dict-tag :type="DICT_TYPE.PRIORITY" :value="detailData.priority" /></div>
        <div class="cd-info-row"><span>业务分类：</span><dict-tag :type="DICT_TYPE.BUSINESS_CATEGORY" :value="detailData.businessCategory" /></div>
        <div class="cd-info-row"><span>合同类别：</span><dict-tag :type="DICT_TYPE.CONTRACT_CATEGORY" :value="detailData.contractCategoryId" /></div>
      </div>
      <!-- 客户信息 -->
      <div class="cd-card">
        <div class="cd-card-title">客户信息</div>
        <div class="cd-info-row"><span>签约客户：</span><b>{{ detailData.customerName }}</b></div>
        <div class="cd-info-row"><span>客户部门：</span><dict-tag :type="DICT_TYPE.CUSTOMER_DEPT" :value="detailData.customerDivision" /></div>
        <div class="cd-info-row"><span>客户签约人：</span>{{ detailData.customerContractor }}</div>
        <div class="cd-info-row"><span>客户电话：</span>{{ detailData.customerPhone }}</div>
      </div>
      <!-- 合同信息 -->
      <div class="cd-card">
        <div class="cd-card-title">合同信息</div>
        <div class="cd-info-row"><span>签约日期：</span>{{ formatDate(detailData.signingDate, 'YYYY-MM-DD') }}</div>
        <div class="cd-info-row"><span>签约金额：</span><b class="cd-highlight">{{ erpPriceInputFormatterWithComma(detailData.currentContractAmount) }}</b></div>
        <div class="cd-info-row"><span>付款方式：</span>{{ detailData.details }}</div>
        <div class="cd-info-row"><span>开始日期：</span>{{ formatDate(detailData.startTime, 'YYYY-MM-DD') }}</div>
        <div class="cd-info-row"><span>结束日期：</span>{{ formatDate(detailData.endTime, 'YYYY-MM-DD') }}</div>
        <div class="cd-info-row"><span>合同保管：</span>{{ detailData.jointStorage }}</div>
        <div class="cd-info-row"><span>合同说明：</span>{{ detailData.remark }}</div>
      </div>
      <!-- 协作与绩效 -->
      <div class="cd-card">
        <div class="cd-card-title">协作与绩效</div>
        <div class="cd-info-row"><span>所属部门：</span>{{ detailData.deptName }}</div>
        <div class="cd-info-row"><span>所属签约人：</span>{{ detailData.signedUserName }}</div>
        <div class="cd-info-row"><span>所属负责人：</span>{{ detailData.salesManagerName }}</div>
        <div class="cd-info-row"><span>协作人：</span>{{ detailData.collaboratorsName }}</div>
        <div class="cd-info-row"><span>协作人有效绩效：</span>{{ detailData.collaboratorsCommissionRatio }}</div>
        <div class="cd-info-row"><span>签约人有效绩效：</span>{{ detailData.signedCommissionRatio }}</div>
      </div>
      <!-- 附件与说明 -->
      <div class="cd-card cd-card-attach">
        <div class="cd-card-title">附件与说明</div>
        <div class="cd-info-row"><span>审批合同附件：</span><FileListPreview :fileUrl="detailData.attachmentList" /></div>
        <div class="cd-info-row"><span>盖章合同附件：</span><FileListPreview :fileUrl="detailData.contractAttachment" /></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { erpPriceInputFormatterWithComma } from '@/utils'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ContractApi } from '@/api/sales/contract'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ContractDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ContractApi.getContract(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}

defineExpose({
  open: getInfo, // 提供 open 方法，用于打开弹窗
  detailData // 暴露详情数据供父组件访问
})

/** 初始化 **/
onMounted(() => {
  getInfo()
})
</script>

<style scoped lang="scss">
.contract-detail-wrap {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 0 24px 0;
}
.cd-back-btn {
  margin-bottom: 18px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 15px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  box-shadow: 0 2px 8px rgba(102,126,234,0.10);
  &:hover {
    background: linear-gradient(90deg, #5a67d8 0%, #6b47b2 100%);
    color: #fff;
  }
}
.cd-card-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px 28px;
  @media (max-width: 900px) {
    grid-template-columns: 1fr;
    gap: 18px;
  }
}
.cd-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 16px rgba(102,126,234,0.10);
  padding: 28px 28px 18px 28px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 0;
  min-height: 180px;
  position: relative;
  .cd-card-title {
    font-size: 20px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 18px;
    letter-spacing: 0.5px;
    background: linear-gradient(90deg, #e0e7ff 0%, #f3f4f6 100%);
    border-radius: 8px;
    padding: 6px 18px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(102,126,234,0.04);
  }
  .cd-info-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 15px;
    margin-bottom: 6px;
    span {
      color: #64748b;
      min-width: 90px;
      font-weight: 500;
      flex-shrink: 0;
    }
    b {
      color: #334155;
      font-weight: 700;
      font-size: 15px;
    }
    .cd-highlight {
      color: #e26a6a;
      font-weight: 700;
      font-size: 16px;
    }
  }
}
.cd-card-attach {
  grid-column: span 2;
  @media (max-width: 900px) {
    grid-column: span 1;
  }
  .cd-info-row {
    align-items: center;
    span {
      min-width: 120px;
    }
  }
}
</style>
