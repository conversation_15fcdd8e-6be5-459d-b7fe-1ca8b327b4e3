<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="130px"
    >
      <el-form-item label="合同名称" prop="contractName">
        <el-input v-model="formData.contractName" disabled placeholder="请输入合同名称" />
      </el-form-item>
      <el-form-item label="客户签约人" prop="signedUserName">
        <el-input v-model="formData.signedUserName" disabled />
      </el-form-item>
      <el-form-item label="签约人有效绩效" prop="signedCommissionRatio">
        <el-input
          v-model="formData.signedCommissionRatio"
          placeholder="请输入签约人有效绩效"
          @input="(v) => (formData.signedCommissionRatio = v.replace(/[^\d.]/g, ''))"
        >
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="协作人" prop="collaboratorsId">
        <el-select
          ref="collaboratorRef"
          filterable
          v-model="formData.collaboratorsId"
          placeholder="请选择协作人"
          value-key="id"
          lable-key="nickname"
          class="modern-select"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="协作人有效绩效" prop="collaboratorsCommissionRatio">
        <el-input
          v-model="formData.collaboratorsCommissionRatio"
          placeholder="请输入协作人有效绩效"
          @input="(v) => (formData.collaboratorsCommissionRatio = v.replace(/[^\d.]/g, ''))"
        >
          <template #append>%</template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ContractApi, ContractVO } from '@/api/sales/contract'
import * as UserApi from '@/api/system/user'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const collaboratorRef = ref()
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('修改提成') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({})
const formRules = reactive({
  signedCommissionRatio: [{ required: true, message: '签约人有效绩效不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  userList.value = await UserApi.getSimpleUserList()
  formData.value = row
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['fetch-data'])
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    formData.value.collaboratorsName = collaboratorRef.value.states.selectedLabel
    const data = formData.value
    await ContractApi.updateContract(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('fetch-data')
  } finally {
    formLoading.value = false
  }
}
</script>
