<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务种类" prop="businessCategory">
        <el-select
          v-model="queryParams.businessCategory"
          class="!w-240px"
          clearable
          placeholder="请选择所属行业"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BUSINESS_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="contractCode">
        <el-tree-select
          class="!w-240px"
          v-model="queryParams.deptId"
          :data="deptList"
          default-expand-all
          :props="defaultProps"
          check-strictly
          node-key="id"
          placeholder="请选择部门"
        />
      </el-form-item>

      <el-form-item label="所属签约人" prop="signedUserName">
        <el-input
          v-model="queryParams.signedUserName"
          placeholder="请输入所属签约人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务分类" prop="businessCategory">
        <el-select
          v-model="queryParams.businessCategory"
          class="!w-240px"
          clearable
          placeholder="请选择业务分类"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BUSINESS_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同类别" prop="contractCategoryId">
        <el-select
          class="!w-240px"
          placeholder="请选择合同类别"
          v-model="queryParams.contractCategoryId"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CONTRACT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="result">
        <el-select v-model="queryParams.result" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb5">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleCreate()">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @sort-change="sortChange"
    >
      <el-table-column label="合同类别" align="center" prop="contractCategoryId" width="250">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.CONTRACT_CATEGORY, scope.row.contractCategoryId) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="业务种类" prop="businessCategory">
        <template #default="scope">
          <span>{{ getDictLabel(DICT_TYPE.CUSTOMER_NATURE, scope.row.businessCategory) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属部门" align="center" prop="deptName" width="250" />
      <el-table-column
        label="合同编号"
        align="center"
        prop="contractCode"
        width="150"
        sortable="custom"
      />
      <el-table-column label="合同名称" align="center" prop="contractName" width="250" />
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" prop="customerName" width="270" />
      <el-table-column label="所属签约人" align="center" prop="signedUserName" width="100" />
      <el-table-column label="合同金额" align="center" prop="currentContractAmount" width="120" :formatter="erpPriceTableColumnFormatterWithComma" />
      <el-table-column
        label="签约日期"
        align="center"
        prop="signingDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column label="付款方式" align="center" prop="details" width="150" />
      <el-table-column label="操作" align="center" fixed="right" width="500">
        <template #default="scope">
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button
            link
            type="primary"
            @click="handleProcessDetail(scope.row)"
            v-if="scope.row.result !== 8"
          >
            进度
          </el-button>
          <!-- <el-button
            link
            type="primary"
            @click="handlePayment(scope.row)"
            v-if="scope.row.result !== 8"
          >
            收款约定
          </el-button> -->
          <el-button
            link
            type="primary"
            @click="jumpTo(scope.row)"
            v-if="scope.row.hasProject == 0"
          >
            立项申请
          </el-button>
          <el-button
            link
            type="primary"
            @click="projectTo(scope.row)"
            v-if="scope.row.hasProject == 1"
          >
            项目进展
          </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
          <el-button
            link
            :type="scope.row.contractAttachment ? 'primary' : 'danger'"
            @click="supplementaryContract(scope.row)"
            v-if="scope.row.result === 2"
          >
            合同</el-button
          >
          <el-button link type="primary" @click="supplementaryAttachment(scope.row)">
            附件</el-button
          >
          <el-button
            link
            type="primary"
            @click="invoiceSituation(scope.row)"
            v-if="scope.row.result === 2"
          >
            开票情况
          </el-button>
          <el-button
            link
            type="primary"
            @click="handlePurchase(scope.row)"
            v-if="scope.row.result === 2"
          >
            采购申请
          </el-button>
          <el-button link type="primary" @click="handleEdit(scope.row)"> 有效绩效 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="paginationChange"
    />
    <!-- 合同/追加附件弹窗 -->
    <SupplementaryDialog ref="supplementaryDialogRef" @fetch-data="getList" />
    <!-- 开票情况弹窗 -->
    <InvoiceSituationDialog ref="invoiceSituationDialogRef" />
    <EditDialog ref="editDialogRef" @fetch-data="getList" />
    <!-- 文件预览 -->
    <PreviewDialog ref="previewDialogRef" />
  </ContentWrap>
</template>

<script setup lang="ts">
import SupplementaryDialog from './components/supplementaryDialog.vue'
import InvoiceSituationDialog from './components/invoiceSituationDialog.vue'
import EditDialog from './editDialog.vue'
import { defaultProps, handleTree } from '@/utils/tree'
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { erpPriceTableColumnFormatterWithComma } from '@/utils'
import { ContractApi, ContractVO } from '@/api/sales/contract'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import * as LawManageApi from '@/api/bpm/lawmanage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import download from '@/utils/download'

defineOptions({ name: 'Contract' })

const router = useRouter() // 路由
const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  contractName: undefined,
  deptId: undefined,
  customerId: undefined,
  contractCategoryId: undefined,
  customerName: route.query.customerName
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const deptList = ref<Tree[]>([]) // 树形结构

/** 查询列表 */
const getList = async (order?) => {
  loading.value = true
  try {
    if (!order) {
      order = 'desc'
    }
    const data = await ContractApi.getContractPage({ ...queryParams, sortOrder: order })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const jumpTo = (row) => {
  router.push({
    name: 'ProjectCreate',
    query: {
      contractId: row.id,
      contractName: row.contractName,
      customerName: row.customerName
    }
  })
}

const projectTo = (row) => {
  router.push({
    path: '/statistics/projectHours',
    query: {
      customerName: row.customerName
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'ContentCreate' })
}

/** 详情操作 */
const handleDetail = (row: LawManageApi.LawManageVO) => {
  router.push({
    name: 'ContentDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ContractApi.exportContract(queryParams)
    download.excel(data, '合同信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}
/** 收款约定 */
const handlePayment = (row) => {
  router.push({
    name: 'ContentPayment',
    query: {
      id: row.id,
      contractName: row.contractName
    }
  })
}

const sortChange = (column: any) => {
  const realOrder =
    column.order === 'ascending' ? 'asc' : column.order === 'descending' ? 'desc' : null
  getList(realOrder)
}

const paginationChange = () => {
  getList()
}

const reissue = (row) => {
  router.push({
    name: 'ContentCreate',
    query: {
      id: row.id
    }
  })
}

const supplementaryDialogRef = ref()
const previewDialogRef = ref()
const supplementaryContract = (row) => {
  supplementaryDialogRef.value.openDialog('contract', row)
}

const supplementaryAttachment = (row) => {
  supplementaryDialogRef.value.openDialog('attachment', row)
}

const invoiceSituationDialogRef = ref()
const editDialogRef = ref()
const invoiceSituation = (row) => {
  invoiceSituationDialogRef.value.openDialog(row)
}
const handleEdit = (row) => {
  editDialogRef.value.open(row)
}
const handlePurchase = (row) => {
  router.push({
    name: 'ErpPurchaseRequestCreate',
    query: {
      contractCode: row.contractCode,
      contractName: row.contractName
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  if (route.query.contractCode) {
    queryParams.contractCode = route.query.contractCode
  }
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

const isAlreadyEnter = ref(false)
onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
