<template>
  <ContentWrap>
    <div class="flex items-center mb-20px">
      <el-button type="primary" @click="goBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>
      <div class="font-bold text-xl ml-20px"> 合同名称：{{ query.contractName }} </div>
    </div>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="结算方式" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入结算方式"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="收款约定" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入收款约定"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="收款条件" prop="paymentTerms">
        <el-input
          v-model="queryParams.paymentTerms"
          placeholder="请输入收款条件"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb5">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="openForm('create')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="结算方式" align="center" prop="contractCode" width="120" />
      <el-table-column label="收款约定" align="center" prop="contractName" width="150" />
      <el-table-column
        label="结算日期"
        align="center"
        prop="settlementDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="收款条件" align="center" prop="paymentTerms" width="120" />
      <el-table-column label="百分比（%）" align="center" prop="percentage" width="120" />
      <el-table-column label="申请金额" align="center" prop="applicationAmount" width="120" />
      <el-table-column label="约定金额" align="center" prop="agreedAmount" width="120" />
      <el-table-column label="项目" align="center" prop="project" min-width="180" />
      <el-table-column label="开票金额" align="center" prop="invoiceAmount" width="120" />
      <el-table-column label="收款金额" align="center" prop="amountCollected" width="120" />
      <el-table-column
        label="最近开票日期"
        align="center"
        prop="latestInvoicingDate"
        :formatter="dateFormatter2"
        width="150px"
      />
      <el-table-column
        label="最近收款日期"
        align="center"
        prop="recentPaymentDate"
        :formatter="dateFormatter2"
        width="150px"
      />
      <!-- <el-table-column label="分组" align="center" prop="groupingVal" width="160" />
      <el-table-column label="分组占比" align="center" prop="groupProportion" width="120" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter2"
        width="150px"
      />
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractPayStipulationForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { defaultProps, handleTree } from '@/utils/tree'
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { ContractPayStipulationApi, ContractPayStipulationVO } from '@/api/sales/contract'
import * as LawManageApi from '@/api/bpm/lawmanage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import ContractPayStipulationForm from './ContractPayStipulationForm.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ContractPayment' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const { query } = useRoute() // 查询参数
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  contractId: queryId
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractPayStipulationApi.getContractPayStipulationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractPayStipulationApi.deleteContractPayStipulation(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/sales/contract')
}

/** 初始化 **/
onMounted(async () => {
  getList()
})
</script>
