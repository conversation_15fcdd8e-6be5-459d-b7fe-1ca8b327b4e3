<template>
  <Dialog width="1280px" :title="dialogTitle" v-model="dialogVisible" class="business-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="business-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><User /></el-icon>
            </div>
            <h2 class="section-title">基本信息</h2>
            <div class="section-line"></div>
          </div>
          <div class="form-grid">
            <el-col>
              <el-form-item label="客户名称" prop="customerName" class="custom-form-item">
                <el-input
                  v-model="formData.customerName"
                  placeholder="请输入客户名称"
                  class="custom-input"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户别称" prop="customerFullName" class="custom-form-item">
                <el-input
                  v-model="formData.customerFullName"
                  placeholder="请输入客户别称"
                  class="custom-input"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="上级客户" prop="parentCompany" class="custom-form-item">
                <el-input v-model="formData.parentCompany" placeholder="请输入上级客户" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户类别" prop="customerGroup" class="custom-form-item">
                <el-select v-model="formData.customerGroup">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CUSTOMER_GROUP)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="社会信用代码" prop="socialCreditCode" class="custom-form-item">
                <el-input v-model="formData.socialCreditCode" placeholder="请输入社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="成立日期" prop="establishTime" class="custom-form-item">
                <el-date-picker
                  v-model="formData.establishTime"
                  type="date"
                  value-format="x"
                  placeholder="选择成立日期"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="公司电话" prop="companyPhone" class="custom-form-item">
                <el-input v-model="formData.companyPhone" placeholder="请输入公司电话" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户部门" prop="customerDivision" class="custom-form-item">
                <el-select v-model="formData.customerDivision">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CUSTOMER_DEPT)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户联系人" prop="contacts1" class="custom-form-item">
                <el-input v-model="formData.contacts1" placeholder="请输入公司联系人" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户电话/手机" prop="telephone1" class="custom-form-item">
                <el-input v-model="formData.telephone1" placeholder="请输入客户电话/手机" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="注册地区" prop="area" class="custom-form-item">
                <el-input v-model="formData.area" placeholder="请输入注册地区" />
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item label="注册地址" prop="address" class="custom-form-item">
                <div class="flex w-100%">
                  <el-input
                    class="mr-50px"
                    v-model="formData.address"
                    disabled
                    placeholder="请选择注册地址"
                  ></el-input>
                  <el-button type="primary" @click="chooseLocation('address')"
                    >选择注册地址</el-button
                  >
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item label="经营地址" prop="businessAddress " class="custom-form-item">
                <div class="flex w-100%">
                  <el-input
                    class="mr-50px"
                    v-model="formData.businessAddress"
                    disabled
                    placeholder="请选择经营地址"
                  ></el-input>
                  <el-button type="primary" @click="chooseLocation('businessAddress')"
                    >选择经营地址</el-button
                  >
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="注册资金" prop="registeredCapital" class="custom-form-item">
                <el-input
                  @input="(v) => (formData.registeredCapital = v.replace(/[^\d.]/g, ''))"
                  v-model="formData.registeredCapital"
                  placeholder="请输入注册资金"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="官方网站" prop="companyWebsite" class="custom-form-item">
                <el-input v-model="formData.companyWebsite" placeholder="请输入官方网站" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="人员规模" prop="numberEmployees" class="custom-form-item">
                <el-select v-model="formData.numberEmployees">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.NUMBER_EMPLOYEES)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="经营面积" prop="operatingArea" class="custom-form-item">
                <el-select v-model="formData.operatingArea">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.OPERATING_AREA)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="所属行业" prop="industry" class="custom-form-item">
                <el-select v-model="formData.industry">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.INDUSTRY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户来源" prop="sourceId" class="custom-form-item">
                <el-select v-model="formData.sourceId">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.SOURCE_ID)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="所属部门" prop="deptId" class="custom-form-item">
                <el-tree-select
                  v-model="formData.deptId"
                  :data="deptList"
                  default-expand-all
                  :props="defaultProps"
                  check-strictly
                  node-key="id"
                  placeholder="请选择所属部门"
                  @current-change="handleDeptChange"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="所属联系人" prop="responsibleId" class="custom-form-item">
                <el-select
                  ref="responsibleRef"
                  filterable
                  v-model="formData.responsibleId"
                  placeholder="请选择所属联系人"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="协作人" prop="collaboratorsId" class="custom-form-item">
                <el-select
                  ref="collaboratorRef"
                  filterable
                  v-model="formData.collaboratorsId"
                  placeholder="请选择协作人"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.nickname"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="客户分级" prop="classification" class="custom-form-item">
                <el-select v-model="formData.classification">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CLASSIFICATION)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="第一次签约日期" prop="firstSigningDate" class="custom-form-item">
                <el-date-picker
                  v-model="formData.firstSigningDate"
                  type="date"
                  value-format="x"
                  placeholder="选择第一次签约日期"
                />
              </el-form-item>
            </el-col>
          </div>
        </div>
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><User /></el-icon>
            </div>
            <h2 class="section-title">其他信息</h2>
            <div class="section-line"></div>
          </div>
          <el-row :gutter="10">
            <el-col :span="22">
              <el-form-item label="备注" prop="remark" class="custom-form-item">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  v-model="formData.remark"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="submitForm"
          type="primary"
          :disabled="formLoading"
          class="submit-button"
          size="large"
        >
          <el-icon><Check /></el-icon>
          确认提交
        </el-button>
        <el-button @click="dialogVisible = false" class="cancel-button" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </div>
    </template>
    <Map ref="mapChooseRef" @btnAddress="chooseLocationDone" />
  </Dialog>
</template>
<script setup lang="ts">
import { CustomerApi, CustomerVO } from '@/api/sales/customerInfo'
import { defaultProps, handleTree } from '@/utils/tree'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 客户信息 表单 */
defineOptions({ name: 'CustomerForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  deptId: undefined,
  deptName: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  collaboratorsId: undefined,
  collaboratorsName: undefined,
  customerName: undefined,
  customerFullName: undefined,
  parentCompany: undefined,
  customerGroup: undefined,
  registeredCapital: undefined,
  socialCreditCode: undefined,
  establishTime: undefined,
  firstSigningDate: undefined,
  companyWebsite: undefined,
  industry: undefined,
  institutionalNature: undefined,
  numberEmployees: undefined,
  annualIncome: undefined,
  classification: undefined,
  sourceId: undefined,
  taxpayerIdentificationNumber: undefined,
  openingBank: undefined,
  address: undefined,
  account: undefined,
  expireDays: undefined,
  remark: undefined,
  contacts1: undefined,
  telephone1: undefined,
  mobilePhone1: undefined,
  contacts2: undefined,
  telephone2: undefined,
  mobilePhone2: undefined,
  contacts3: undefined,
  telephone3: undefined,
  mobilePhone3: undefined,
  customerDivision: undefined,
  companyPhone: undefined,
  businessAddress: undefined,
  operatingArea: undefined,
  latitude: undefined,
  longitude: undefined,
  businessLongitude: undefined,
  businessLatitude: undefined
})
const formRules = reactive({
  customerName: [{ required: true, message: '客户名不能为空', trigger: 'blur' }],
  socialCreditCode: [{ required: true, message: '社会信用代码不能为空', trigger: 'blur' }],
  establishTime: [{ required: true, message: '成立日期不能为空', trigger: 'blur' }],
  companyPhone: [{ required: true, message: '公司电话不能为空', trigger: 'blur' }],
  contacts1: [{ required: true, message: '客户联系人不能为空', trigger: 'blur' }],
  area: [{ required: true, message: '注册地区不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '注册地址不能为空', trigger: 'blur' }],
  businessAddress: [{ required: true, message: '经营地址不能为空', trigger: 'blur' }],
  registeredCapital: [{ required: true, message: '注册资金不能为空', trigger: 'blur' }],
  numberEmployees: [{ required: true, message: '人员规模不能为空', trigger: 'blur' }],
  industry: [{ required: true, message: '所属行业不能为空', trigger: 'blur' }],
  sourceId: [{ required: true, message: '客户来源不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '所属部门不能为空', trigger: 'blur' }],
  responsibleId: [{ required: true, message: '所属联系人不能为空', trigger: 'blur' }],
  firstSigningDate: [{ required: true, message: '第一次签约日期不能为空', trigger: 'blur' }],
  classification: [{ required: true, message: '客户分级不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomerApi.getCustomer(id)
    } finally {
      formLoading.value = false
    }
  }
  userList.value = await UserApi.getSimpleUserList()
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const responsibleRef = ref()
const collaboratorRef = ref()
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    formData.value.responsibleName = responsibleRef.value.states.selectedLabel
    formData.value.collaboratorsName = collaboratorRef.value.states.selectedLabel
    formData.value.taxpayerIdentificationNumber = collaboratorRef.value.socialCreditCode
    const data = formData.value as unknown as CustomerVO
    if (formType.value === 'create') {
      await CustomerApi.createCustomer(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomerApi.updateCustomer(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    deptId: undefined,
    deptName: undefined,
    responsibleId: undefined,
    responsibleName: undefined,
    collaboratorsId: undefined,
    collaboratorsName: undefined,
    customerName: undefined,
    customerFullName: undefined,
    parentCompany: undefined,
    customerGroup: undefined,
    registeredCapital: undefined,
    socialCreditCode: undefined,
    establishTime: undefined,
    firstSigningDate: undefined,
    companyWebsite: undefined,
    industry: undefined,
    institutionalNature: undefined,
    numberEmployees: undefined,
    annualIncome: undefined,
    classification: undefined,
    sourceId: undefined,
    taxpayerIdentificationNumber: undefined,
    openingBank: undefined,
    address: undefined,
    account: undefined,
    expireDays: undefined,
    remark: undefined,
    customerDivision: undefined,
    companyPhone: undefined,
    businessAddress: undefined,
    operatingArea: undefined,
    latitude: undefined,
    longitude: undefined,
    businessLongitude: undefined,
    businessLatitude: undefined
  }
  formRef.value?.resetFields()
}

// 部门树变化
const handleDeptChange = (data, node) => {
  formData.value.deptName = data.name
}

const mapChooseRef = ref()
const chooseLocation = (key) => {
  mapChooseRef.value.openDialog(key)
}
const chooseLocationDone = (coord, addresstext, adcode, field) => {
  if (!coord || !addresstext || !field) return
  const coordArr = coord.split(',')
  if (field === 'address') {
    formData.value.longitude = coordArr[0]
    formData.value.latitude = coordArr[1]
  } else if (field === 'businessAddress') {
    formData.value.businessLongitude = coordArr[0]
    formData.value.businessLatitude = coordArr[1]
  }
  formData.value[field] = addresstext
}
</script>

<style lang="scss" scoped>
.business-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    .el-dialog__body {
      padding: 0;
      background: #f8fafc;
    }
    .el-dialog__footer {
      background: white;
      padding: 24px 32px;
      border-top: 1px solid #e5e7eb;
    }
  }
}
.form-container {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    &:hover {
      background: #a8a8a8;
    }
  }
}
.business-form {
  .form-section {
    background: white;
    border-radius: 0;
    padding: 24px 32px;
    margin-bottom: 0;
    box-shadow: none;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: none;
      transform: none;
    }
    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
  }
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;
    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      .el-icon {
        font-size: 20px;
        color: white;
      }
    }
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
      flex: 1;
    }
    .section-line {
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      border-radius: 1px;
      flex: 1;
      margin-left: 16px;
    }
  }
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    .form-item-wrapper {
      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }
  .custom-form-item {
    margin-bottom: 0;
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
      line-height: 1.5;
    }
    :deep(.el-form-item__content) {
      line-height: 1;
    }
    :deep(.el-form-item__error) {
      font-size: 12px;
      color: #ef4444;
      margin-top: 4px;
    }
  }
  .custom-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-select {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-date-picker {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      font-size: 14px;
      color: #1f2937;
      resize: vertical;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-radio-group {
    :deep(.el-radio) {
      margin-right: 20px;
      .el-radio__label {
        color: #374151;
        font-size: 14px;
      }
      .el-radio__input.is-checked .el-radio__inner {
        border-color: #667eea;
        background: #667eea;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #667eea;
      }
    }
  }
  .input-with-button {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    .custom-input {
      flex: 1;
    }
    .customer-button {
      white-space: nowrap;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
  .product-content {
    padding: 0;
    :deep(.el-tabs) {
      .el-tabs__header {
        margin-bottom: 20px;
        .el-tabs__nav-wrap {
          &::after {
            display: none;
          }
        }
        .el-tabs__item {
          color: #6b7280;
          font-weight: 500;
          &.is-active {
            color: #667eea;
          }
        }
        .el-tabs__active-bar {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
@media (max-width: 768px) {
  .business-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  .business-form {
    .form-section {
      padding: 20px;
    }
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    .input-with-button {
      flex-direction: column;
      align-items: stretch;
    }
  }
  .dialog-footer {
    flex-direction: column;
    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}
</style>
