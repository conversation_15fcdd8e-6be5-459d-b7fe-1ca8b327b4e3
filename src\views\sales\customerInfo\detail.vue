<!--
 * @Description: 详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-20 16:49:24
 * @LastEditTime: 2024-06-25 17:18:36
-->
<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBackContract">
      <Icon icon="ep:search" class="mr-5px" /> &nbsp;关联的合同
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="客户名称">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户别称">
        {{ detailData.customerFullName }}
      </el-descriptions-item>
      <el-descriptions-item label="上级客户">
        {{ detailData.parentCompany }}
      </el-descriptions-item>
      <el-descriptions-item label="客户类别">
        <dict-tag :type="DICT_TYPE.CUSTOMER_GROUP" :value="detailData.customerGroup" />
      </el-descriptions-item>
      <el-descriptions-item label="社会信用代码">
        {{ detailData.socialCreditCode }}
      </el-descriptions-item>
      <el-descriptions-item label="成立日期">
        {{ formatDate(detailData.establishTime, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="公司电话">
        {{ detailData.companyPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="客户部门">
        {{ detailData.customerDivision }}
      </el-descriptions-item>
      <el-descriptions-item label="客户联系人">
        {{ detailData.contacts1 }}
      </el-descriptions-item>
      <el-descriptions-item label="客户电话">
        {{ detailData.telephone1 }}
      </el-descriptions-item>
      <el-descriptions-item label="注册地区">
        {{ detailData.area }}
      </el-descriptions-item>
      <el-descriptions-item label="注册地址">
        {{ detailData.address }}
      </el-descriptions-item>
      <el-descriptions-item label="经营地址">
        {{ detailData.businessAddress }}
      </el-descriptions-item>
      <el-descriptions-item label="注册资金">
        {{ detailData.registeredCapital }}
      </el-descriptions-item>
      <el-descriptions-item label="官方网站">
        {{ detailData.companyWebsite }}
      </el-descriptions-item>
      <el-descriptions-item label="人员规模">
        <dict-tag :type="DICT_TYPE.NUMBER_EMPLOYEES" :value="detailData.numberEmployees" />
      </el-descriptions-item>
      <el-descriptions-item label="经营面积">
        <dict-tag :type="DICT_TYPE.OPERATING_AREA" :value="detailData.operatingArea" />
      </el-descriptions-item>
      <el-descriptions-item label="所属行业">
        <dict-tag :type="DICT_TYPE.INDUSTRY" :value="detailData.industry" />
      </el-descriptions-item>
      <el-descriptions-item label="纳税人识别号">
        {{ detailData.taxpayerIdentificationNumber }}
      </el-descriptions-item>
      <el-descriptions-item label="客户来源">
        <dict-tag :type="DICT_TYPE.SOURCE_ID" :value="detailData.sourceId" />
      </el-descriptions-item>
      <el-descriptions-item label="所属部门">
        {{ detailData.deptName }}
      </el-descriptions-item>
      <el-descriptions-item label="所属联系人">
        {{ detailData.responsibleName }}
      </el-descriptions-item>
      <el-descriptions-item label="协作人名称">
        {{ detailData.collaboratorsName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户分级">
        <dict-tag :type="DICT_TYPE.CLASSIFICATION" :value="detailData.classification" />
      </el-descriptions-item>
      <el-descriptions-item label="第一次签约日期">
        {{ formatDate(detailData.firstSigningDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { CustomerApi, CustomerVO } from '@/api/sales/customerInfo'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'CustomerInfoDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await CustomerApi.getCustomer(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push('/sales/customerInfo')
}

const goBackContract = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push({
    path: '/sales/contract',
    query: {
      customerId: props.id || queryId,
      customerName: detailData.value.customerName
    }
  })
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(async () => {
  getInfo()
})
</script>
