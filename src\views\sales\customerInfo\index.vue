<!--
 * @Description: 客户信息
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2024-04-26 14:31:51
 * @LastEditTime: 2025-07-04 10:41:44
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="所属联系人" prop="responsibleId">
        <el-select
          v-model="queryParams.responsibleId"
          filterable
          placeholder="请选择所属联系人"
          value-key="id"
          lable-key="nickname"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名"
          @keyup.enter="handleQ<PERSON>"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="所属行业" prop="industry">
        <el-select v-model="queryParams.industry">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INDUSTRY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="机构性质" prop="institutionalNature">
        <el-select v-model="queryParams.institutionalNature">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INSTITUTIONAL_NATURE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="人员规模" prop="numberEmployees">
        <el-select v-model="queryParams.numberEmployees">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.NUMBER_EMPLOYEES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>

      <el-row :gutter="10" class="mb5">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="openForm('create')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="所属联系人" align="center" prop="responsibleName" width="250" />
      <el-table-column label="协作人" align="center" prop="collaboratorsName" width="250" />
      <el-table-column label="注册地址" align="center" prop="address" />
      <el-table-column label="联系电话" align="center" prop="telephone1" width="200" />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CustomerForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { CustomerApi, CustomerVO } from '@/api/sales/customerInfo'
import CustomerForm from './CustomerForm.vue'
import { defaultProps, handleTree } from '@/utils/tree'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 客户信息 列表 */
defineOptions({ name: 'Customer' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CustomerVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined,
  deptName: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  customerName: undefined,
  customerFullName: undefined,
  parentCompany: undefined,
  customerGroup: undefined,
  registeredCapital: undefined,
  socialCreditCode: undefined,
  establishTime: [],
  firstSigningDate: undefined,
  companyWebsite: undefined,
  industry: undefined,
  institutionalNature: undefined,
  numberEmployees: undefined,
  annualIncome: undefined,
  classification: undefined,
  sourceId: undefined,
  taxpayerIdentificationNumber: undefined,
  openingBank: undefined,
  address: undefined,
  account: undefined,
  expireDays: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustomerApi.getCustomerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'CustomerInfoDetail',
    query: {
      id: row.id
    }
  })
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomerApi.deleteCustomer(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CustomerApi.exportCustomer(queryParams)
    download.excel(data, '客户信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const deptList = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
/** 初始化 **/
onMounted(async () => {
  getList()
  userList.value = await UserApi.getSimpleUserList()
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
})
</script>
