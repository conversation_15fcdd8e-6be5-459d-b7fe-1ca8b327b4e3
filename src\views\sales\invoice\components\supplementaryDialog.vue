<!--
 * @Description: 追加附件弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-10 10:21:10
 * @LastEditTime: 2024-07-10 10:30:22
-->
<template>
  <el-dialog title="追加附件" v-model="dialogVisible" width="680px" append-to-body center>
    <el-form ref="formRef" label-width="90px" :model="formData">
      <el-form-item label="附件" prop="annex">
        <UploadFile
          v-model="formData.annex"
          :file-size="5120"
          :limit="50"
          @uploading="handleUploading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="save" :disabled="isUploading">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { InvoiceApplyApi } from '@/api/sales/invoice'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash-es'
import { isArray } from '@/utils/is'

const { t } = useI18n()
const emit = defineEmits(['fetch-data'])
const formRef = ref()

const message = useMessage()
const dialogVisible = ref(false)
const formData: any = ref({
  id: undefined,
  userId: undefined,
  applyDate: undefined,
  userName: undefined,
  invoiceType: undefined,
  taxRate: undefined,
  contractId: undefined,
  jointIssue: undefined,
  content: undefined,
  totalAmount: undefined,
  acceptanceType: undefined,
  issued: undefined,
  daysToPay: undefined,
  paymentTermNote: undefined,
  billingFrequencyOrRatio: undefined,
  invoicedAmount: undefined,
  receivedAmount: undefined,
  invoiceAmount: undefined,
  companyName: undefined,
  taxpayerNumber: undefined,
  address: undefined,
  mobile: undefined,
  bankName: undefined,
  bankAccount: undefined,
  billingBank: undefined,
  submitFlag: undefined,
  urgentInvoiceRemark: undefined,
  remark: undefined,
  annex: undefined,
  result: undefined,
  processInstanceId: undefined,
  contractCode: undefined,
  invoiceBasis: undefined
})

//** 弹框打开事件 */
const openDialog = (row) => {
  formData.value = cloneDeep(row)
  dialogVisible.value = true
}
// 关闭弹框并重置操作
const close = () => {
  reset()
  dialogVisible.value = false
}
const save = () => {
  formRef.value.validate(async (valid) => {
    const data = cloneDeep(formData.value)
    if (isArray(data.annex) && data.annex.length > 0) {
      data.annex = data.annex.join()
    }
    try {
      await InvoiceApplyApi.updateInvoiceApply(data)
      message.success(t('common.updateSuccess'))
      // 关闭当前 Tab
      emit('fetch-data')
      close()
    } finally {
    }
  })
}

const reset = () => {
  formRef.value.resetFields()
  formData.value = {
    id: undefined,
    userId: undefined,
    applyDate: undefined,
    userName: undefined,
    invoiceType: undefined,
    taxRate: undefined,
    contractId: undefined,
    jointIssue: undefined,
    content: undefined,
    totalAmount: undefined,
    acceptanceType: undefined,
    issued: undefined,
    daysToPay: undefined,
    paymentTermNote: undefined,
    billingFrequencyOrRatio: undefined,
    invoicedAmount: undefined,
    receivedAmount: undefined,
    invoiceAmount: undefined,
    companyName: undefined,
    taxpayerNumber: undefined,
    address: undefined,
    mobile: undefined,
    bankName: undefined,
    bankAccount: undefined,
    billingBank: undefined,
    submitFlag: undefined,
    urgentInvoiceRemark: undefined,
    remark: undefined,
    annex: undefined,
    result: undefined,
    processInstanceId: undefined,
    contractCode: undefined,
    invoiceBasis: undefined
  }
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
defineExpose({
  openDialog
})
</script>
