<!--
 * @Description: 开票申请新增流程
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2025-07-04 13:43:14
-->
<template>
  <div class="invoice-create-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <Icon icon="ep:document-add" />
        </div>
        <div class="header-text">
          <h1 class="page-title">开票申请创建</h1>
          <p class="page-subtitle">创建新的开票申请，填写相关信息</p>
        </div>
      </div>
    </div>
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="110px"
        v-loading="formLoading"
        class="modern-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:document" />
            </div>
            <h3 class="section-title">基本信息</h3>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发票类型" prop="invoiceType">
                <el-select
                  placeholder="请选择发票类型"
                  v-model="formData.invoiceType"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.INVOICE_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税率" prop="taxRate">
                <el-select
                  placeholder="请选择税率"
                  v-model="formData.taxRate"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.TAX_RATE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开票内容" prop="content">
                <el-input
                  v-model="formData.content"
                  placeholder="请输入开票内容"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="付款账期特殊说明" prop="paymentTermNote">
                <el-input
                  v-model="formData.paymentTermNote"
                  placeholder="请输入付款账期特殊说明"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="完工验收方式" prop="acceptanceType">
                <el-select
                  v-model="formData.acceptanceType"
                  placeholder="请选择完工验收方式"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.ACCPETANCE_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="完工单是否发出" prop="issued">
                <el-select
                  v-model="formData.issued"
                  placeholder="请选择完工单是否发出"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.IS_ORDER_ISSUED)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="到账日期" prop="dzDate">
                <el-date-picker
                  v-model="formData.dzDate"
                  type="date"
                  class="modern-date-picker"
                  value-format="x"
                  placeholder="选择到账日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同名称" prop="jointIssue">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.jointIssue"
                    disabled
                    placeholder="请选择合同"
                    class="modern-input"
                  />
                  <el-button type="primary" @click="chooseContract" class="action-button"
                    >选择合同</el-button
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同编号" prop="contractCode">
                <el-input
                  v-model="formData.contractCode"
                  placeholder="合同编号"
                  class="modern-input"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同总金额" prop="totalAmount">
                <el-input
                  @input="(v) => (formData.totalAmount = v.replace(/[^.]/g, ''))"
                  v-model="formData.totalAmount"
                  placeholder="合同总金额"
                  class="modern-input"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 合同信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:office-building" />
            </div>
            <h3 class="section-title">合同信息</h3>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开票公司名称" prop="companyName">
                <el-input
                  v-model="formData.companyName"
                  placeholder="请输入开票公司名称"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="合同约定开票次数/比例"
                prop="billingFrequencyOrRatio"
                label-width="180px"
              >
                <el-input
                  type="textarea"
                  :rows="3"
                  v-model="formData.billingFrequencyOrRatio"
                  placeholder="请输入合同约定开票次数/比例"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 金额信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:setting" />
            </div>
            <h3 class="section-title">金额信息</h3>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="已开票金额" prop="invoicedAmount">
                <el-input
                  v-model="invoicedAmountTotal"
                  placeholder="已开票金额"
                  class="modern-input"
                  disabled
                />
              </el-form-item>
              <div class="mb-20px pl-50px">
                <el-table :data="invoicedAmountTableData">
                  <el-table-column label="序号" type="index" width="100"></el-table-column>
                  <el-table-column label="申请人" prop="userName"></el-table-column>
                  <el-table-column
                    label="申请日期"
                    align="center"
                    prop="applyDate"
                    :formatter="dateFormatter2"
                    width="180"
                  />
                  <el-table-column label="税率" align="center" prop="taxRate">
                    <template #default="scope">
                      <dict-tag :type="DICT_TYPE.TAX_RATE" :value="scope.row.taxRate" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="本次开票金额（元）"
                    prop="invoiceAmount"
                  ></el-table-column>
                </el-table>
              </div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="已到账金额" prop="receivedAmount">
                <el-input
                  v-model="receivedAmountTotal"
                  placeholder="已到账金额"
                  class="modern-input"
                  disabled
                />
              </el-form-item>
              <div class="mb-20px pl-50px">
                <el-table :data="receivedAmountTableData">
                  <el-table-column label="序号" type="index" width="100"></el-table-column>
                  <el-table-column label="客户名" prop="customerName"></el-table-column>
                  <el-table-column label="收款人" prop="receiveUser"></el-table-column>
                  <el-table-column
                    label="收款日期"
                    align="center"
                    prop="receiveDate"
                    :formatter="dateFormatter2"
                    width="180px"
                  />
                  <el-table-column label="收款金额" align="center" prop="receiveAmount" />
                </el-table>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="本次开票金额（元）" prop="invoiceAmount" label-width="180px">
                <el-input
                  @input="handleInvoiceAmountInput"
                  v-model="formData.invoiceAmount"
                  placeholder="请输入本次开票金额"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowNextDate">
              <el-form-item label="下次开票日期" prop="nextDate">
                <el-date-picker
                  v-model="formData.nextDate"
                  type="date"
                  value-format="x"
                  placeholder="选择下次开票日期"
                  class="modern-date-picker"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 其他信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <Icon icon="ep:user" />
            </div>
            <h3 class="section-title">其他信息</h3>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="本次开票依据" prop="invoiceBasis">
                <el-input
                  v-model="formData.invoiceBasis"
                  placeholder="请输入本次开票依据"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人编号" prop="taxpayerNumber">
                <el-input
                  v-model="formData.taxpayerNumber"
                  placeholder="请输入纳税人编号"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="合同原件是否提交项目管理人"
                prop="submitFlag"
                label-width="220px"
              >
                <el-select
                  v-model="formData.submitFlag"
                  placeholder="合同原件是否提交项目管理人"
                  class="modern-select"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.SUBMIT_FLAG)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="加急开票说明" prop="urgentInvoiceRemark">
                <el-input
                  v-model="formData.urgentInvoiceRemark"
                  placeholder="请输入加急开票说明"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  v-model="formData.remark"
                  placeholder="请输入备注"
                  class="modern-textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="附件" prop="annex">
                <UploadFile
                  v-model="formData.annex"
                  :file-size="5120"
                  :limit="1"
                  @uploading="handleUploading"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            @click="submitForm"
            type="primary"
            :disabled="formLoading || isUploading"
            class="submit-button"
            >确 定</el-button
          >
          <el-button
            @click="handleClose"
            type="warning"
            :disabled="formLoading"
            class="cancel-button"
            >关 闭</el-button
          >
        </div>
        <ContractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { InvoiceApplyApi, InvoiceApplyVO } from '@/api/sales/invoice'
import { ContractReceiveAmountApi } from '@/api/sales/contractCollection'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'
import { isArray } from '@/utils/is'

defineOptions({ name: 'InvoiceCreate' })
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter() // 路由
const route = useRoute()
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  userId: undefined,
  applyDate: undefined,
  userName: undefined,
  invoiceType: undefined,
  taxRate: undefined,
  contractId: undefined,
  jointIssue: undefined,
  content: undefined,
  totalAmount: undefined,
  acceptanceType: undefined,
  issued: undefined,
  daysToPay: undefined,
  paymentTermNote: undefined,
  billingFrequencyOrRatio: undefined,
  invoicedAmount: undefined,
  receivedAmount: undefined,
  invoiceAmount: undefined,
  companyName: undefined,
  taxpayerNumber: undefined,
  address: undefined,
  mobile: undefined,
  bankName: undefined,
  bankAccount: undefined,
  billingBank: undefined,
  submitFlag: undefined,
  urgentInvoiceRemark: undefined,
  remark: undefined,
  annex: undefined,
  result: undefined,
  processInstanceId: undefined,
  contractCode: undefined,
  invoiceBasis: undefined,
  nextDate: undefined
})
const formRules = reactive({
  invoiceType: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  taxRate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  applyDate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  taxpayerNumber: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  invoiceBasis: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  invoiceAmount: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  nextDate: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const contractDialogRef = ref()
// 处理输入事件
const handleInvoiceAmountInput = (v) => {
  // 允许输入负数、数字和小数点
  formData.value.invoiceAmount = v
    .replace(/[^\d.-]/g, '')
    .replace(/^-?\d*\.?\d*$/, '$&')
    .replace('--', '-')
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  // 提交请求
  formLoading.value = true
  try {
    formData.value.invoicedAmount = invoicedAmountTotal.value
    formData.value.receivedAmount = receivedAmountTotal.value
    const data = formData.value as any
    if (isArray(data.annex) && data.annex.length > 0) {
      data.annex = data.annex.join()
    }
    delete data.id
    await InvoiceApplyApi.createInvoiceApply(data)
    message.success(t('common.createSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push({ name: 'Invoice' })
}

const chooseContract = () => {
  contractDialogRef.value.openDialog()
}
const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractCode = item.contractCode
  formData.value.jointIssue = item.contractName
  formData.value.totalAmount = item.currentContractAmount
  formData.value.billingFrequencyOrRatio = item.details
  formData.value.companyName = item.customerName
  formData.value.taxpayerNumber = item.taxpayerIdentificationNumber
  getInvoicedAmount()
  getReceivedAmount()
}

// 已开票金额列表
const invoicedAmountTotal = computed(() => {
  return invoicedAmountTableData.value?.reduce((sum, item) => sum + item.invoiceAmount, 0)
})
const invoicedAmountTableData = ref<any>([])
const getInvoicedAmount = async () => {
  const data = await InvoiceApplyApi.getInvoiceApplyPage({
    contractId: formData.value.contractId,
    queryValidData: '1',
    pageSize: 1000,
    pageNo: 1
  })
  invoicedAmountTableData.value = data.list
}

// 已到账金额
const receivedAmountTotal = computed(() => {
  return receivedAmountTableData.value?.reduce((sum, item) => sum + item.receiveAmount, 0)
})
const receivedAmountTableData = ref<any>([])
const getReceivedAmount = async () => {
  const data = await ContractReceiveAmountApi.getContractReceiveAmountPage({
    contractId: formData.value.contractId,
    queryValidData: '1',
    pageSize: 1000,
    pageNo: 1
  })
  receivedAmountTableData.value = data.list
}

// 是否展示下次开票日期字段
const isShowNextDate = computed(() => {
  if (!formData.value.invoiceAmount || !formData.value.totalAmount) return false
  // 当 本次开票金额 + 已开票金额 < 合同总金额 时，才展示 下次开票日期 字段
  return (
    Number(formData.value.invoiceAmount) + Number(invoicedAmountTotal.value) <
    Number(formData.value.totalAmount)
  )
})

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}

onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()
  if (route.query.id) {
    formData.value = await InvoiceApplyApi.getInvoiceApply(route.query.id as any)
    // 字典值回显问题
    formData.value.taxRate = formData.value.taxRate && Number(formData.value.taxRate)
    formData.value.invoiceType = formData.value.invoiceType && Number(formData.value.invoiceType)
    formData.value.acceptanceType =
      formData.value.acceptanceType && Number(formData.value.acceptanceType)
    formData.value.issued = formData.value.issued && Number(formData.value.issued)
    formData.value.submitFlag = formData.value.submitFlag && Number(formData.value.submitFlag)
    if (formData.value.contractId) {
      getInvoicedAmount()
      getReceivedAmount()
    }
  }
})
</script>

<style lang="scss" scoped>
// 复制自合同创建页的样式
.contract-create-container,
.invoice-create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
}

.page-header {
  background: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 10px 40px rgba(79, 70, 229, 0.08),
    0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .header-content {
    display: flex;
    align-items: center;

    .header-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-right: 20px;
      box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    }

    .header-text {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        letter-spacing: -0.5px;
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

.form-container {
  .modern-form {
    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        line-height: 1.5;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-picker {
          .el-input__wrapper {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &.is-focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
          }

          .el-input__inner {
            font-size: 14px;
            color: #374151;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        .el-textarea {
          .el-textarea__inner {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            font-size: 14px;
            color: #374151;

            &:hover {
              border-color: #4f46e5;
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
            }

            &:focus {
              border-color: #4f46e5;
              box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

.input-with-button {
  display: flex;
  gap: 12px;
  align-items: flex-end;

  .modern-input {
    flex: 1;
  }

  .action-button {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
    }

    .el-icon {
      margin-right: 6px;
    }
  }
}

.form-section {
  background: #ffffff;
  border-radius: 18px;
  padding: 28px;
  margin-bottom: 20px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &:nth-child(1) {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &:nth-child(2) {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      &:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &:nth-child(4) {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
    }

    .section-title {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(79, 70, 229, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .submit-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }

  .cancel-button {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 32px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      background: #9ca3af;
      box-shadow: none;
      transform: none;
    }

    .el-icon {
      margin-right: 8px;
    }
  }
}

@media (max-width: 1200px) {
  .contract-create-container,
  .invoice-create-container {
    padding: 20px;
  }
  .page-header {
    padding: 24px;
    .header-content {
      .header-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-right: 16px;
      }
      .header-text {
        .page-title {
          font-size: 24px;
        }
        .page-subtitle {
          font-size: 14px;
        }
      }
    }
  }
  .form-section {
    padding: 24px;
    .section-header {
      .section-title {
        font-size: 18px;
      }
    }
  }
}
@media (max-width: 768px) {
  .contract-create-container,
  .invoice-create-container {
    padding: 16px;
  }
  .page-header {
    padding: 20px;
    margin-bottom: 16px;
    .header-content {
      flex-direction: column;
      text-align: center;
      .header-icon {
        margin-right: 0;
        margin-bottom: 16px;
      }
    }
  }
  .form-section {
    padding: 20px;
    margin-bottom: 16px;
    .section-header {
      .section-title {
        font-size: 16px;
      }
    }
  }
  .form-actions {
    flex-direction: column;
    gap: 12px;
    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}
@media (max-width: 480px) {
  .contract-create-container,
  .invoice-create-container {
    padding: 12px;
  }
  .page-header {
    padding: 16px;
    .header-content {
      .header-text {
        .page-title {
          font-size: 20px;
        }
        .page-subtitle {
          font-size: 13px;
        }
      }
    }
  }
  .form-section {
    padding: 16px;
    .section-header {
      .section-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-right: 12px;
      }
      .section-title {
        font-size: 15px;
      }
    }
  }
}
</style>
