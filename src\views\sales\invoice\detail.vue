<!--
 * @Description: 开票申请详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2025-07-10 11:44:48
-->

<template>
  <div class="invoice-detail-wrap">
    <div class="mb-10px flex justify-between">
      <el-button v-if="showBack" type="primary" class="id-back-btn" @click="goBack">
        <el-icon><Back /></el-icon> &nbsp;返 回
      </el-button>
      <el-button type="success" class="id-export-btn" @click="exportPDF">
        <el-icon><Download /></el-icon>
        &nbsp;导 出
      </el-button>
    </div>
    <div class="id-card-grid">
      <!-- 申请信息 -->
      <div class="id-card">
        <div class="id-card-title">申请信息</div>
        <div class="id-info-row"><span>申请日期：</span>{{ formatDate(detailData.applyDate, 'YYYY-MM-DD') }}</div>
        <div class="id-info-row"><span>申请人：</span>{{ detailData.userName }}</div>
        <div class="id-info-row"><span>发票类型：</span>{{ getDictLabel(DICT_TYPE.INVOICE_TYPE, detailData.invoiceType) }}</div>
        <div class="id-info-row"><span>税率：</span>{{ getDictLabel(DICT_TYPE.TAX_RATE, detailData.taxRate) }}</div>
        <div class="id-info-row"><span>开票内容：</span>{{ detailData.content }}</div>
        <div class="id-info-row"><span>付款账期特殊说明：</span>{{ detailData.paymentTermNote }}</div>
        <div class="id-info-row"><span>加急开票说明：</span>{{ detailData.urgentInvoiceRemark }}</div>
      </div>
      <!-- 合同信息 -->
      <div class="id-card">
        <div class="id-card-title">合同信息</div>
        <div class="id-info-row"><span>合同名称：</span>{{ detailData.jointIssue }}</div>
        <div class="id-info-row"><span>合同编号：</span>{{ detailData.contractCode }}</div>
        <div class="id-info-row"><span>合同总金额：</span><b class="id-highlight">{{ erpPriceInputFormatterWithComma(detailData.totalAmount) }}</b></div>
        <div class="id-info-row"><span>合同约定开票次数/比例：</span>{{ detailData.billingFrequencyOrRatio }}</div>
        <div class="id-info-row"><span>合同原件是否提交项目管理人：</span>{{ getDictLabel(DICT_TYPE.SUBMIT_FLAG, detailData.submitFlag) }}</div>
      </div>
      <!-- 开票信息 -->
      <div class="id-card">
        <div class="id-card-title">开票信息</div>
        <div class="id-info-row"><span>本次开票金额（元）：</span><b class="id-highlight">{{ erpPriceInputFormatterWithComma(detailData.invoiceAmount) }}</b></div>
        <div class="id-info-row"><span>本次开票依据：</span>{{ detailData.invoiceBasis }}</div>
        <div class="id-info-row"><span>下次开票日期：</span>{{ formatDate(detailData.nextDate, 'YYYY-MM-DD') }}</div>
        <div class="id-info-row"><span>完工验收方式：</span>{{ getDictLabel(DICT_TYPE.ACCPETANCE_TYPE, detailData.acceptanceType) }}</div>
        <div class="id-info-row"><span>完工单是否发出：</span>{{ getDictLabel(DICT_TYPE.IS_ORDER_ISSUED, detailData.issued) }}</div>
      </div>
      <!-- 财务信息 -->
      <div class="id-card">
        <div class="id-card-title">财务信息</div>
        <div class="id-info-row"><span>到账日期：</span>{{ formatDate(detailData.dzDate, 'YYYY-MM-DD') }}</div>
        <div class="id-info-row"><span>开票公司名称：</span>{{ detailData.companyName }}</div>
        <div class="id-info-row"><span>纳税人编号：</span>{{ detailData.taxpayerNumber }}</div>
        <div class="id-info-row"><span>发票号：</span>{{ detailData.serialNumber }}</div>
      </div>
      <!-- 附件与备注 -->
      <div class="id-card id-card-attach">
        <div class="id-card-title">附件与备注</div>
        <div class="id-info-row"><span>附件：</span><FileListPreview :fileUrl="detailData.annex" /></div>
        <div class="id-info-row"><span>备注：</span>{{ detailData.remark }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { erpPriceInputFormatterWithComma } from '@/utils'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { InvoiceApplyApi } from '@/api/sales/invoice'
import { useTagsViewStore } from '@/store/modules/tagsView'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

defineOptions({ name: 'InvoiceDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await InvoiceApplyApi.getInvoiceApply(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push({ name: 'Invoice' })
}
const exportPDF = async () => {
  // 获取要导出的Vue组件
  const exportContent = document.querySelector('.invoice-detail-wrap') as any
  if (!exportContent) {
    console.error('导出元素未找到')
    return
  }
  let elDescriptionCell = document.querySelectorAll('.el-descriptions__cell') as any
  elDescriptionCell.forEach((element) => {
    element.style.height = '60px'
    element.style.fontSize = '24px'
  })
  let domHeight = exportContent.offsetHeight
  let maxHeight = 64257
  html2canvas(exportContent, {
    useCORS: true, // 如果截图的内容里有图片,可能会有跨域的情况,加上这个参数,解决文件跨域问题
    scale: maxHeight / domHeight > 1 ? 1 : maxHeight / domHeight
  }).then((canvas) => {
    const contentWidth = canvas.width
    const contentHeight = canvas.height
    let pageHeight = (contentWidth / 592.28) * 841.89
    let leftHeight = contentHeight
    //页面偏移
    var position = 0
    //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
    var imgWidth = 595.28 // A4 宽度
    var imgHeight = (592.28 / contentWidth) * contentHeight // A4总高度
    var pageData = canvas.toDataURL('image/jpeg', 1.0)
    var pdf = new jsPDF(undefined, 'pt', 'a4')
    //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
    //当内容未超过pdf一页显示的范围，无需分页
    if (leftHeight < pageHeight) {
      pdf.addImage(pageData, 'JPEG', 10, 0, imgWidth - 20, imgHeight)
    } else {
      while (leftHeight > 0) {
        pdf.addImage(pageData, 'JPEG', 10, position, imgWidth - 20, imgHeight)
        leftHeight -= pageHeight
        position -= 841.89
        // 避免添加空白页
        if (leftHeight > 0) {
          pdf.addPage()
        }
      }
    }
    pdf.save(`开票详情-${detailData.value.content}.pdf`)
    elDescriptionCell.forEach((element) => {
      element.style.height = ''
      element.style.fontSize = ''
    })
  })
}

/** 初始化 **/
onMounted(() => {
  getInfo()
})

defineExpose({ open: getInfo })
</script>

<style scoped lang="scss">
.invoice-detail-wrap {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 0 24px 0;
}
.id-back-btn, .id-export-btn {
  border-radius: 8px;
  font-weight: 600;
  font-size: 15px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.10);
}
.id-back-btn {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  &:hover {
    background: linear-gradient(90deg, #5a67d8 0%, #6b47b2 100%);
    color: #fff;
  }
}
.id-export-btn {
  background: linear-gradient(90deg, #10b981 0%, #667eea 100%);
  color: #fff;
  border: none;
  &:hover {
    background: linear-gradient(90deg, #0ea672 0%, #5a67d8 100%);
    color: #fff;
  }
}
.id-card-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px 28px;
  @media (max-width: 900px) {
    grid-template-columns: 1fr;
    gap: 18px;
  }
}
.id-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 16px rgba(102,126,234,0.10);
  padding: 28px 28px 18px 28px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 0;
  min-height: 180px;
  position: relative;
  .id-card-title {
    font-size: 20px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 18px;
    letter-spacing: 0.5px;
    background: linear-gradient(90deg, #e0e7ff 0%, #f3f4f6 100%);
    border-radius: 8px;
    padding: 6px 18px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(102,126,234,0.04);
  }
  .id-info-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 15px;
    margin-bottom: 6px;
    span {
      color: #64748b;
      min-width: 120px;
      font-weight: 500;
      flex-shrink: 0;
    }
    b {
      color: #334155;
      font-weight: 700;
      font-size: 15px;
    }
    .id-highlight {
      color: #e26a6a;
      font-weight: 700;
      font-size: 16px;
    }
  }
}
.id-card-attach {
  grid-column: span 2;
  @media (max-width: 900px) {
    grid-column: span 1;
  }
  .id-info-row {
    align-items: center;
    span {
      min-width: 120px;
    }
  }
}
</style>
