<!--
 * @Description: 开票申请列表页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2025-07-10 11:42:34
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="118px"
    >
      <el-form-item label="开票公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入开票公司名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开票内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入开票内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="发票类型" prop="invoiceType">
        <el-select
          v-model="queryParams.invoiceType"
          placeholder="请选择发票类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INVOICE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请日期" prop="applyDate">
        <el-date-picker
          v-model="queryParams.applyDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="完工验收方式" prop="acceptanceType">
        <el-select
          v-model="queryParams.acceptanceType"
          placeholder="请选择完工验收方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ACCPETANCE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完工单是否发出" prop="issued">
        <el-select
          v-model="queryParams.issued"
          placeholder="请选择完工单是否发出"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.IS_ORDER_ISSUED)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发票号" prop="serialNumber">
        <el-input
          v-model="queryParams.serialNumber"
          placeholder="请输入发票号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb8">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleCreate()">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
        <!-- <el-col :span="3">
          <el-statistic title="合同总金额" :value="totalAmount" />
        </el-col> -->
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="开票公司名称" align="center" prop="companyName" width="280" />
      <el-table-column
        label="申请日期"
        align="center"
        prop="applyDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column label="申请人" align="center" prop="userName" width="100" />
      <el-table-column label="发票类型" align="center" prop="invoiceType" width="130">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVOICE_TYPE" :value="scope.row.invoiceType" />
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="发票号" align="center" prop="serialNumber" width="200" />
      <el-table-column
        label="到账日期"
        align="center"
        prop="dzDate"
        width="120"
        :formatter="dateFormatter2"
      />
      <el-table-column label="税率" align="center" prop="taxRate">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TAX_RATE" :value="scope.row.taxRate" />
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center" prop="contractCode" width="150" />
      <el-table-column label="开票内容" align="center" prop="content" width="180" />
      <el-table-column
        label="合同总金额"
        align="center"
        prop="totalAmount"
        :formatter="erpPriceTableColumnFormatterWithComma"
        width="120"
      />
      <el-table-column label="完工验收方式" align="center" prop="acceptanceType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ACCPETANCE_TYPE" :value="scope.row.acceptanceType" />
        </template>
      </el-table-column>
      <el-table-column label="完工单是否发出" align="center" prop="issued" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.IS_ORDER_ISSUED" :value="scope.row.issued" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="约定付款天数" align="center" prop="daysToPay" /> -->
      <el-table-column
        label="合同约定开票次数/比例"
        align="center"
        prop="billingFrequencyOrRatio"
        width="300"
      />
      <!-- <el-table-column label="已开票金额" align="center" prop="invoicedAmount" />
      <el-table-column label="已到账金额" align="center" prop="receivedAmount" /> -->
      <el-table-column
        label="本次开票金额（元）"
        align="center"
        prop="invoiceAmount"
        :formatter="erpPriceTableColumnFormatterWithComma"
        width="120"
      />
      <el-table-column label="本次开票依据" align="center" prop="invoiceBasis" width="250" />
      <el-table-column label="纳税人编号" align="center" prop="taxpayerNumber" width="180" />
      <!--<el-table-column label="地址" align="center" prop="address" width="150" />
      <el-table-column label="电话" align="center" prop="mobile" width="220" />
      <el-table-column label="开户行名称" align="center" prop="bankName" width="150" />
      <el-table-column label="开户行账号" align="center" prop="bankAccount" width="150" />
      <el-table-column label="开票银行" align="center" prop="billingBank" width="130" /> -->
      <el-table-column
        label="合同原件是否提交项目管理人"
        align="center"
        prop="submitFlag"
        width="130"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SUBMIT_FLAG" :value="scope.row.submitFlag" />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="280" fixed="right">
        <template #default="scope">
          <template v-if="scope.row.result === 2">
            <el-button link type="warning" @click="addReceiptRecord(scope.row)">增加收款</el-button>
            <el-button link type="primary" @click="supplementaryAttachment(scope.row)">
              附件
            </el-button>
          </template>
          <el-button
            link
            type="warning"
            @click="reissue(scope.row)"
            v-if="scope.row.result === 3 || scope.row.result === 4"
          >
            重新发起
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>
          <el-button link type="primary" @click="handleProcessDetail(scope.row)"> 进度 </el-button>
          <el-button
            link
            type="danger"
            @click="cancelLeave(scope.row)"
            v-if="scope.row.result === 1"
          >
            取消
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDownload(scope.row)"
            v-if="scope.row.result === 2 && scope.row.annex"
          >
            <el-icon size="20">
              <Download />
            </el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 追加附件弹窗 -->
    <SupplementaryDialog ref="supplementaryDialogRef" @fetch-data="getList" />

    <!-- 增加收款弹窗 -->
    <AddReceiptDialog ref="addReceiptRef" @success="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { erpPriceTableColumnFormatterWithComma } from '@/utils'
import AddReceiptDialog from '../receiptRecord/ReceiptRecordForm.vue'
import SupplementaryDialog from './components/supplementaryDialog.vue'
import { ElMessageBox } from 'element-plus'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { InvoiceApplyApi } from '@/api/sales/invoice'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import download from '@/utils/download'

defineOptions({ name: 'Invoice' })

const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const router = useRouter() // 路由
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 10,
  content: undefined,
  invoiceType: undefined,
  contractCode: undefined,
  applyDate: [],
  acceptanceType: undefined,
  issued: undefined,
  serialNumber: undefined
})
// const totalAmount = ref(0) // 合同总金额
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InvoiceApplyApi.getInvoiceApplyPage(queryParams.value)
    // const statisticsData = await InvoiceApplyApi.contractReceiptStatistics(queryParams)

    // totalAmount.value = statisticsData.totalAmount
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InvoiceApplyApi.exportInvoiceApply(queryParams.value)
    download.excel(data, '合同收款信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 添加操作 */
const handleCreate = () => {
  router.push({ name: 'InvoiceCreate' })
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'InvoiceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消请假操作 */
const cancelLeave = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstance(row.processInstanceId, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 审批进度 */
const handleProcessDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstanceId,
      backPath: route.path
    }
  })
}

const handleDownload = (row) => {
  window.open(row.annex)
}

const reissue = (row) => {
  router.push({
    name: 'InvoiceCreate',
    query: {
      id: row.id
    }
  })
}

// 追加附件
const supplementaryDialogRef = ref()
const supplementaryAttachment = (row) => {
  supplementaryDialogRef.value.openDialog(row)
}

// 增加收款
const addReceiptRef = ref()
const addReceiptRecord = (row) => {
  addReceiptRef.value.open('create', null, row)
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  if (route.query.contractCode) {
    queryParams.value.contractCode = route.query.contractCode
  }
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-statistic) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
:deep(.el-statistic__number) {
  font-weight: bold;
  color: #38bdf8;
}
:deep(.el-statistic__head) {
  font-size: 14px;
}

.red-text {
  :deep(.el-statistic__number) {
    color: rgb(220 38 38) !important;
  }
}
</style>
