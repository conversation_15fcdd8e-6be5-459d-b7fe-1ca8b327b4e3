<!--
 * @Description: 收款记录新增/修改弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-13 10:31:39
 * @LastEditTime: 2024-07-10 13:53:36
-->

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1280px" class="business-form-dialog">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        v-loading="formLoading"
        class="business-form"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Document /></el-icon>
            </div>
            <h2 class="section-title">基本信息</h2>
            <div class="section-line"></div>
          </div>
          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="合同名称" prop="contractName" class="custom-form-item">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.contractName"
                    disabled
                    placeholder="合同名称"
                    class="custom-input"
                    size="large"
                  />
                  <el-button
                    v-if="!fromInvoiceTable"
                    type="primary"
                    @click="chooseContract"
                    class="customer-button"
                    size="large"
                  >
                    <el-icon><User /></el-icon>
                    选择合同
                  </el-button>
                </div>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="客户名称" prop="customerName" class="custom-form-item">
                <el-input
                  v-model="formData.customerName"
                  placeholder="客户名称"
                  disabled
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="收款日期" prop="receiveDate" class="custom-form-item">
                <el-date-picker
                  v-model="formData.receiveDate"
                  type="date"
                  value-format="x"
                  placeholder="选择收款日期"
                  class="custom-date-picker"
                  size="large"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="收款金额" prop="receiveAmount" class="custom-form-item">
                <el-input
                  @input="(v) => (formData.receiveAmount = v.replace(/[^\d.]/g, ''))"
                  v-model="formData.receiveAmount"
                  placeholder="请输入收款金额"
                  class="custom-input"
                  size="large"
                />
              </el-form-item>
            </div>
          </div>
        </div>
        <!-- 附件与备注区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Folder /></el-icon>
            </div>
            <h2 class="section-title">附件与备注</h2>
            <div class="section-line"></div>
          </div>
          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="附件" prop="attachment" class="custom-form-item">
                <UploadFile
                  v-model="formData.attachment"
                  :file-size="5120"
                  :limit="1"
                  @uploading="handleUploading"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper full-width">
              <el-form-item label="备注" prop="remark" class="custom-form-item">
                <el-input
                  type="textarea"
                  :rows="3"
                  v-model="formData.remark"
                  placeholder="请输入备注"
                  class="custom-textarea"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="submitForm"
          type="primary"
          :disabled="formLoading || isUploading"
          class="submit-button"
          size="large"
        >
          <el-icon><Check /></el-icon>
          确认提交
        </el-button>
        <el-button @click="dialogVisible = false" class="cancel-button" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </div>
    </template>
    <ContractDialog ref="contractDialogRef" @fetch-data="chooseContractDone" />
  </Dialog>
</template>
<script setup lang="ts">
import { ContractReceiveAmountApi, ContractReceiveAmountVO } from '@/api/sales/receiptRecord'

/** 收款记录信息 表单 */
defineOptions({ name: 'ReceiptRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  contractId: undefined,
  contractName: undefined,
  receiveDate: undefined,
  receiveAmount: undefined,
  attachment: undefined,
  remark: undefined,
  customerName: undefined,
  customerId: undefined
})
const formRules = reactive({
  contractName: [{ required: true, message: '合同不能为空', trigger: 'blur' }],
  receiveAmount: [{ required: true, message: '收款金额不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const fromInvoiceTable = ref(false)
/** 打开弹窗 */
const open = async (type: string, id?: number, row?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ContractReceiveAmountApi.getContractReceiveAmount(id)
    } finally {
      formLoading.value = false
    }
  }
  // 从开票申请列表页面直接增加收款
  if (row) {
    fromInvoiceTable.value = true
    formData.value.contractId = row.contractId
    formData.value.contractName = row.contractName
    formData.value.customerName = row.customerName
    formData.value.customerId = row.customerId
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ContractReceiveAmountVO
    if (formType.value === 'create') {
      await ContractReceiveAmountApi.createContractReceiveAmount(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractReceiveAmountApi.updateContractReceiveAmount(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    contractId: undefined,
    contractName: undefined,
    receiveDate: undefined,
    receiveAmount: undefined,
    attachment: undefined,
    remark: undefined,
    customerName: undefined,
    customerId: undefined
  }
  formRef.value?.resetFields()
}

const contractDialogRef = ref()

const chooseContract = () => {
  contractDialogRef.value.openDialog()
}
const chooseContractDone = (item) => {
  formData.value.contractId = item.id
  formData.value.contractName = item.contractName
  formData.value.customerName = item.customerName
  formData.value.customerId = item.customerId
}

const isUploading = ref(false) // 上传状态，初始为 false
// 上传状态变化时的处理
const handleUploading = (uploading: boolean) => {
  isUploading.value = uploading
}
</script>

<style lang="scss" scoped>
// 以下内容与 BusinessForm.vue 完全一致
.business-form-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 20px;
          &:hover {
            color: #f0f0f0;
          }
        }
      }
    }
    .el-dialog__body {
      padding: 0;
      background: #f8fafc;
    }
    .el-dialog__footer {
      background: white;
      padding: 24px 32px;
      border-top: 1px solid #e5e7eb;
    }
  }
}
.form-container {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    &:hover {
      background: #a8a8a8;
    }
  }
}
.business-form {
  .form-section {
    background: white;
    border-radius: 0;
    padding: 24px 32px;
    margin-bottom: 0;
    box-shadow: none;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: none;
      transform: none;
    }
    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
  }
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;
    .section-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      .el-icon {
        font-size: 20px;
        color: white;
      }
    }
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
      flex: 1;
    }
    .section-line {
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      border-radius: 1px;
      flex: 1;
      margin-left: 16px;
    }
  }
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    .form-item-wrapper {
      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }
  .custom-form-item {
    margin-bottom: 0;
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
      line-height: 1.5;
    }
    :deep(.el-form-item__content) {
      line-height: 1;
    }
    :deep(.el-form-item__error) {
      font-size: 12px;
      color: #ef4444;
      margin-top: 4px;
    }
  }
  .custom-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-select {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-date-picker {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &.is-focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
    :deep(.el-input__inner) {
      color: #1f2937;
      font-size: 14px;
    }
  }
  .custom-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 2px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.3s ease;
      box-shadow: none;
      font-size: 14px;
      color: #1f2937;
      resize: vertical;
      &:hover {
        border-color: #d1d5db;
        background: white;
      }
      &:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      &::placeholder {
        color: #9ca3af;
      }
    }
  }
  .custom-radio-group {
    :deep(.el-radio) {
      margin-right: 20px;
      .el-radio__label {
        color: #374151;
        font-size: 14px;
      }
      .el-radio__input.is-checked .el-radio__inner {
        border-color: #667eea;
        background: #667eea;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #667eea;
      }
    }
  }
  .input-with-button {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    .custom-input {
      flex: 1;
    }
    .customer-button {
      white-space: nowrap;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
  .product-content {
    padding: 0;
    :deep(.el-tabs) {
      .el-tabs__header {
        margin-bottom: 20px;
        .el-tabs__nav-wrap {
          &::after {
            display: none;
          }
        }
        .el-tabs__item {
          color: #6b7280;
          font-weight: 500;
          &.is-active {
            color: #667eea;
          }
        }
        .el-tabs__active-bar {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
@media (max-width: 768px) {
  .business-form-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
  .business-form {
    .form-section {
      padding: 20px;
    }
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    .input-with-button {
      flex-direction: column;
      align-items: stretch;
    }
  }
  .dialog-footer {
    flex-direction: column;
    .submit-button,
    .cancel-button {
      width: 100%;
    }
  }
}
</style>
