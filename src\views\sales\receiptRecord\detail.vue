<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-04 13:59:40
 * @LastEditTime: 2025-07-10 11:44:13
-->
<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="合同名称">
        {{ detailData.contractName }}
      </el-descriptions-item>
      <el-descriptions-item label="客户名称">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="收款日期">
        {{ formatDate(detailData.receiveDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="收款金额">
        {{ erpPriceInputFormatterWithComma(detailData.receiveAmount) }}
      </el-descriptions-item>
      <el-descriptions-item label="附件">
        <FileListPreview :fileUrl="detailData.attachment" />
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { erpPriceInputFormatterWithComma } from '@/utils'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { ContractReceiveAmountApi, ContractReceiveAmountVO } from '@/api/sales/receiptRecord'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ReceiptRecordDetail' })

const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute, back } = useRouter()
const { query } = useRoute() // 查询参数

const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await ContractReceiveAmountApi.getContractReceiveAmount(props.id || queryId)
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  back()
}
/** 初始化 **/
onMounted(() => {
  getInfo()
})
defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
