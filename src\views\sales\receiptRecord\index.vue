<!--
 * @Description: 收款记录
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-03 14:54:39
 * @LastEditTime: 2025-07-10 11:43:53
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="销售人员" prop="signedId">
        <el-select
          class="!w-240px"
          v-model="queryParams.signedId"
          filterable
          placeholder="请选择销售人员"
          value-key="id"
          lable-key="nickname"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="收款日期" prop="receiveDate">
        <el-date-picker
          v-model="queryParams.receiveDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>

      <el-row :gutter="10" class="mb5">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="openForm('create')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button type="success" plain @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="合同名称" align="center" prop="contractName" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="销售人员" align="center" prop="signedId">
        <template #default="scope">
          <span>{{ userList.find((item) => item.id === scope.row.signedId)?.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="收款日期"
        align="center"
        prop="receiveDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="收款金额" align="center" prop="receiveAmount" :formatter="erpPriceTableColumnFormatterWithComma" width="120"/>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)"> 详情 </el-button>

          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ReceiptRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { erpPriceTableColumnFormatterWithComma } from '@/utils'
import * as UserApi from '@/api/system/user'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { ContractReceiveAmountApi } from '@/api/sales/receiptRecord'
import ReceiptRecordForm from './ReceiptRecordForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 客户信息 列表 */
defineOptions({ name: 'ReceiptRecord' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractId: undefined,
  contractName: undefined,
  receiveDate: [],
  signedId: undefined,
  receiveAmount: undefined,
  attachment: undefined,
  customerName: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractReceiveAmountApi.getContractReceiveAmountPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 详情操作 */
const handleDetail = (row) => {
  router.push({
    name: 'ReceiptRecordDetail',
    query: {
      id: row.id
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractReceiveAmountApi.deleteContractReceiveAmount(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ContractReceiveAmountApi.exportContractReceiveAmount(queryParams)
    download.excel(data, '收款记录信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
/** 初始化 **/
onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()

  getList()
})
</script>
