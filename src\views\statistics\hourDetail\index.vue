<!--
 * @Description: 工时明细报表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-03 14:07:21
 * @LastEditTime: 2024-08-12 13:27:20
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="员工" prop="userId">
        <el-select
          class="!w-240px"
          filterable
          v-model="queryParams.userId"
          placeholder="请选择员工"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入项目编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 分组统计区 -->
    <div class="group-summary" v-if="list.length">
      <div class="summary-title">员工工时统计</div>
      <div class="summary-list">
        <div class="summary-item" v-for="item in employeeSummary" :key="item.userId">
          <div class="avatar" v-if="item.avatar">
            <img :src="item.avatar" alt="avatar" />
          </div>
          <div class="avatar" v-else>
            {{ item.userName?.charAt(0) || '?' }}
          </div>
          <div class="name">{{ item.userName }}</div>
          <div class="total">{{ item.totalHours }} H</div>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="日期" align="center" prop="taskDate" width="220" />
      <el-table-column label="员工" align="center" prop="userName" width="160">
        <template #default="scope">
          <div class="employee-cell">
            <div class="avatar" v-if="getUserAvatar(scope.row.userId)">
              <img :src="getUserAvatar(scope.row.userId)" alt="avatar" />
            </div>
            <div class="avatar" v-else>
              {{ scope.row.userName?.charAt(0) || '?' }}
            </div>
            <span class="name" @click="filterBy('userId', scope.row.userId)">{{ scope.row.userName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" width="140">
        <template #default="scope">
          <span class="customer-cell" @click="filterBy('customerName', scope.row.customerName)">{{ scope.row.customerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目编码" align="center" prop="contractCode" width="150" />
      <el-table-column label="项目名称" align="center" prop="projectName" width="200">
        <template #default="scope">
          <span class="project-cell" @click="filterBy('projectName', scope.row.projectName)">{{ scope.row.projectName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="项目部门" align="center" prop="projectDeptName" width="130" /> -->
      <el-table-column label="项目经理" align="center" prop="projectManagerName" width="110" />
      <el-table-column label="任务" align="center" prop="describeVal" width="200" />
      <el-table-column label="实际工时" align="center" prop="actualWorkHours" width="140">
        <template #default="scope">
          <div class="workhour-cell">
            <span>{{ scope.row.actualWorkHours || 0 }} H</span>
            <el-progress :percentage="getWorkHourPercent(scope.row.actualWorkHours)" :stroke-width="10" :show-text="false" color="#10b981" style="width:60px;margin-left:8px;" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="日志详情" align="center" prop="logDetails" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import download from '@/utils/download'
import { dateFormatter2 } from '@/utils/formatTime'
import { getHourDetailPage, exportHourDetail } from '@/api/statistics/hourDetail'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'HourDetail' })

const dateRange = ref<any>([])
const route = useRoute()
const router = useRouter()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 10,
  projectName: undefined,
  queryTimeFrom: undefined,
  queryTimeTo: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const userList = ref<any[]>([]) // 用户列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getHourDetailPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    projectName: undefined,
    queryTimeFrom: undefined,
    queryTimeTo: undefined
  }
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  if (dateRange.value.length === 0) {
    return message.warning('请先选择日期！')
  }
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportHourDetail(queryParams.value)
    download.excel(data, `工时明细报表-${dayjs().format('YYYY-MM-DD')}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const selectDate = () => {
  if (dateRange.value != null) {
    queryParams.value.queryTimeFrom = dateRange.value[0]
    queryParams.value.queryTimeTo = dateRange.value[1]
  } else {
    queryParams.value.queryTimeFrom = ''
    queryParams.value.queryTimeTo = ''
  }
}

/** 初始化 **/
onMounted(async () => {
  if (route.query.projectName) {
    queryParams.value.projectName = route.query.projectName
  }
  getList()
  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
})

// 头像获取
const getUserAvatar = (userId: any) => {
  const user = userList.value.find(u => u.id === userId)
  return user?.avatar || ''
}
// 点击筛选
const filterBy = (key: string, value: any) => {
  queryParams.value[key] = value
  handleQuery()
}
// 工时进度条百分比
const getWorkHourPercent = (val: number) => {
  const max = Math.max(...list.value.map(i => i.actualWorkHours || 0), 1)
  return Math.round(((val || 0) / max) * 100)
}
// 分组统计
const employeeSummary = computed(() => {
  const map = new Map()
  list.value.forEach(row => {
    if (!row.userId) return
    if (!map.has(row.userId)) {
      const user = userList.value.find(u => u.id === row.userId)
      map.set(row.userId, {
        userId: row.userId,
        userName: row.userName,
        avatar: user?.avatar || '',
        totalHours: 0
      })
    }
    map.get(row.userId).totalHours += Number(row.actualWorkHours || 0)
  })
  return Array.from(map.values())
})
</script>

<style scoped lang="scss">
.hour-detail-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 24px;
}
.hour-detail-search-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 32px 32px 8px 32px;
  margin-bottom: 16px;
  .el-form-item {
    margin-bottom: 0;
    .el-form-item__label {
      font-weight: 600;
      color: #334155;
      font-size: 15px;
      min-width: 80px;
      white-space: nowrap;
    }
  }
  .el-input, .el-select, .el-date-picker {
    width: 100% !important;
    border-radius: 10px !important;
    font-size: 15px;
    .el-input__wrapper, .el-select__wrapper, .el-date-editor {
      border-radius: 10px !important;
      border: 1px solid #d1d5db !important;
      box-shadow: 0 1px 4px rgba(102,126,234,0.08);
      transition: all 0.3s;
      background: #f8fafc;
      &:hover, &.is-focus {
        border-color: #667eea !important;
        box-shadow: 0 2px 8px rgba(102,126,234,0.15);
        background: #fff;
      }
    }
    .el-input__inner, .el-select__selected-value, .el-date-editor input {
      font-size: 15px;
      color: #334155;
      &::placeholder {
        color: #b6bdd3;
        font-weight: 400;
      }
    }
  }
}
.el-table {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  overflow: hidden;
  margin-bottom: 16px;
  :deep(.el-table__header) th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e2e8f0;
  }
  :deep(.el-table__row) {
    transition: background 0.2s;
    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    td {
      border-bottom: 1px solid #f1f5f9;
      padding: 16px 8px;
      font-size: 14px;
      color: #334155;
    }
    .employee-cell {
      display: flex;
      align-items: center;
      gap: 8px;
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 16px;
      }
      .name {
        font-weight: 600;
        color: #3b82f6;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .project-cell, .customer-cell {
      font-weight: 600;
      color: #764ba2;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    .workhour-cell {
      font-weight: 700;
      color: #10b981;
      background: #e0f7ef;
      border-radius: 8px;
      padding: 2px 10px;
      display: inline-block;
    }
  }
}
.Pagination {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  padding: 16px 0;
  margin-top: 0;
}
.group-summary {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 20px 32px 8px 32px;
  margin-bottom: 16px;
  .summary-title {
    font-size: 17px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 10px;
  }
  .summary-list {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    .summary-item {
      display: flex;
      align-items: center;
      gap: 10px;
      background: #f8fafc;
      border-radius: 10px;
      padding: 8px 18px;
      min-width: 180px;
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 16px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }
      }
      .name {
        font-weight: 600;
        color: #3b82f6;
      }
      .total {
        font-weight: 700;
        color: #10b981;
        font-size: 15px;
        margin-left: 8px;
      }
    }
  }
}
@media (max-width: 900px) {
  .hour-detail-page {
    padding: 8px;
  }
  .hour-detail-search-form {
    padding: 12px 8px 8px 8px;
    .el-col {
      margin-bottom: 12px;
      flex: 0 0 100%;
      max-width: 100%;
    }
    .el-form-item__label {
      font-size: 14px;
    }
    .el-input, .el-select, .el-date-picker {
      font-size: 14px;
    }
  }
  .el-table {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(102,126,234,0.06);
  }
  .Pagination {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(102,126,234,0.04);
    padding: 8px 0;
  }
}
</style>
