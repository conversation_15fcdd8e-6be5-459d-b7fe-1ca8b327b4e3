<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="负责人" prop="responsibleName">
        <el-input
          v-model="queryParams.responsibleName"
          placeholder="请输入负责人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="参与人" prop="joinId">
        <el-select
          v-model="queryParams.joinId"
          clearable
          filterable
          placeholder="请选择参与人"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="优先级" prop="taskPriority">
        <el-select
          v-model="queryParams.taskPriority"
          placeholder="请选择优先级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TASK_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="customerName">
        <el-select
          v-model="queryParams.customerName"
          placeholder="请选择状态"
          clearable
          multiple
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      height="650"
      ref="tableRef"
      v-loading="loading"
      :span-method="objectSpanMethod"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="任务阶段" align="center" prop="taskPhases" width="150" />
      <el-table-column label="任务名称" align="center" prop="describeVal" />
      <el-table-column label="任务优先级" align="center" prop="taskPriority" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TASK_PRIORITY" :value="scope.row.taskPriority" />
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="customerName" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TASK_STATUS" :value="scope.row.customerName" />
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="responsibleName" width="120" />
      <el-table-column label="项目总工时" align="center" prop="taskWorkHours" width="100">
        <template #default="{ row }">
          {{ row.taskWorkHours ? row.taskWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="已消耗工时" align="center" prop="consumedWorkHours" width="100">
        <template #default="{ row }">
          {{ row.consumedWorkHours ? row.consumedWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="剩余工时" align="center" prop="remainWorkHours" width="100">
        <template #default="{ row }">
          {{ row.remainWorkHours ? row.remainWorkHours + 'H' : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="计划开工日期"
        align="center"
        prop="plannedStartDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="计划完工日期"
        align="center"
        prop="plannedEndDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="实际开工日期"
        align="center"
        prop="actualStartDate"
        :formatter="dateFormatter2"
        width="120"
      />
      <el-table-column
        label="实际完工日期"
        align="center"
        prop="actualEndDate"
        :formatter="dateFormatter2"
        width="120"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { getDictLabel, DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { TaskApi, TaskVO } from '@/api/project/projectTask'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
import * as UserApi from '@/api/system/user'

const { query } = useRoute() // 查询参数
const projectId = query.projectId as unknown as number // 从 URL 传递过来的 id 编号

/** 任务信息 列表 */
defineOptions({ name: 'PersonTask' })
const userStore = useUserStore()

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const tableRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<TaskVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectId,
  customerName: undefined,
  describeVal: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  taskType: undefined,
  plannedDuration: undefined,
  plannedStartDate: [],
  plannedEndDate: [],
  actualStartDate: [],
  actualEndDate: [],
  completeBase: undefined,
  // completeBase: [],
  specifyBasis: undefined,
  // specifyBasis: [],
  completionConditions: undefined,
  remark: undefined,
  createTime: [],
  taskPriority: undefined,
  joinId: undefined
})
const queryFormRef = ref() // 搜索的表单
const router = useRouter() // 路由
const route = useRoute()
const { delView } = useTagsViewStore() // 视图操作
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams,
      customerName: queryParams.customerName ? queryParams.customerName.join(',') : undefined
    }
    const data = await TaskApi.getTaskPage(queryData)
    list.value = data.list
    total.value = data.total
    rowspan()
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

let spanArr = ref()
let position = ref()
const rowspan = () => {
  //每次调用清空数据
  spanArr.value = []
  position.value = 0
  list.value.forEach((item, index) => {
    if (index === 0) {
      spanArr.value.push(1)
      position.value = 0
    } else {
      if (list.value[index].taskPhases === list.value[index - 1].taskPhases) {
        spanArr.value[position.value] += 1
        spanArr.value.push(0)
      } else {
        spanArr.value.push(1)
        position.value = index
      }
    }
  })
}

const objectSpanMethod: any = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    if (spanArr.value && spanArr.value.length > 0) {
      const _row = spanArr.value[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }
}

/** 初始化 **/
onMounted(async () => {
  userList.value = await UserApi.getSimpleUserList()
  await getList()
})
</script>
