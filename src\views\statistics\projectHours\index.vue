<!--
 * @Description: 项目/工时统计报表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-30 16:42:14
 * @LastEditTime: 2024-07-01 17:18:46
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 + 视图切换按钮 -->
    <div class="ph-search-bar">
      <el-form
        class="ph-search-form"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="客户" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="合同编号" prop="contractCode">
          <el-input
            v-model="queryParams.contractCode"
            placeholder="请输入合同编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model="queryParams.projectName"
            placeholder="请输入项目名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
        <el-row :gutter="10" class="mb5">
          <el-form-item class="ml-5px">
            <el-button type="success" plain @click="handleExport" :loading="exportLoading">
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <div class="ph-view-switch">
        <el-button :type="viewType==='card'?'primary':'default'" @click="viewType='card'" class="ph-switch-btn" plain>
          <Icon icon="ep:grid" class="mr-5px" /> 田卡片
        </el-button>
        <el-button :type="viewType==='list'?'primary':'default'" @click="viewType='list'" class="ph-switch-btn" plain>
          <Icon icon="ep:list" class="mr-5px" /> 三列表
        </el-button>
      </div>
    </div>
    <!-- 卡片视图 -->
    <div v-if="viewType==='card'" class="ph-card-list">
      <div v-for="item in list as any[]" :key="item.contractCode" class="ph-card" @click="goToProject(item)">
        <div class="ph-card-bg"></div>
        <div class="ph-card-content">
          <div class="ph-card-title">
            <span class="ph-card-link" @click.stop="goToProject(item)">{{ item.projectName }}</span>
          </div>
          <div class="ph-card-row">
            <span class="ph-card-label">客户：</span>
            <span class="ph-card-link" @click.stop="goToCustomer(item)">{{ item.customerName }}</span>
          </div>
          <div class="ph-card-row">
            <span class="ph-card-label">合同编号：</span>
            <span class="ph-card-link" @click.stop="goToContract(item)">{{ item.contractCode }}</span>
          </div>
          <div class="ph-card-row">
            <span class="ph-card-label">项目经理：</span>
            <span class="ph-card-value">{{ item.projectManagerName }}</span>
          </div>
          <div class="ph-card-row">
            <span class="ph-card-label">实际工时：</span>
            <span class="ph-card-value ph-card-link" @click.stop="pushToDetail(item)">{{ item.actualWorkHours || 0 }} H</span>
          </div>
        </div>
        <div class="ph-card-footer">
          <span class="ph-card-footer-label">预估工时：</span>
          <span class="ph-card-footer-value">{{ item.estimateCost || 0 }} H</span>
          <span class="ph-card-footer-label" style="margin-left:16px;">剩余工时：</span>
          <span class="ph-card-footer-value">{{ item.remainWorkHours || 0 }} H</span>
        </div>
      </div>
      <el-empty v-if="!list.length" description="暂无数据" />
    </div>
    <!-- 列表视图 -->
    <el-table
      v-else
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @sort-change="sortChange"
    >
      <el-table-column
        label="合同编号"
        align="center"
        prop="contractCode"
        width="150"
        sortable="custom"
      >
        <template #default="scope">
          <div @click="goToContract(scope.row)" style="cursor: pointer; color: #606bfb">{{
            scope.row.contractCode
          }}</div>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" width="180">
        <template #default="scope">
          <div @click="goToCustomer(scope.row)" style="cursor: pointer; color: #606bfb">{{
            scope.row.customerName
          }}</div>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="180">
        <template #default="scope">
          <div @click="goToProject(scope.row)" style="cursor: pointer; color: #606bfb">{{
            scope.row.projectName
          }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="项目状态" align="center" prop="result" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column> -->
      <el-table-column label="项目经理" align="center" prop="projectManagerName" width="120" />
      <el-table-column label="项目总监" align="center" prop="projectOwner" width="120" />
      <el-table-column label="咨询小组" align="center" prop="counselor" min-width="250" />
      <el-table-column
        label="开工日期"
        align="center"
        prop="plannedStartDate"
        :formatter="dateFormatter2"
        width="150"
      />
      <el-table-column label="预估工时" align="center" prop="estimateCost" width="100">
        <template #default="scope"> {{ scope.row.estimateCost || 0 }} H </template>
      </el-table-column>
      <el-table-column label="实际工时" align="center" prop="actualWorkHours" width="100">
        <template #default="scope">
          <div class="listLink" @click="pushToDetail(scope.row)">
            {{ scope.row.actualWorkHours || 0 }} H
          </div>
        </template>
      </el-table-column>
      <el-table-column label="剩余工时" align="center" prop="remainWorkHours" width="100">
        <template #default="scope">{{ scope.row.remainWorkHours || 0 }} H </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="pagnationChange"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
const route = useRoute()
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { getProjectHourPage, exportProjectHour } from '@/api/statistics/projectHours'

defineOptions({ name: 'ProjectHours' })

const router = useRouter()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  contractName: undefined,
  deptId: undefined,
  customerId: undefined,
  customerName: route.query.customerName
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const viewType = ref<'card'|'list'>('card')

const goToContract = (row) => {
  router.push({
    path: '/crm/contract',
    query: {
      contractCode: row.contractCode
    }
  })
}
const goToProject = (row) => {
  router.push({
    path: '/project/projectPlan',
    query: {
      projectName: row.projectName
    }
  })
}

const goToCustomer = (row) => {
  router.push({
    name: 'CrmCustomerDetail',
    params: {
      id: row.customerId
    }
  })
}

/** 查询列表 */
const getList = async (order?) => {
  loading.value = true
  try {
    const data = await getProjectHourPage({ ...queryParams, sortOrder: order })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportProjectHour(queryParams)
    download.excel(data, `项目工时报表-${dayjs().format('YYYY-MM-DD')}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const pushToDetail = (row) => {
  router.push({
    path: '/statistics/hourDetail',
    query: {
      projectName: row.projectName
    }
  })
  // router.push({
  //   path: '/user/taskDetail',
  //   query: {
  //     type: 'project',
  //     projectId: row.id,
  //     backPath: '/statistics/projectHours'
  //   }
  // })
}

const sortChange = (column: any) => {
  const realOrder =
    column.order === 'ascending' ? 'asc' : column.order === 'descending' ? 'desc' : null
  getList(realOrder)
}

const pagnationChange = () => {
  getList()
}

/** 初始化 **/
onMounted(async () => {
  getList()
})
</script>

<style scoped lang="scss">
.ph-search-bar {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;
  flex-wrap: wrap;
  .ph-search-form {
    flex: 1;
    min-width: 0;
  }
  .ph-view-switch {
    display: flex;
    gap: 8px;
    margin-top: 4px;
    .ph-switch-btn {
      min-width: 90px;
      font-weight: 600;
      border-radius: 8px;
      font-size: 15px;
      background: #f3f4f6;
      color: #667eea;
      border: none;
      &:hover, &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
      }
    }
    .el-button.is-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      color: #fff !important;
    }
  }
}
.ph-card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin: 24px 0 0 0;
}
.ph-card {
  position: relative;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 16px rgba(102,126,234,0.10);
  overflow: hidden;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &:hover {
    box-shadow: 0 8px 32px rgba(102,126,234,0.16);
    transform: translateY(-2px) scale(1.02);
  }
  .ph-card-bg {
    position: absolute;
    left: 0; top: 0; right: 0; bottom: 0;
    background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
    opacity: 0.7;
    z-index: 0;
  }
  .ph-card-content {
    position: relative;
    z-index: 1;
    padding: 24px 20px 12px 20px;
    .ph-card-title {
      font-size: 20px;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 10px;
      letter-spacing: 0.5px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .ph-card-row {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 15px;
      margin-bottom: 4px;
      .ph-card-label {
        color: #64748b;
        font-weight: 500;
      }
      .ph-card-value {
        color: #334155;
        font-weight: 600;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        &.ph-card-link {
          color: #606bfb;
          text-decoration: underline;
          cursor: pointer;
          transition: color 0.15s;
          &:hover {
            color: #3b47c4;
            text-decoration: underline;
          }
        }
      }
    }
  }
  .ph-card-footer {
    position: relative;
    z-index: 1;
    padding: 0 20px 16px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    .ph-card-footer-label {
      color: #64748b;
      font-size: 14px;
    }
    .ph-card-footer-value {
      color: #667eea;
      font-size: 15px;
      font-weight: 600;
    }
  }
}
@media (max-width: 900px) {
  .ph-card-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  .ph-card {
    min-height: 120px;
    .ph-card-content {
      padding: 12px 10px 8px 10px;
      .ph-card-title {
        font-size: 16px;
      }
      .ph-card-row {
        font-size: 13px;
      }
    }
    .ph-card-footer {
      padding: 0 10px 8px 10px;
      .ph-card-footer-label, .ph-card-footer-value {
        font-size: 13px;
      }
    }
  }
}
</style>
