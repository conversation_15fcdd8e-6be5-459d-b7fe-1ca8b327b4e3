<!--
 * @Description: 添加/编辑计划弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-09 17:09:20
 * @LastEditTime: 2024-08-14 15:44:49
-->

<template>
  <Dialog
    :title="flag == 'add' ? '添加计划' : '编辑计划'"
    v-model="dialogVisible"
    center
    width="1080"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="日期">
            {{ propDate }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="所属项目" prop="projectName">
            <div class="flex w-100%">
              <el-input
                class="mr-50px"
                v-model="formData.projectName"
                disabled
                placeholder="请选择项目"
              ></el-input>
              <el-button type="primary" @click="openProjectDialog">选择项目</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="客户名称" prop="customerName">
            <div>{{ formData.customerName }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="22">
          <el-form-item label="所属任务" prop="taskId">
            <el-select
              ref="taskRef"
              filterable
              v-model="formData.taskId"
              placeholder="请选择所属任务"
              style="width: 380px"
            >
              <el-option
                v-for="item in taskList"
                :key="item.id"
                :label="item.describeVal"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="负责人" prop="userId">
        {{ user.nickname }}
      </el-form-item>
      <el-form-item label="计划工作内容" prop="planContent">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.planContent"
          placeholder="请输入计划工作内容"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.remark"
          placeholder="请输入备注(现场第几次咨询?)"
        />
      </el-form-item>
    </el-form>
    <div v-if="flag === 'edit'">
      <div class="text-20px font-bold mb-10px">变更记录</div>
      <el-table :data="formData.taskChangeLogList">
        <el-table-column type="index" label="序号" width="70"></el-table-column>
        <el-table-column prop="changeType" label="变更类型"></el-table-column>
        <el-table-column prop="oldValue" label="变更前"></el-table-column>
        <el-table-column prop="newValue" label="变更后"></el-table-column>
        <el-table-column
          prop="changeTime"
          label="变更时间"
          :formatter="dateFormatter"
          width="180"
        ></el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
    <projectDialog ref="projectDialogRef" @fetch-data="chooseProjectDone" />
  </Dialog>
</template>
<script setup lang="ts">
import { TaskApi } from '@/api/project/projectTask'
import { useUserStore } from '@/store/modules/user'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import { dateFormatter } from '@/utils/formatTime'

dayjs.extend(advancedFormat)
/** 任务信息 表单 */
defineOptions({ name: 'AddPlanDialog' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const user = useUserStore().getUser
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  projectId: undefined,
  customerName: undefined,
  describe: undefined,
  userId: undefined,
  userName: undefined,
  taskType: undefined,
  plannedDuration: undefined,
  actualStartDate: undefined,
  actualEndDate: undefined,
  completeBase: undefined,
  specifyBasis: undefined,
  planContent: undefined,
  remark: undefined,
  mustBeCompletedDate: undefined,
  taskId: undefined,
  consumeDate: undefined
})
const formRules = reactive({
  planContent: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const taskRef = ref()
const propDate = ref('')
const flag = ref('add')
/** 打开弹窗 */
const open = async (type, date, id?) => {
  resetForm()
  flag.value = type
  formData.value.consumeDate = +dayjs(date).format('x')
  propDate.value = date
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TaskApi.getTaskPlan(id)
      await getTaskList()
    } finally {
      formLoading.value = false
    }
  }
  dialogVisible.value = true
}

const projectDialogRef = ref()
const openProjectDialog = () => {
  projectDialogRef.value.openDialog()
}

const chooseProjectDone = (item) => {
  formData.value.taskId = undefined
  formData.value.mustBeCompletedDate = undefined
  formData.value.projectId = item.id
  formData.value.projectName = item.projectName
  formData.value.customerName = item.customerName
  getTaskList()
}

const taskList = ref<any>([])
const getTaskList = async () => {
  formLoading.value = true
  try {
    const data = await TaskApi.getTaskPage({
      pageNo: 1,
      pageSize: 999,
      projectId: formData.value.projectId
    })
    taskList.value = data.list
  } finally {
    formLoading.value = false
  }
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    formData.value.describeVal = taskRef.value.states.selectedLabel
    formData.value.userId = user.id
    formData.value.userName = user.nickname
    const data = cloneDeep(formData.value)
    if (flag.value === 'add') delete data.id
    await TaskApi.updateTaskPlan(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  taskList.value = []
  formData.value = {
    id: undefined,
    projectId: undefined,
    customerName: undefined,
    describe: undefined,
    userId: undefined,
    userName: undefined,
    taskType: undefined,
    plannedDuration: undefined,
    actualStartDate: undefined,
    actualEndDate: undefined,
    completeBase: undefined,
    specifyBasis: undefined,
    planContent: undefined,
    remark: undefined,
    mustBeCompletedDate: undefined,
    taskId: undefined,
    consumeDate: undefined
  }
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
