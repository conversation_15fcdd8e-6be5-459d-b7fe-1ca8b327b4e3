<template>
  <el-dialog title="工作记录详情" v-model="dialogVisible" width="1240px" append-to-body center>
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading" size="24">
        <Loading />
      </el-icon>
      <p>正在加载数据...</p>
    </div>
    <div v-else>
      <!-- 日志记录 -->
      <div v-if="allData.type1 && allData.type1.length > 0">
        <el-divider>📝 日志记录</el-divider>
        <el-table :data="allData.type1" border stripe style="width: 100%">
          <el-table-column prop="projectName" label="所属项目" min-width="120" />
          <el-table-column prop="customerName" label="客户名" min-width="120" />
          <el-table-column prop="taskName" label="所属任务" min-width="120" />
          <el-table-column label="日期" min-width="100">
            <template #default="{ row }">
              {{ dayjs(row.consumeDate).format('YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="当前工时" min-width="100">
            <template #default="{ row }"> {{ row.consumeHour || 0 }}小时 </template>
          </el-table-column>
          <el-table-column label="当前用户已用工时" min-width="120">
            <template #default="{ row }"> {{ row.consumeUserHour || 0 }}小时 </template>
          </el-table-column>
          <el-table-column label="已用总工时" min-width="100">
            <template #default="{ row }"> {{ row.consumeTotalHour || 0 }}小时 </template>
          </el-table-column>
          <el-table-column label="总工时" min-width="100">
            <template #default="{ row }"> {{ row.totalHour || 0 }}小时 </template>
          </el-table-column>
          <el-table-column prop="remark" label="工作内容" min-width="150" show-overflow-tooltip />
          <el-table-column label="文件" min-width="100">
            <template #default="{ row }">
              <FileListPreview
                v-if="row.fileUrls && row.fileUrls.length > 0"
                :fileUrl="row.fileUrls"
              />
            </template>
          </el-table-column>
          <el-table-column label="成果" min-width="100">
            <template #default="{ row }">
              <FileListPreview
                v-if="row.resultUrls && row.resultUrls.length > 0"
                :fileUrl="row.resultUrls"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 费用报销详情 -->
      <div v-if="allData.type2 && allData.type2.length > 0">
        <el-divider>💰 费用报销详情</el-divider>
        <el-table :data="allData.type2" border stripe style="width: 100%">
          <el-table-column label="所属项目" min-width="120">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.projectName }}
            </template>
          </el-table-column>
          <el-table-column label="申请单编号" min-width="120">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.applyCode }}
            </template>
          </el-table-column>
          <el-table-column label="申请日期" min-width="100">
            <template #default="{ row }">
              {{ dayjs(row.applyExcelRespVO?.applyDate).format('YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="发生日期" min-width="100">
            <template #default="{ row }">
              {{ dayjs(row.applyExcelRespVO?.consumeDate).format('YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="出发地" min-width="120">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.startAddr }}
            </template>
          </el-table-column>
          <el-table-column label="目的地" min-width="120">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.endAddr }}
            </template>
          </el-table-column>
          <el-table-column label="公里数" min-width="80">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.distance }}
            </template>
          </el-table-column>
          <el-table-column label="事由" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.reason }}
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="100">
            <template #default="{ row }">
              {{ row.applyExcelRespVO?.price }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 打卡记录 -->
      <div v-if="allData.type3 && allData.type3.length > 0">
        <el-divider>📅 打卡记录</el-divider>
        <el-table :data="allData.type3" border stripe style="width: 100%">
          <el-table-column label="客户名" min-width="120">
            <template #default="{ row }">
              {{ row.punchRecordsDO?.customerName }}
            </template>
          </el-table-column>
          <el-table-column label="客户地址" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.punchRecordsDO?.destination }}
            </template>
          </el-table-column>
          <el-table-column label="打卡地址" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.punchRecordsDO?.punchLocation }}
            </template>
          </el-table-column>
          <el-table-column label="打卡时间" min-width="150">
            <template #default="{ row }">
              {{ dayjs(row.punchRecordsDO?.punchDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column label="偏差" min-width="100">
            <template #default="{ row }">
              {{ row.punchRecordsDO?.distance ? formatDistance(row.punchRecordsDO.distance) : '' }}
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.punchRecordsDO?.remark }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 查看地图按钮 -->
        <div style="margin-top: 15px; text-align: center">
          <el-button
            type="primary"
            :icon="Location"
            @click="viewAllPunchLocations"
            :disabled="!hasValidPunchData"
          >
            查看轨迹回放
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 腾讯地图组件 -->
  <TencentMap ref="tencentMapRef" />
</template>

<script setup>
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { ProjectTaskManhourApi } from '@/api/pms/taskHour'
import { Loading, Location } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'

const { t } = useI18n()
const { proxy } = getCurrentInstance()

const message = useMessage()
const dialogVisible = ref(false)
const loading = ref(false)
const allData = ref({
  type1: [], // 日志记录
  type2: [], // 费用报销
  type3: [] // 打卡记录
})

// 腾讯地图组件引用
const tencentMapRef = ref()

// 格式化距离显示
const formatDistance = (distance) => {
  if (!distance) return ''
  if (distance < 1000) {
    return `${distance}米`
  } else {
    return `${(distance / 1000).toFixed(2)}公里`
  }
}

//** 弹框打开事件 */
const openDialog = async (data) => {
  loading.value = true
  dialogVisible.value = true

  try {
    // 处理 type1 数据，通过 id 获取详细信息
    if (data.type1 && data.type1.length > 0) {
      const detailedType1 = []
      for (const item of data.type1) {
        try {
          const res = await ProjectTaskManhourApi.getProjectTaskManhour(item.id)
          detailedType1.push(res)
        } catch (error) {
          console.error('获取日志详情失败:', error)
          // 如果获取失败，使用原始数据
          detailedType1.push(item)
        }
      }
      data.type1 = detailedType1
    }

    allData.value = data
  } catch (error) {
    console.error('处理数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 检查是否有有效的打卡数据
const hasValidPunchData = computed(() => {
  return (
    allData.value.type3 &&
    allData.value.type3.length > 0 &&
    allData.value.type3.some(
      (item) => item.punchRecordsDO?.latitude && item.punchRecordsDO?.longitude
    )
  )
})

// 查看轨迹回放
const viewAllPunchLocations = () => {
  if (!hasValidPunchData.value) {
    message.warning('没有有效的打卡位置数据')
    return
  }

  // 提取所有有效的打卡位置数据
  const locations = allData.value.type3
    .filter((item) => item.punchRecordsDO?.latitude && item.punchRecordsDO?.longitude)
    .map((item) => ({
      customerName: item.punchRecordsDO.customerName,
      punchLocation: item.punchRecordsDO.punchLocation,
      punchTime: dayjs(item.punchRecordsDO.punchDate).format('YYYY-MM-DD HH:mm:ss'),
      latitude: item.punchRecordsDO.latitude,
      longitude: item.punchRecordsDO.longitude
    }))

  // 打开腾讯地图弹窗
  tencentMapRef.value?.openDialog(locations)
}

// 关闭弹框并重置操作
const close = () => {
  dialogVisible.value = false
  loading.value = false
  allData.value = {
    type1: [],
    type2: [],
    type3: []
  }
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  p {
    margin-top: 16px;
    color: #666;
  }

  .is-loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

:deep(.el-upload-list) {
  width: 200px;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}
</style>
