<!--
 * @Description: 费用报销详情/打卡记录弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-18 08:31:29
 * @LastEditTime: 2024-10-15 17:03:21
-->
<template>
  <el-dialog
    :title="type === 2 ? '费用报销详情' : '打卡记录'"
    v-model="dialogVisible"
    width="600px"
    append-to-body
    center
  >
    <div class="content">
      <div v-if="type === 2" class="list" v-for="item in moreList">
        所属项目：{{ item.applyExcelRespVO?.projectName }}
        <br />
        申请单编号：{{ item.applyExcelRespVO?.applyCode }}
        <br />
        申请日期：{{ dayjs(item.applyExcelRespVO?.applyDate).format('YYYY-MM-DD') }}
        <br />
        发生日期：{{ dayjs(item.applyExcelRespVO?.consumeDate).format('YYYY-MM-DD') }}
        <br />
        出发地：{{ item.applyExcelRespVO?.startAddr }}
        <br />
        目的地：{{ item.applyExcelRespVO?.endAddr }}
        <br />
        公里数：{{ item.applyExcelRespVO?.distance }}
        <br />
        事由：{{ item.applyExcelRespVO?.reason }}
        <br />
        金额：{{ item.applyExcelRespVO?.price }}
        <el-divider />
      </div>
      <div class="text-16px" v-else v-for="item in moreList">
        客户名：{{ item.punchRecordsDO?.customerName }}
        <br />
        客户地址：{{ item.punchRecordsDO?.destination }}
        <br />
        打卡地址：{{ item.punchRecordsDO?.punchLocation }}
        <br />
        打卡时间：{{ dayjs(item.punchRecordsDO?.punchDate).format('YYYY-MM-DD HH:mm:ss') }}
        <br />
        偏差：{{ item.punchRecordsDO?.distance && formatDistance(item.punchRecordsDO.distance) }}
        <br />
        备注：{{ item.punchRecordsDO?.remark }}     
        <el-divider />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
  <ReimbursementDialog ref="reimbursementDialogRef" />
</template>

<script setup>
import ReimbursementDialog from './reimbursementDialog.vue'
import dayjs from 'dayjs'
import { formatDistance } from '@/utils'

const reimbursementDialogRef = ref()
const dialogVisible = ref(false)
const moreList = ref([])
const type = ref()
//** 弹框打开事件 */
const openDialog = async (flag, item) => {
  type.value = flag
  dialogVisible.value = true
  moreList.value = item
}

const openReimbursementDialog = (item) => {
  reimbursementDialogRef.value.openDialog(item.id)
}
// 关闭弹框并重置操作
const close = () => {
  dialogVisible.value = false
}

defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
:deep(.el-table .row-expand-cover .cell .el-table__expand-icon) {
  display: none;
}

:deep(.el-divider--horizontal) {
  margin-bottom: 5px;
}

.content {
  border-top: 1px solid #dddddd;
  margin-top: -10px;
  padding-top: 20px;
}
.list {
  padding: 10px;
  font-size: 16px;
  /* color: #3594c5; */
  /* cursor: pointer; */
}
</style>
