<!--
 * @Description: 员工工时日历
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2024-06-25 11:31:05
 * @LastEditTime: 2025-07-07 10:40:08
-->

<template>
  <ContentWrap class="position-relative">
    <el-tree-select
      v-if="onlySeeSelf"
      class="top-8px left-15px !w-200px"
      v-model="selectedDeptId"
      :data="deptList"
      default-expand-all
      :props="defaultProps"
      check-strictly
      node-key="id"
      placeholder="请选择归属部门"
      :disabled="roles.includes('project_manager')"
      @change="changeDept"
    />
    <el-select
      v-if="onlySeeSelf"
      class="!position-absolute top-8px left-230px !w-200px"
      filterable
      v-model="selectedUserId"
      placeholder="请先选择用户"
      value-key="id"
      lable-key="nickname"
      @change="getList"
    >
      <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
    </el-select>
    <el-button
      class="!position-absolute top-8px left-450px"
      type="primary"
      @click="handleCommunicate"
    >
      沟 通
    </el-button>

    <FullCalendar :options="calendarOptions" style="width: 100%" ref="fullCalendarRef" />
    <!-- <DailyLogDetailDialog ref="dailyLogDetailDialogRef" />
    <MoreDialog ref="moreDialogRef" /> -->
    <WebIMDialog ref="webIMDialogRef" />
    <AddPlanDialog ref="addPlanDialogRef" @success="getList" />
    <AllInfoDialog ref="allInfoDialogRef" />
  </ContentWrap>
</template>

<script setup lang="ts">
import AddPlanDialog from './components/addPlanDialog.vue'
import MoreDialog from './components/moreDialog.vue'
import AllInfoDialog from './components/allInfoDialog.vue'
import { getWorkCalendarData } from '@/api/statistics/workCalendar'
import dayjs from 'dayjs'
import FullCalendar from '@fullcalendar/vue3'
import locale from '@fullcalendar/core/locales/zh-cn'
import multiMonthPlugin from '@fullcalendar/multimonth'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import * as UserApi from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'
import { isTodayOrLater } from '@/utils'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

defineOptions({ name: 'WorkCalendar' })
const userStore = useUserStore()
const userId = userStore.getUser.id // 当前登录的编号
const user = userStore.getUser

const roles = userStore.getRoles // 当前登录人角色
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const deptList = ref<Tree[]>([]) // 树形结构

const selectedUserId = ref<any>(null)
const selectedDeptId = ref<any>(roles.includes('project_manager') ? user.deptId : null)
// 只看自己的日历的角色
const onlySeeSelf = computed(() => {
  // return roles.includes('sales') || roles.includes('dmt1') || roles.includes('oa')
  return (
    roles.includes('super_admin') ||
    roles.includes('project_manager') ||
    roles.includes('pms') ||
    roles.includes('personnel_manager') ||
    roles.includes('dmt')
  )
})

const startDate = ref('')
const endDate = ref('')
const fullCalendarRef = ref()
const dailyLogDetailDialogRef = ref()
const addPlanDialogRef = ref()
const moreDialogRef = ref()
const allInfoDialogRef = ref()
const logList = ref<any>([])
const applyList = ref<any>([])
const clockList = ref<any>([])
const planList = ref<any>([])
const isAlreadyEnter = ref(false)
const TYPE_MAP = {
  // 日志
  1: {
    backgroundColor: '#3788d8',
    borderColor: '#3788d8'
  },
  // 报销申请
  2: {
    backgroundColor: '#f97316',
    borderColor: '#f97316'
  },
  // 打卡
  3: {
    backgroundColor: '#84d97a',
    borderColor: '#84d97a'
  },
  // 计划
  4: {
    backgroundColor: '#fff',
    borderColor: '#fff',
    textColor: '#f97316'
  }
}

const changeDept = async (value) => {
  userList.value = await UserApi.getSimpleUserList({ deptId: value })
}

const calendarOptions = ref({
  plugins: [multiMonthPlugin, dayGridPlugin, timeGridPlugin], // 需要加载的插件
  initialView: 'dayGridMonth', // 初始视图
  multiMonthMaxColumns: 3, // 多月展示时最多几个月一行
  height: 'auto',
  locale, // 语言汉化
  selectable: true,
  editable: true,
  dayMaxEventRows: 10, // 每天最多显示10条事件
  fixedWeekCount: false, // 因为每月起始日是星期几不固定，导致一个月的行数会不固定，是否固定行数
  showNonCurrentDates: false, // 隐藏非当前月份的日期
  handleWindowResize: true,
  windowResizeDelay: 100,
  aspectRatio: 2, // 宽高比
  // 日历上方的按钮和title
  headerToolbar: {
    left: '',
    center: 'title',
    // right: 'multiMonthYear,dayGridMonth,timeGridWeek,timeGridDay'
    right: 'prevYear,prev,next,nextYear today'
  },
  // 自定义事件排序，确保计划(type=4)总是显示在最上方
  eventOrder: function (a, b) {
    // 如果a是计划(type=4)，它应该排在前面（返回-1）
    if (a.extendedProps?.type === 4 && b.extendedProps?.type !== 4) {
      return -1
    }
    // 如果b是计划(type=4)，它应该排在前面（返回1）
    if (a.extendedProps?.type !== 4 && b.extendedProps?.type === 4) {
      return 1
    }
    // 如果两者类型相同，保持原有顺序
    return 0
  },
  //多数据源展示
  eventSources: [logList.value, planList.value],
  // 事件的点击
  eventClick: (info) => {
    const {
      event: { extendedProps, id, startStr }
    } = info
    const { type, allData } = extendedProps
    if (!type) return
    if (type === 1 || type === 2 || type === 3) {
      // 打开综合信息对话框，传递所有数据
      allInfoDialogRef.value.openDialog(allData)
    } else if (type === 4) {
      openAddPlanDialog('edit', startStr, id)
    }
  },
  datesSet: (info) => {
    startDate.value = dayjs(info.start).format('YYYY-MM-DD')
    endDate.value = dayjs(info.end).format('YYYY-MM-DD')
    getList()
  }
})

const reset = () => {
  logList.value = []
  planList.value = []
}

/** 查询列表 */
const getList = async () => {
  // 销售角色只能查询自己的数据
  // if (onlySeeSelf.value && !selectedUserId.value) return
  try {
    const queryData = {
      userId: !selectedUserId.value ? userId : selectedUserId.value,
      startDate: startDate.value,
      endDate: endDate.value
    }
    const data = await getWorkCalendarData(queryData)
    reset()

    // 按日期分组数据
    const dateGroups = {}

    data.forEach((item) => {
      const date = item.start || item.startStr
      if (!dateGroups[date]) {
        dateGroups[date] = {
          type1: [], // 日志
          type2: [], // 报销申请
          type3: [], // 打卡记录
          type4: [] // 计划
        }
      }

      if (item.type === 1 || item.type === 2 || item.type === 3) {
        // type为1、2、3的数据按类型分组
        dateGroups[date][`type${item.type}`].push(item)
      } else if (item.type === 4) {
        // type为4的数据直接添加到planList
        planList.value.push({
          ...item,
          ...TYPE_MAP[item.type],
          allDay: true,
          extendedProps: {
            type: item.type
          }
        })
      }
    })

    // 为每个日期创建一条合并的记录
    Object.keys(dateGroups).forEach((date) => {
      const group = dateGroups[date]
      const hasData = group.type1.length > 0 || group.type2.length > 0 || group.type3.length > 0

      if (hasData) {
        // 合并所有数据
        const allData = {
          type1: group.type1,
          type2: group.type2,
          type3: group.type3
        }

        // 计算总数
        const totalCount = group.type1.length + group.type2.length + group.type3.length

        // 构建标题
        let title = ''
        const icons = []

        if (group.type2.length > 0) {
          icons.push('💰')
        }
        if (group.type3.length > 0) {
          icons.push('📅')
        }

        // 拼接type1的title字段
        const type1Titles = group.type1.map((item) => item.title).filter(Boolean)
        if (type1Titles.length > 0) {
          title = `${icons.join('')} ${type1Titles.join('、')}`
        } else {
          title = `${icons.join('')} 工作记录 (${totalCount})`
        }

        // 创建合并的事件
        logList.value.push({
          id: `merged-${date}`,
          title: title,
          start: date,
          allDay: true,
          backgroundColor: '#3788d8',
          borderColor: '#3788d8',
          extendedProps: {
            type: 1, // 使用type1作为标识
            allData: allData
          }
        })
      }
    })
    // 组装数据，重新渲染
    calendarOptions.value.eventSources = [logList.value, planList.value]

    nextTick(() => {
      fullCalendarRef.value.getApi().render()
      addCustomTrigger()
    })
  } finally {
    isAlreadyEnter.value = true
  }
}

const addCustomTrigger = () => {
  document.querySelectorAll('.fc-daygrid-day-frame').forEach((element: any) => {
    const date = element.parentNode?.dataset?.date
    // 判断是否是今天及之后的日期
    if (!isTodayOrLater(date)) return
    const trigger = document.createElement('div')
    trigger.style.position = 'absolute'
    trigger.style.top = '5px'
    trigger.style.left = '5px'
    trigger.style.height = '20px'
    trigger.style.cursor = 'pointer'
    trigger.innerHTML = '➕'

    trigger.addEventListener('click', (e) => {
      openAddPlanDialog('add', date)
    })

    element.appendChild(trigger)
  })
}

const openAddPlanDialog = (type, date, id?) => {
  addPlanDialogRef.value.open(type, date, id)
}

// 将日期相同的合并并累加
const mergeItems = (itemList, icon) => {
  const itemStore = {}
  itemList.value.forEach((item) => {
    if (!itemStore[item.start]) {
      itemStore[item.start] = {
        ...item,
        title: `${icon}(1)`,
        children: [item]
      }
    } else if (itemStore[item.start].type === item.type) {
      const count = parseInt(itemStore[item.start].title.match(/\d+/)[0], 10)
      itemStore[item.start].title = `${icon}(${count + 1})`
      itemStore[item.start].children.push(item)
    }
  })
  itemList.value = Object.values(itemStore)
}

const webIMDialogRef = ref()
const handleCommunicate = () => {
  webIMDialogRef.value.loadIM()
}
/** 初始化 **/
onMounted(async () => {
  if (onlySeeSelf.value) {
    deptList.value = handleTree(await DeptApi.getSimpleDeptList())

    // 如果是 project_manager，默认选中当前用户的部门，并触发 changeDept
    if (roles.includes('project_manager')) {
      selectedDeptId.value = user.deptId
      await changeDept(user.deptId) // 手动触发 changeDept 加载用户列表
      // selectedUserId.value = userId  // 默认选中当前用户
    } else {
      // 其他角色，正常加载用户列表
      userList.value = await UserApi.getSimpleUserList()
    }
  }
  getList()
})

onActivated(async () => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-card__body > div {
    position: relative;
  }
}
</style>
