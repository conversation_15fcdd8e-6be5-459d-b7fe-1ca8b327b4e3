<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-08 10:02:47
 * @LastEditTime: 2024-11-08 17:15:53
-->
<template>
  <ContentWrap>
    <el-button v-if="showBack" type="primary" style="margin-bottom: 10px" @click="goBack">
      <el-icon><Back /></el-icon> &nbsp;返 回
    </el-button>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="日期">
        {{ formatDate(detailData.consumeDate, 'YYYY-MM-DD') }}
      </el-descriptions-item>
      <el-descriptions-item label="所属项目">
        {{ detailData.projectName }}
        <span class="text-#f40 ml-200px" v-if="detailData?.newProjectName">
          变更后：{{ detailData.newProjectName }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="客户名称">
        {{ detailData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="所属任务">
        {{ detailData.describeVal }}
        <span class="text-#f40 ml-200px" v-if="detailData?.newDescribeVal">
          变更后：{{ detailData.newDescribeVal }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="负责人">
        {{ detailData.changedBy }}
      </el-descriptions-item>
      <el-descriptions-item label="计划工作内容">
        {{ detailData.planContent }}
        <span class="text-#f40 ml-200px" v-if="detailData?.newPlanContent">
          变更后：{{ detailData.newPlanContent }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ detailData.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="变更记录">
        <el-table :data="detailData.taskChangeLogList">
          <el-table-column type="index" label="序号" width="70"></el-table-column>
          <el-table-column prop="changeType" label="变更类型"></el-table-column>
          <el-table-column prop="oldValue" label="变更前"></el-table-column>
          <el-table-column prop="newValue" label="变更后"></el-table-column>
          <el-table-column
            prop="changeTime"
            label="变更时间"
            :formatter="dateFormatter"
            width="180"
          ></el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import { formatDate } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { TaskApi } from '@/api/project/projectTask'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'PlanDetail' })

const changeTypeMap = {
  项目变更: 'newProjectName',
  任务变更: 'newDescribeVal',
  计划目标内容变更: 'newPlanContent'
}

const { query } = useRoute() // 查询参数
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter()
const props = defineProps({
  id: propTypes.number.def(undefined),
  showBack: propTypes.bool.def(true)
})
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<any>({}) // 详情数据
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号

/** 获得数据 */
const getInfo = async () => {
  detailLoading.value = true
  try {
    detailData.value = await TaskApi.getTaskPlan(props.id || queryId)
    if (detailData.value.thisRevisionRecordList?.length) {
      for (const element of detailData.value.thisRevisionRecordList) {
        const changeTypeField = changeTypeMap[element?.changeType]
        changeTypeField && (detailData.value[changeTypeField] = element.newValue)
      }
    }
  } finally {
    detailLoading.value = false
  }
}

const goBack = () => {
  // 关闭当前 Tab
  delView(unref(currentRoute))
  push({
    path: '/statistics/workCalendar',
    query: { ...query, id: undefined }
  })
}

defineExpose({ open: getInfo }) // 提供 open 方法，用于打开弹窗

/** 初始化 **/
onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped></style>
