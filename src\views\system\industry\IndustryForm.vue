<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="行业名字" prop="industryName">
        <el-input v-model="formData.industryName" placeholder="请输入行业名字" />
      </el-form-item>
      <el-form-item label="行业编码" prop="industryCode">
        <el-input v-model="formData.industryCode" placeholder="请输入行业编码" />
      </el-form-item>
      <el-form-item label="父级编号" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="industryTree"
          :props="{...defaultProps, label: 'industryName'}"
          check-strictly
          default-expand-all
          placeholder="请选择父级编号"
        />
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入显示顺序" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IndustryApi, IndustryVO } from '@/api/system/industry'
import { defaultProps, handleTree } from '@/utils/tree'

/** 行业分类 表单 */
defineOptions({ name: 'IndustryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  industryName: undefined,
  industryCode: undefined,
  parentId: undefined,
  sort: undefined
})
const formRules = reactive({
  industryName: [{ required: true, message: '行业名字不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '显示顺序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const industryTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await IndustryApi.getIndustry(id)
    } finally {
      formLoading.value = false
    }
  }
  await getIndustryTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IndustryVO
    if (formType.value === 'create') {
      await IndustryApi.createIndustry(data)
      message.success(t('common.createSuccess'))
    } else {
      await IndustryApi.updateIndustry(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    industryName: undefined,
    industryCode: undefined,
    parentId: undefined,
    sort: undefined
  }
  formRef.value?.resetFields()
}

/** 获得行业分类树 */
const getIndustryTree = async () => {
  industryTree.value = []
  const data = await IndustryApi.getIndustryList()
  const root: Tree = { id: 0, name: '顶级行业分类', children: [] }
  root.children = handleTree(data, 'id', 'parentId')
  industryTree.value.push(root)
}
</script>